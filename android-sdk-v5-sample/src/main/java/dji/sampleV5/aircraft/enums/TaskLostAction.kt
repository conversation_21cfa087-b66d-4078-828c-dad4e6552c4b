package dji.sampleV5.aircraft.enums

import dji.sampleV5.aircraft.enums.FinishAction.UNKNOWN


/**
 * Copyright (C), 2015-2025
 * FileName: TaskLostAction
 * Author: 80945
 * Date: 2025/1/23 9:51
 * Description:
 */
enum class TaskLostAction(val code: Int, val value: String) {
    HOVER(0, "hover"),
    LANDING(1, "landing"),
    GO_BACK(2, "goBack"),
    GO_CONTINUE(3, "goContinue"),
    UNKNOWN(-1, "goBack");
    companion object {
        fun getByCode(code: Int): TaskLostAction {
            return TaskLostAction.values().find { it.code == code } ?: UNKNOWN
        }
        fun getValue(value: String): TaskLostAction {
            return TaskLostAction.values().find { it.value == value } ?: UNKNOWN
        }
    }
}