package dji.sampleV5.aircraft.enums

/**
 * Copyright (C), 2015-2024
 * FileName: FinishActionType
 * Author: 80945
 * Date: 2024/12/30 9:57
 * Description:完成动作
 */
enum class FinishActionType(val code: Int, val value: String) {
    //自动返航
    AUTORETURN(1, "自动返航"),
    //终点站
    ENDPOINT(4, "终点站"),
    //未知
    UNKONW(-1, "未知");

    companion object {
        fun getByCode(code: Int): FinishActionType {
            return FinishActionType.values().find { it.code == code } ?: UNKONW
        }
    }
}