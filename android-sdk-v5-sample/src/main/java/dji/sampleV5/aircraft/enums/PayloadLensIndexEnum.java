package dji.sampleV5.aircraft.enums;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wang<PERSON><PERSON>
 * @date: 2024/8/12 11:10
 * @description: 媒体文件存储镜头类型
 */
public enum PayloadLensIndexEnum {

    /**
     * 广角镜头
     */
    WIDE(2, "wide"),

    /**
     * 变焦镜头
     */
    ZOOM(3, "zoom"),

    /**
     * 红外镜头
     */
    THRM(4, "ir"),

    /**
     * 可见光照片，二代库广角
     */
    VISABLE(5, "visable");

    private int code;
    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    PayloadLensIndexEnum(int code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (PayloadLensIndexEnum lensIndexEnum : PayloadLensIndexEnum.values()) {
            if (lensIndexEnum.getCode() == code) {
                return lensIndexEnum.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据传入的媒体文件存储镜头类型code值，获得对应的媒体文件存储镜头类型String值，用，隔开
     */
    public static String getPayloadLensIndexs(List<Integer> codes) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < codes.size(); i++) {
            sb.append(getDesc(codes.get(i)));
            if (i < codes.size() - 1) {
                sb.append(", ");
            }
        }

        return sb.toString();
    }
}
