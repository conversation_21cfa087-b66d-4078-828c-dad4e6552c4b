package dji.sampleV5.aircraft;

import static com.alivc.live.pusher.AlivcPreviewOrientationEnum.ORIENTATION_LANDSCAPE_HOME_RIGHT;
import static java.lang.Math.abs;
import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.bytesToMegabytes;
import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.readJsonFromAssets;
import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.toStartActivity;

import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.provider.Settings;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;

import com.alivc.live.annotations.AlivcLiveMode;
import com.alivc.live.baselive_push.ui.LivePushActivity;
import com.alivc.live.commonbiz.SharedPreferenceUtils;
import com.alivc.live.commonui.configview.LivePushSettingView;
import com.alivc.live.commonutils.FastClickUtil;
import com.alivc.live.pusher.AlivcLivePushCameraTypeEnum;
import com.alivc.live.pusher.AlivcLivePushConfig;
import com.alivc.live.pusher.AlivcPreviewDisplayMode;
import com.alivc.live.pusher.AlivcPreviewOrientationEnum;
import com.alivc.live.pusher.AlivcResolutionEnum;
import com.alivc.live.pusher.WaterMarkInfo;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.offlinemap.OfflineMapActivity;
import com.google.android.material.navigation.NavigationView;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import dji.sampleV5.aircraft.common.json.TypeBuilder;
import dji.sampleV5.aircraft.data.database.CameraSettingTable;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityHomeBinding;
import dji.sampleV5.aircraft.lbs.LocationService;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.mqtt.MQttManager;
import dji.sampleV5.aircraft.mvvm.base.BaseActivity;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.DialogExtKt;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity;
import dji.sampleV5.aircraft.mvvm.net.bean.CameraInfoBean;
import dji.sampleV5.aircraft.mvvm.net.bean.DeviceModelBean;
import dji.sampleV5.aircraft.mvvm.net.bean.SiteListBean;
import dji.sampleV5.aircraft.mvvm.net.request.RegisterSiteRequest;
import dji.sampleV5.aircraft.mvvm.net.request.RegisterUavRequest;
import dji.sampleV5.aircraft.mvvm.net.request.SiteLocationRequest;
import dji.sampleV5.aircraft.mvvm.ui.activity.config.ConfigManagerActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.history.HistoryListActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.log.LogManagerActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.mission.MissionListActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.multisite.MultisiteActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.site.RegisterStationActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.report.HistoryReportActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.warn.HistoryWarnActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.waypoint.NewMissionActivity;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.HomeViewModel;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.WayPointViewModel;
import dji.sampleV5.aircraft.mvvm.update.manager.DownloadManager;
import dji.sampleV5.aircraft.mvvm.util.AMapLocationUtil;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.mvvm.widget.popup.SiteListPopup;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.FindAircraftActivity;
import dji.sampleV5.aircraft.page.RecentMissionDetailActivity;
import dji.sampleV5.aircraft.page.SiteListActivity;
import dji.sampleV5.aircraft.page.ai.AiReportActivity;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.page.login.LoginMixActivity;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.key.ProductKey;
import dji.sdk.keyvalue.utils.ProductUtil;
import dji.sdk.keyvalue.value.camera.CameraStorageInfo;
import dji.sdk.keyvalue.value.camera.CameraStorageLocation;
import dji.sdk.keyvalue.value.camera.CameraType;
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType;
import dji.sdk.keyvalue.value.camera.SDCardLoadState;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.utils.GeoidManager;
import dji.v5.manager.KeyManager;
import dji.v5.manager.SDKManager;
import dji.v5.manager.account.LoginInfo;
import dji.v5.manager.account.UserAccountManager;
import dji.v5.utils.common.JsonUtil;
import dji.v5.ux.core.communication.DefaultGlobalPreferences;
import dji.v5.ux.core.communication.GlobalPreferencesManager;
import dji.v5.ux.core.util.UxSharedPreferencesUtil;
import kotlin.Unit;
import me.jessyan.autosize.internal.CancelAdapt;

public class HomeActivity extends BaseActivity<HomeViewModel, ActivityHomeBinding> implements View.OnClickListener, NavigationView.OnNavigationItemSelectedListener, CancelAdapt {
    //    private HomeActivityBinding binding;
    private final AtomicBoolean isInited = new AtomicBoolean(false);
    private static final int REQUEST_CODE = 0x0010;
    //private FlyStatusController flyStatusController;
    private AtomicBoolean isProductListenerRegistered = new AtomicBoolean(false);
    private boolean isConnect = false;
    private final String TAG = this.getClass().getName();

    //存储ProductType与其对应的资源和文本
    private static final Map<ProductType, Pair<Integer, DeviceModelBean>> PRODUCT_INFO_MAP = new HashMap<>();

    static {
        PRODUCT_INFO_MAP.put(ProductType.M30_SERIES, new Pair<>(R.drawable.m30, new DeviceModelBean("经纬 M30T", "DJI_M30_T")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES, new Pair<>(R.drawable.mavic3, new DeviceModelBean("御 3 行业进阶版", "DJI_MAVICE3_T")));
        PRODUCT_INFO_MAP.put(ProductType.M300_RTK, new Pair<>(R.drawable.aircraft_m300, new DeviceModelBean("经纬 M300 RTK", "DJI_M300_RTK")));
        PRODUCT_INFO_MAP.put(ProductType.M350_RTK, new Pair<>(R.drawable.aircraft_m300, new DeviceModelBean("经纬 M350 RTK", "DJI_M350_RTK")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MINI_3_PRO, new Pair<>(R.drawable.minipro3, new DeviceModelBean("Mini 3 Pro ", "DJ mini pro 3")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MINI_3, new Pair<>(R.drawable.minipro3, new DeviceModelBean("Mini 3", "DJ mini pro 3")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_4_SERIES, new Pair<>(R.drawable.aircraft_matrice4t, new DeviceModelBean("Matrice 4T", "Matrice4T")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_4D_SERIES, new Pair<>(R.drawable.aircraft_matrice4td, new DeviceModelBean("Matrice 4TD", "Matrice4TD")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_400, new Pair<>(R.drawable.ic_dji_matrice400, new DeviceModelBean("Matrice 400", "DJI_M400")));
    }

    private String uaName = "";
    private DownloadManager manager;
    private WayPointViewModel wayPointViewModel;
    private ArrayList<SiteListBean.SiteItem> siteItemList;
    private LivePushSettingView mLivePushSettingView;
    private AlivcResolutionEnum mCurrentResolution = AlivcResolutionEnum.RESOLUTION_540P;
    private AlivcResolutionEnum mDefinition = AlivcResolutionEnum.RESOLUTION_540P;
    private AlivcLivePushConfig mAlivcLivePushConfig;
    private boolean mAsyncValue = true;
    private boolean mAudioOnlyPush = false;
    private boolean mVideoOnlyPush = false;
    private AlivcPreviewOrientationEnum mOrientationEnum = ORIENTATION_LANDSCAPE_HOME_RIGHT;
    private int mCameraId = AlivcLivePushCameraTypeEnum.CAMERA_TYPE_BACK.getCameraId();
    private boolean isFlash = false;
    private ArrayList<WaterMarkInfo> mWaterMarkInfos = new ArrayList<>();
    private String pushUrl;
    private String mAuthTimeStr = "";
    private String mPrivacyKeyStr = "";
    private boolean mMixStream = false;
    private int mFpsConfig = 25;//默认帧率
    private LocationCoordinate2D uavLocation;
    private ArrayList<String> permissionList;

    {
        permissionList = new ArrayList<String>();
        permissionList.add(Permission.ACCESS_COARSE_LOCATION);
        permissionList.add(Permission.ACCESS_FINE_LOCATION);
        permissionList.add(Permission.READ_PHONE_STATE);
        permissionList.add(Permission.RECORD_AUDIO);
        permissionList.add(Permission.MANAGE_EXTERNAL_STORAGE);
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        check();
        View view = mBinding.navView.getHeaderView(0);
        ConstraintLayout constraintLayout = view.findViewById(R.id.combo_layout);
        constraintLayout.setOnClickListener(this);
        wayPointViewModel = new ViewModelProvider(this).get(WayPointViewModel.class);
        addLoadingUiChange(wayPointViewModel);
        mBinding.fly.setOnClickListener(this);
        mBinding.btnMultimedia.setOnClickListener(this);
        mBinding.btnControlCenter.setOnClickListener(this);
        mBinding.btnSingleLive.setOnClickListener(this);
        mBinding.btnTaskManager.setOnClickListener(this);
        mBinding.btnWayLine.setOnClickListener(this);
        mBinding.imgMenu.setOnClickListener(this);
        mBinding.navView.setNavigationItemSelectedListener(this);
        mBinding.drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        if (!TextUtils.equals(Util.getAppName(getApplicationContext()), SpUtil.getAPPName())) {
            startActivity(new Intent(this, LoginMixActivity.class));
            finish();
            return;
        }

        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache == null) {
            startActivity(new Intent(this, LoginMixActivity.class));
            finish();
            return;
        } else {
            long lastTime = loginCache.getTime();
            Log.e(TAG, "onResume: " + (System.currentTimeMillis() - lastTime) / 1000 / 60 / 60 / 24);
            if ((System.currentTimeMillis() - lastTime) / 1000 / 60 / 60 / 24 > 7) {
                // 使用新的方法处理登录过期
                checkLoginAndMQStatus();
                return;
            } else {
                checkPermissionAndRequest();
                // 登录有效，检查并恢复MQ连接
                checkAndRestoreMQConnection();
            }

            View headView = mBinding.navView.getHeaderView(0);
            AlphabetCircleImageView ivAvatar = headView.findViewById(R.id.iv_nav_avatar);
            ivAvatar.setAlphabet(loginCache.getId());
            TextView tvUserName = headView.findViewById(R.id.tv_nav_name);
            tvUserName.setText(loginCache.getId());
            TextView tvOrgName = headView.findViewById(R.id.tv_org_name);
            tvOrgName.setVisibility(View.INVISIBLE);
        }

        UxSharedPreferencesUtil.initialize(this);
        GlobalPreferencesManager.initialize(new DefaultGlobalPreferences(this));
        GeoidManager.getInstance().init(this);
        initAliLivePush();
        LiveDataEvent.INSTANCE.isUavRegister().observe(this, aBoolean -> {
            if (!aBoolean) {
                DialogExtKt.showActivityDialogMessage(this, "该无人机尚未注册站点，是否立即注册？", "提示", "确定", () -> {
                            // 启动新的Compose注册流程
                            startStationRegistration();
                            return Unit.INSTANCE;
                        }, "取消", () -> Unit.INSTANCE, true
                );
            }
        });
        //加载默认相机配置数据
        loadCameraInfoToAsset();
        LiveDataEvent.INSTANCE.isSdkInit().observeForever(isInit ->{
            productListener();
        });
        // 立即检查一次SDK状态
        if (SDKManager.getInstance().isRegistered()) {
            productListener();
        }
    }

    private void productListener() {
        if (isProductListenerRegistered.getAndSet(true)) {
            return;
        }
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), this, (oldValue, newValue) -> {
            if (newValue == null) {
                return;
            }
            isConnect = newValue;
            DJIAircraftApplication.getInstance().setAircraftConnected(newValue);
            if (newValue) {
                loginDJIAccount();
                getAircraftInfo();
                getSerialNumber();
                getCameraInfo();
                //监听SD卡状态
                setSDCardStateListener();
                updateSiteLocation();
            } else {
                ToastUtil.show("无人机断开连接");
                mBinding.tvName.setText("未连接");
                mBinding.tvSn.setText("无人机序列号");
                mBinding.imgAircraft.setImageResource(R.drawable.unconnect_aircraft);
                Drawable img = getResources().getDrawable(R.drawable.red_icon);
                img.setBounds(0, 0, img.getMinimumWidth(), img.getMinimumHeight());
                mBinding.tvName.setCompoundDrawables(img, null, null, null);
                mBinding.tvStatus.setText("请检查连接线");
            }
        });

    }

    private void loadCameraInfoToAsset() {
        String cameraParamStr = readJsonFromAssets(ContextUtil.getApplicationContext(), "cameraParam.json");
        StorageExtKt.getMmkv().putString(ValueKey.CAMERA_PARAMS, cameraParamStr);
        CameraSettingTable cameraSettingTable = new CameraSettingTable(ContextUtil.getApplicationContext());
        cameraSettingTable.DeleteAll();
        if (cameraParamStr != null && !cameraParamStr.isEmpty()) {
            List<CameraInfoBean> cameraInfoBeanList = dji.sampleV5.aircraft.common.json.JsonUtil.fromJson(cameraParamStr, TypeBuilder.newInstance(List.class).addTypeParam(CameraInfoBean.class).build());
            cameraSettingTable.saveAll(cameraInfoBeanList);
        }
    }


    @Override
    public void onRequestSuccess() {
        wayPointViewModel.getSiteInfoLiveData().observe(this, siteInfo -> {
            if (siteInfo.getUavInfo() != null) {
                pushUrl = siteInfo.getUavInfo().getUavPushUrl();
                if (getPushConfig() != null) {
                    ArrayList<WaterMarkInfo> waterMarkInfos = new ArrayList<>();
                    if (mLivePushSettingView.enableWaterMark()) {
                        waterMarkInfos.addAll(mWaterMarkInfos);
                    }
                    if (mCurrentResolution == AlivcResolutionEnum.RESOLUTION_SELF_DEFINE) {
                        AlivcResolutionEnum.RESOLUTION_SELF_DEFINE.setSelfDefineResolution(mLivePushSettingView.getSelfDefineResolutionWidth(), mLivePushSettingView.getSelfDefineResolutionHeight());
                        mAlivcLivePushConfig.setResolution(AlivcResolutionEnum.RESOLUTION_SELF_DEFINE);
                    } else {
                        mAlivcLivePushConfig.setResolution(mCurrentResolution);
                    }
                    if (pushUrl.contains("rtmp://")) {
                        ((Runnable) () -> LivePushActivity.startActivity(HomeActivity.this, mAlivcLivePushConfig, pushUrl, mAsyncValue, mAudioOnlyPush, mVideoOnlyPush, mOrientationEnum, mCameraId, isFlash, mAuthTimeStr,
                                mPrivacyKeyStr, mMixStream, mAlivcLivePushConfig.isExternMainStream(), false, mFpsConfig, waterMarkInfos)).run();
                    } else {
                        ToastUtil.show("推流地址错误");
                    }
                }
            } else {
                ToastUtil.show("站点错误，无法获取推流地址");
            }
        });
        wayPointViewModel.getSiteListLiveData().observe(this, siteList -> {
            siteItemList = new ArrayList<>();
            siteItemList.addAll(siteList.getList());
            StorageExtKt.getMmkv().putString(ValueKey.SITE_LIST_INFO, CommExtKt.toJsonStr(siteList));
        });
        mViewModel.getAppVersionLiveData().observe(this, this::startUpdate);
        mViewModel.getUpdateSiteLiveData().observe(this, site -> {
            if (site.equals("success")) {
                Toaster.show("站点位置已更新");
            }
        });
    }

    @Override
    public void onRequestError(@NonNull LoadStatusEntity loadStatus) {
        String errorMsg = "请求码：" + loadStatus.getRequestCode() + "错误码：" + loadStatus.getErrorCode() + "错误信息：" + loadStatus.getErrorMessage();
        DialogExtKt.showActivityDialogMessage(this, errorMsg, "提示", "确定", () -> Unit.INSTANCE, "取消", () -> Unit.INSTANCE, false);
    }

    private void initAliLivePush() {
        mLivePushSettingView = new LivePushSettingView(this);
        mAlivcLivePushConfig = new AlivcLivePushConfig();
        mAlivcLivePushConfig.setExtraInfo("such_as_user_id");
        if (mAlivcLivePushConfig.getPreviewOrientation() == AlivcPreviewOrientationEnum.ORIENTATION_LANDSCAPE_HOME_RIGHT.getOrientation() || mAlivcLivePushConfig.getPreviewOrientation() == AlivcPreviewOrientationEnum.ORIENTATION_LANDSCAPE_HOME_LEFT.getOrientation()) {
            mAlivcLivePushConfig.setNetworkPoorPushImage(getFilesDir().getPath() + File.separator + "alivc_resource/poor_network_land.png");
            mAlivcLivePushConfig.setPausePushImage(getFilesDir().getPath() + File.separator + "alivc_resource/background_push_land.png");
        } else {
            mAlivcLivePushConfig.setNetworkPoorPushImage(getFilesDir().getPath() + File.separator + "alivc_resource/poor_network.png");
            mAlivcLivePushConfig.setPausePushImage(getFilesDir().getPath() + File.separator + "alivc_resource/background_push.png");
        }
        AlivcLivePushConfig.setMediaProjectionPermissionResultData(null);
        if (mAlivcLivePushConfig != null) {
            mAlivcLivePushConfig.setPreviewDisplayMode(AlivcPreviewDisplayMode.ALIVC_LIVE_PUSHER_PREVIEW_ASPECT_FILL);
            SharedPreferenceUtils.setDisplayFit(getApplicationContext(), AlivcPreviewDisplayMode.ALIVC_LIVE_PUSHER_PREVIEW_ASPECT_FILL
                    .getPreviewDisplayMode());
        }
        //标准模式
        assert mAlivcLivePushConfig != null;
        mAlivcLivePushConfig.setEnableRTSForInteractiveMode(false);
        mAlivcLivePushConfig.setLivePushMode(AlivcLiveMode.AlivcLiveBasicMode);
    }

    // 检查权限
    private void checkPermissionAndRequest() {
        MapsInitializer.updatePrivacyShow(HomeActivity.this, true, true);
        XXPermissions.with(this)
                .permission(permissionList)
                .request((permissions, allGranted) -> {
                    if (allGranted) {
                        MapsInitializer.updatePrivacyAgree(HomeActivity.this, true);
                        MapsInitializer.updatePrivacyShow(HomeActivity.this, true, true);
                        //开启地形图
                        MapsInitializer.setTerrainEnable(true);

                        LocationService locationService = MapServiceFactory.getLocationServiceInstance();
                        locationService.connectService();
                        locationService.startOnceLocation();
                        new Handler().postDelayed(
                                () -> {
                                    AMapLocationUtil.INSTANCE.getLocation(HomeActivity.this, (lat, lon) -> {
                                        LocateInfo locateInfo = GCJ02_WGS84.gcj02_To_Wgs84(lat, lon);
                                        List<String> locationInfo = new ArrayList<>();
                                        locationInfo.add(String.format("%.7f", locateInfo.getLongitude()));
                                        locationInfo.add(String.format("%.7f", locateInfo.getLatitude()));
                                    });
                                    wayPointViewModel.getSiteList();
                                    mViewModel.appVersionCheck();
                                }, 2000);
                    } else {
                        showPermissionDialog();
                    }
                });
    }

    private void showPermissionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AppTheme_Dialog);
        builder.setTitle("权限申请");
        builder.setMessage("如果没有允许权限应用可能无法正常工作，请到应用设置的权限管理中修改权限。");
        builder.setNegativeButton("取消", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
        });
        builder.setPositiveButton("确定", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
            start2SettingPage();
        });
        builder.setCancelable(false);
        builder.show();
    }

    private void start2SettingPage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && !Environment.isExternalStorageManager()) {
            // 引导用户前往系统设置界面
            Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            Uri uri = Uri.fromParts("package", getPackageName(), null);
            intent.setData(uri);
            startActivity(intent);
        }
    }

    private void getCameraInfo(){
        //获取当前相机类型
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraType), new CommonCallbacks.CompletionCallbackWithParam<CameraType>() {
            @Override
            public void onSuccess(CameraType cameraType) {
                if (cameraType != null) {
                    StorageExtKt.getMmkv().putString(ValueKey.CAMERA_TYPE, JsonUtil.toJson(cameraType));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e("HomeActivity", "onFailure: " + idjiError.description());
            }
        });
        //获取当前相机镜头类型
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource), new CommonCallbacks.CompletionCallbackWithParam<CameraVideoStreamSourceType>() {
            @Override
            public void onSuccess(CameraVideoStreamSourceType cameraVideoStreamSourceType) {
                if (cameraVideoStreamSourceType != null) {
                    StorageExtKt.getMmkv().putString(ValueKey.CAMERA_MODE, JsonUtil.toJson(cameraVideoStreamSourceType));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e("HomeActivity", "onFailure: " + idjiError.description());
            }
        });
    }

    private void loginDJIAccount() {
        //判断是否登录了大疆账号
        LoginInfo loginInfo = UserAccountManager.getInstance().getLoginInfo();
        if (loginInfo.getAccount() == null) {
            try {
                UserAccountManager.getInstance().logInDJIUserAccount(HomeActivity.this, false, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        ToastUtil.show("登录成功！");
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("登录失败:" + error.description());
                    }
                });
                ToastUtil.show("请登录大疆账号");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            Log.e(TAG, ":login " + loginInfo.getAccount());
        }
    }

    //获取飞行器相关数据
    private void getAircraftInfo() {
        KeyManager.getInstance().getValue(KeyTools.createKey(ProductKey.KeyProductType), new CommonCallbacks.CompletionCallbackWithParam<ProductType>() {
            @Override
            public void onSuccess(ProductType productType) {
                DJIAircraftApplication.getInstance().setProductType(productType);
                Drawable img = getResources().getDrawable(R.drawable.green_icon);
                img.setBounds(0, 0, img.getMinimumWidth(), img.getMinimumHeight());
                mBinding.tvName.setCompoundDrawables(img, null, null, null);
                mBinding.tvStatus.setText("已连接");
                setProductInfo(productType);
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                Log.e("register", "KeyProductType onFailure: " + error.description());
            }
        });
    }

    //获取设备码
    private void getSerialNumber() {
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber), new CommonCallbacks.CompletionCallbackWithParam<String>() {
            @Override
            public void onSuccess(String sn) {
                mBinding.tvSn.setText(sn);
                StorageExtKt.getMmkv().putString(ValueKey.UAV_SN, sn);
                Log.e("KeySerialNumber", "onSuccess KeySerialNumber:" + sn);
                MQttManager.getInstance().safeConnectMQtt(sn);
                new Handler().postDelayed(() -> {
                    //更新站点信息
                    updateSiteLocation();
                }, 2000);

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.d(TAG, "onFailure: " + error.description());
            }
        });
    }

    //监听设备SD卡状态
    private void setSDCardStateListener() {
        ComponentIndexType componentIndexType;
        if (ProductUtil.isM400Product()) {
            componentIndexType = ComponentIndexType.PORT_1;
        } else {
            componentIndexType = ComponentIndexType.LEFT_OR_MAIN;
        }
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraStorageInfos, componentIndexType), this, (oldValue1, cameraStorageInfos) -> {
            if (cameraStorageInfos != null) {
                CameraStorageInfo sdcardInfo = cameraStorageInfos.getCameraStorageInfoByLocation(CameraStorageLocation.SDCARD);
                if (sdcardInfo != null) {
                    if (sdcardInfo.getStorageState() == SDCardLoadState.INSERTED) {
                        XLogUtil.INSTANCE.i("HomeActivity", "HomeActivity" + "  onSuccess: SD卡插入");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_SD_CARD, true);
                    } else {
                        XLogUtil.INSTANCE.i("HomeActivity", "HomeActivity" + "  onSuccess: SD卡拔出");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_SD_CARD, false);
                    }
                }
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        /*LoginCache loginCache = SpUtil.getLoginCache();
        if(loginCache == null){
            startActivity(new Intent(this, LoginMixActivity.class));
        }else {
            View view = binding.navView.getHeaderView(0);
            AlphabetCircleImageView ivAvatar = view.findViewById(R.id.iv_nav_avatar);
            ivAvatar.setAlphabet(loginCache.getId());
            TextView tvUserName = view.findViewById(R.id.tv_nav_name);
            tvUserName.setText(loginCache.getId());
            TextView tvOrgName = view.findViewById(R.id.tv_org_name);
            tvOrgName.setVisibility(View.INVISIBLE);
        }*/
    }

    @Override
    public void onBackPressed() {
        //RTMPManager.getInstance().startPublish("rtmp://stream3.skysys.cn/xl_uav/ch666698013");
        super.onBackPressed();
        //MQttManager.getInstance().connectMQtt("1ZNBJ8D00C001F");
        /*File file = new File(Environment.getExternalStorageDirectory().getPath() + "/Skysys/download/DJI_20230419135528_0001_T.JPG");
        try {
            ExifInterface exifInterface = new ExifInterface(file.getAbsolutePath());
            Log.e("TAG", ": "+(score2dimensionality(exifInterface.getAttribute(ExifInterface.TAG_GPS_LATITUDE))));
        } catch (IOException e) {
            e.printStackTrace();
        }*/
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (manager != null) {
            manager.cancel();
        }
        LiveDataEvent.INSTANCE.clearData();
        LiveDataEvent.INSTANCE.isSdkInit().removeObserver(isInit ->{});
        //flyStatusController.onDestroy();
        //MQttManager.getInstance().notifyAppClosed();

        // 取消所有KeyManager监听器，防止内存泄漏
        KeyManager.getInstance().cancelListen(this);
    }

    @Override
    public void onClick(View v) {
        Intent intent;
        switch (v.getId()) {
            // 抽屉菜单打开
            case R.id.img_menu:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                updateLoginInfo();//每次打开menu都更新登录信息
                mBinding.drawerLayout.openDrawer(GravityCompat.START);
                break;
            case R.id.fly:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                toStartActivity(DefaultLayoutActivity.class);
                break;
            case R.id.btn_multimedia:
                if (!isConnect) {
                    ToastUtil.show("请先连接上飞机");
                    return;
                }
                startActivity(new Intent(this, PictureActivity.class));
                break;
            case R.id.btn_control_center:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                // 调度中心
                startActivity(new Intent(this, SiteListActivity.class));
                break;
            case R.id.btn_single_live:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                if (CommExtKt.getDeviceModel().contains("DJI")) {
                    ToastUtil.show("单兵直播功能暂不支持遥控器设备");
                    return;
                }
                showSitePopup();
              /*  intent = new Intent(HomeActivity.this, PushConfigActivity.class);
                if (!TextUtils.isEmpty(mPushUrl)) {
                    intent.putExtra("pushUrl", mPushUrl);
                }
                startActivity(intent);*/
                break;
            case R.id.btn_task_manager:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                startActivity(new Intent(this, MissionListActivity.class));
                break;
            case R.id.btn_way_line:
                if (FastClickUtil.isFastClick()) {
                    return;
                }
                startActivity(new Intent(this, NewMissionActivity.class));
                break;
        }
    }

    private void updateSiteLocation() {
        uavLocation = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
        UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();
        if (uavInfoSN != null && uavInfoSN.getData() != null && uavInfoSN.getData().getSiteInfo() != null && uavInfoSN.getData().getSiteInfo().getSiteLocation() != null) {
            double[] siteLocation = DJIAircraftApplication.getInstance().getUavInfoSN().getData().getSiteInfo().getSiteLocation();
            String siteId = DJIAircraftApplication.getInstance().getUavInfoSN().getData().getSiteInfo().getSiteID();
            if (uavLocation != null) {
                double[] location = new double[]{uavLocation.getLongitude(), uavLocation.getLatitude()};
                // 对比经纬度
                boolean isSameLocation = (siteLocation[0] == location[0]) && (siteLocation[1] == location[1]);
                if (!isSameLocation) {
                    SiteLocationRequest siteLocationRequest = new SiteLocationRequest();
                    siteLocationRequest.setSiteId(siteId);
                    siteLocationRequest.setSiteLocation(List.of(location[0], location[1]));
                    siteLocationRequest.setSiteEllipsAltitude(0);
                    mViewModel.updateSiteLocation(siteLocationRequest);
                }
            }
        }
    }

    private void updateLoginInfo() {
        View view = mBinding.navView.getHeaderView(0);
        AlphabetCircleImageView ivAvatar = view.findViewById(R.id.iv_nav_avatar);
        TextView tvUserName = view.findViewById(R.id.tv_nav_name);
        TextView tvOrgName = view.findViewById(R.id.tv_org_name);
        ImageView exit = view.findViewById(R.id.exit);
        ivAvatar.setCircleBackgroundColor(Color.parseColor("#e3e3e3"));

        exit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    // 先断开MQ连接
                    XLogUtil.INSTANCE.d(TAG, "用户退出登录，断开MQ连接");
                    MQttManager.getInstance().closeMQtt();
                } catch (Exception e) {
                    XLogUtil.INSTANCE.e(TAG, "断开MQ连接异常: " + e.getMessage());
                } finally {
                    // 清除缓存,重新登录
                    SpUtil.setLoginCache(null);
                    startActivity(new Intent(HomeActivity.this, LoginMixActivity.class));
                    finish();
                }
            }
        });

    }

    private Spanned proName(String raw) {
        int index = raw.lastIndexOf(" ");
        if (index != -1) {
            return Html.fromHtml(raw.substring(0, index) + " <font color=\"red\">" + raw.substring(index, raw.length()) + "</font>");
        }
        return Html.fromHtml(raw);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            /*case  R.id.sites_list:
                // 调度中心
                startActivity(new Intent(this, SiteListActivity.class));
                break;*/
            case R.id.offline_map:
                // 离线地图
                startActivity(new Intent(this, OfflineMapActivity.class));
                break;
            case R.id.recent_mission:
                if (TextUtils.isEmpty(SpUtil.getMissionBatch())) {
                    ToastUtil.show("没有待上传的任务");
                } else {
                    if (!isConnect) {
                        ToastUtil.show("请先连接上飞机");

                    } else {
                        startActivity(new Intent(this, RecentMissionDetailActivity.class));
                    }
                }
                break;
            case R.id.task_history:
                startActivity(new Intent(this, HistoryListActivity.class));
//                startActivity(new Intent(this, TaskHistoryListActivity.class));
                break;
            case R.id.his_report:
                startActivity(new Intent(this, HistoryReportActivity.class));
                break;
            case R.id.ai_report:
                // 打开 ai 报告
                startActivity(new Intent(this, AiReportActivity.class));
                break;
            case R.id.warning_picture:
                // 打开 ai 报告
                startActivity(new Intent(this, HistoryWarnActivity.class));
                break;
            case R.id.find_aircraft:
                startActivity(new Intent(this, FindAircraftActivity.class));
                break;
            /*case R.id.way_point:
                DialogExtKt.showNewTaskDialog(this, "选择任务",  missionTypeList, taskTypeBean -> {
                    if (taskTypeBean.getMissionType() == 1) {
                        startActivity(new Intent(this, NewMissionActivity.class));
                    } else {
                        startActivity(new Intent(this, MissionListActivity.class));
                    }
                    return Unit.INSTANCE;
                });
                break;*/
            case R.id.multi_site:
                startActivity(new Intent(this, MultisiteActivity.class));
                break;
            case R.id.log_manager:
                startActivity(new Intent(this, LogManagerActivity.class));
                break;
            case R.id.site_register:
                startStationRegistration();
                break;
            /*case R.id.live_push:
                if (FastClickUtil.isFastClick()) {
                    return true;
                }
                intent = new Intent(HomeActivity.this, PushConfigActivity.class);
                if (!TextUtils.isEmpty(mPushUrl)) {
                    intent.putExtra("pushUrl", mPushUrl);
                }
                startActivity(intent);
                break;*/
        }
        mBinding.drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    private void immersionBar() {
        ImmerseUtil.fullScreen(this);
    }

    /**
     * 启动新的Compose站点注册流程
     */
    private void startStationRegistration() {
        Intent intent = new Intent(this, RegisterStationActivity.class);
        startActivity(intent);
    }

    private void setProductInfo(ProductType productType) {
        if (PRODUCT_INFO_MAP.containsKey(productType)) {
            Pair<Integer, DeviceModelBean> productPair = PRODUCT_INFO_MAP.get(productType);
            uaName = productPair.second.getModelName();
            mBinding.imgAircraft.setImageResource(productPair.first);
            mBinding.tvName.setText(productPair.second.getModelName());
        }
    }

    private void startUpdate(LoginCache.AppVersionInfo apkVersion) {
        if (apkVersion.code == null) return;
        int size = apkVersion.size;
        double apkSize = (size > 0) ? bytesToMegabytes(apkVersion.size) : 0.0;
        manager = new DownloadManager.Builder(this)
                .apkUrl(apkVersion.url)
                .apkName(apkVersion.url.substring(apkVersion.url.lastIndexOf("/") + 1))
                .smallIcon(R.mipmap.app_huiyan)
                .apkVersionCode(Integer.parseInt(apkVersion.code))
                .apkVersionName(apkVersion.version)
                .apkSize(apkSize + "MB")
                .apkDescription(apkVersion.des)
                .enableLog(true)
                .jumpInstallPage(true)
                .dialogProgressBarColor(Color.parseColor("#9919a7f0"))
                .dialogButtonTextColor(Color.WHITE)
                .showNotification(true)
                .showBgdToast(false)
                .forcedUpgrade(false)
                .build();
        manager.download();
    }

    private void check() {
        if (!this.isTaskRoot()) { // 当前类不是该Task的根部，那么之前启动
            Intent intent = getIntent();
            if (intent != null) {
                String action = intent.getAction();
                if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN.equals(action)) { // 当前类是从桌面启动的
                    finish(); // finish掉该类，直接打开该Task中现存的Activity
                }
            }
        }
    }

    @Override
    public boolean showToolBar() {
        return false;
    }

    private void showSitePopup() {
        new XPopup.Builder(this)
                .hasStatusBar(false)
                .popupHeight(DensityUtil.getScreenHeight() / 2)
                .popupWidth(DensityUtil.getScreenWidth() / 2)
                .asCustom(new SiteListPopup(this, siteItemList, item -> {
                    wayPointViewModel.getSiteInfo(item.getSiteId());
                })).show();
    }

    private AlivcLivePushConfig getPushConfig() {

        mAlivcLivePushConfig.setResolution(mDefinition);
        mAlivcLivePushConfig.setInitialVideoBitrate(Integer.parseInt(mLivePushSettingView.getInitVideoBitrate()));
        mAlivcLivePushConfig.setAudioBitRate(1000 * Integer.parseInt(mLivePushSettingView.getAudioBitrate()));

        mAlivcLivePushConfig.setMinVideoBitrate(Integer.parseInt(mLivePushSettingView.getMinVideoBitrate()));
        SharedPreferenceUtils.setMinBit(getApplicationContext(), Integer.parseInt(mLivePushSettingView.getMinVideoBitrate()));

        mAlivcLivePushConfig.setTargetVideoBitrate(Integer.parseInt(mLivePushSettingView.getTargetVideoBitrate()));
        SharedPreferenceUtils.setTargetBit(getApplicationContext(), Integer.parseInt(mLivePushSettingView.getTargetVideoBitrate()));

        mAlivcLivePushConfig.setConnectRetryCount(mLivePushSettingView.getRetryCount());
        mAlivcLivePushConfig.setConnectRetryInterval(mLivePushSettingView.getRetryInterval());

        mAlivcLivePushConfig.setCameraType(AlivcLivePushCameraTypeEnum.CAMERA_TYPE_BACK);
        mAlivcLivePushConfig.setEnableAutoResolution(true);
        mAlivcLivePushConfig.setPushMirror(false);
        mAlivcLivePushConfig.setPreviewOrientation(ORIENTATION_LANDSCAPE_HOME_RIGHT);
        mAuthTimeStr = mLivePushSettingView.getAuthTime();
        mPrivacyKeyStr = mLivePushSettingView.getPrivacyKey();
        return mAlivcLivePushConfig;
    }

    /**
     * 检查登录状态和MQ连接状态
     */
    private void checkLoginAndMQStatus() {
        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache != null) {
            // 检查登录时效性
            long daysDiff = (System.currentTimeMillis() - loginCache.getTime()) / (1000 * 60 * 60 * 24);
            if (daysDiff > 7) {
                // 登录过期，断开MQ并跳转登录页
                XLogUtil.INSTANCE.d(TAG, "登录已过期，断开MQ连接并跳转登录页");
                try {
                    MQttManager.getInstance().closeMQtt();
                } catch (Exception e) {
                    XLogUtil.INSTANCE.e(TAG, "断开MQ连接异常: " + e.getMessage());
                }
                SpUtil.setLoginCache(null);
                startActivity(new Intent(this, LoginMixActivity.class));
                finish();
                return;
            }

            // 登录有效，检查是否需要恢复MQ连接
            checkAndRestoreMQConnection();
        }
    }

    /**
     * 检查并恢复MQ连接
     */
    private void checkAndRestoreMQConnection() {
        // 如果有有效的无人机SN且MQ未连接，则尝试连接
        String sn = StorageExtKt.getMmkv().getString(ValueKey.UAV_SN, "");
        if (!sn.isEmpty() && !MQttManager.getInstance().isConnected()) {
            XLogUtil.INSTANCE.d(TAG, "检测到有效SN且MQ未连接，尝试恢复连接: " + sn);
            MQttManager.getInstance().safeConnectMQtt(sn);
        } else if (sn.isEmpty()) {
            XLogUtil.INSTANCE.d(TAG, "无人机SN为空，等待无人机连接后再建立MQ连接");
        } else if (MQttManager.getInstance().isConnected()) {
            XLogUtil.INSTANCE.d(TAG, "MQ连接已存在，无需重复连接");
        }
    }
}
