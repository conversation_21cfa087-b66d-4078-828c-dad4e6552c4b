package dji.sampleV5.aircraft;

import android.content.Context;
import android.content.res.XmlResourceParser;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbManager;

import org.xmlpull.v1.XmlPullParserException;

import java.io.IOException;


public class DJIUtil {
    public static UsbAccessory djiUSBConnected(Context context) {
        UsbManager manager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        UsbAccessory[] accessoryList = manager.getAccessoryList();
        if (accessoryList != null) {
            for (UsbAccessory item : accessoryList) {
                XmlResourceParser parser = context.getResources().getXml(dji.v5.core.R.xml.accessory_filter);
                try {
                    parser.next();
                    parser.next();
                    parser.next();
                    String manufacturer = parser.getAttributeValue(null, "manufacturer");
                    String model = parser.getAttributeValue(null, "model");
                    if (item.getManufacturer().equals(manufacturer) && item.getModel().equals(model)) {
                        return item;
                    }
                } catch (XmlPullParserException | IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

   /* public static boolean fpvEquiped() {
        VideoFeeder videoFeeder = VideoFeeder.getInstance();
        if (videoFeeder != null) {
            VideoFeeder.VideoFeed videoFeed = videoFeeder.getPrimaryVideoFeed();
            if (videoFeed.getVideoSource() != VideoFeeder.PhysicalSource.FPV_CAM) {
                videoFeed = videoFeeder.getSecondaryVideoFeed();
                return videoFeed.getVideoSource() == VideoFeeder.PhysicalSource.FPV_CAM;
            } else {
                return true;
            }
        }
        return false;
    }*/

}
