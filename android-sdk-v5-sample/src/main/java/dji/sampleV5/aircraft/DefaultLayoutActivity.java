/*
 * Copyright (c) 2018-2020 DJI
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 */

package dji.sampleV5.aircraft;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Transformation;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.GravityCompat;
import androidx.core.widget.PopupWindowCompat;
import androidx.databinding.DataBindingUtil;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.lifecycle.ViewModelProvider;

import com.amap.api.maps.AMap;
import com.amap.api.maps.TextureMapView;
import com.amap.api.navi.AmapNaviType;
import com.amap.api.navi.model.NaviLatLng;
import com.hjq.toast.Toaster;
import com.iflytek.sparkchain.core.SparkChain;
import com.iflytek.sparkchain.core.SparkChainConfig;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.core.BasePopupView;
import com.lxj.xpopup.enums.PopupAnimation;
import com.lxj.xpopup.util.XPopupUtils;

import org.greenrobot.eventbus.Subscribe;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.event.Events;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MapPainter;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.lbs.amap.AMapController;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.data.SensorData;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.PopupExtKt;
import dji.sampleV5.aircraft.mvvm.ext.ViewExtKt;
import dji.sampleV5.aircraft.mvvm.net.request.InformAiInfoRequest;
import dji.sampleV5.aircraft.mvvm.net.request.StartMisInfoRequest;
import dji.sampleV5.aircraft.mvvm.ui.activity.navi.AMapNaviActivity;
import dji.sampleV5.aircraft.mvvm.ui.fragment.speaker.RealTimeFragment;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.MegaphoneViewModel;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.OperateViewModel;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.PayloadViewModel;
import dji.sampleV5.aircraft.mvvm.ui.viewModel.SpotLightViewModel;
import dji.sampleV5.aircraft.mvvm.util.AMapLocationUtil;
import dji.sampleV5.aircraft.mvvm.util.LogConfig;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.mvvm.util.upload.RealTimeUploadHelper;
import dji.sampleV5.aircraft.mvvm.widget.camera.CameraThermalPaletteWidget;
import dji.sampleV5.aircraft.mvvm.widget.popup.MapTypePopup;
import dji.sampleV5.aircraft.mvvm.widget.popup.TaskChecklistPopup;
import dji.sampleV5.aircraft.mvvm.widget.searchLight.SearchlightControlView;
import dji.sampleV5.aircraft.mvvm.widget.speaker.OnDrawerCloseListener;
import dji.sampleV5.aircraft.net.bean.FocusParam;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.fly.AircraftFragmentManager;
import dji.sampleV5.aircraft.page.fly.controller.MissionController;
import dji.sampleV5.aircraft.page.fly.controller.PopupWindowController;
import dji.sampleV5.aircraft.page.fly.controller.RtmpController;
import dji.sampleV5.aircraft.page.fly.controller.TemMeasureController;
import dji.sampleV5.aircraft.page.fly.controller.VirtualStickController;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.KeyOperateUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.view.SelectedOptionPanel;
import dji.sampleV5.aircraft.view.ThermalAreaMeterView;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.DJIKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraExposureCompensation;
import dji.sdk.keyvalue.value.camera.CameraExposureMode;
import dji.sdk.keyvalue.value.camera.CameraFocusMode;
import dji.sdk.keyvalue.value.camera.CameraISO;
import dji.sdk.keyvalue.value.camera.CameraShutterSpeed;
import dji.sdk.keyvalue.value.camera.CameraStorageInfo;
import dji.sdk.keyvalue.value.camera.CameraStorageInfos;
import dji.sdk.keyvalue.value.camera.CameraStorageLocation;
import dji.sdk.keyvalue.value.camera.CameraThermalPalette;
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType;
import dji.sdk.keyvalue.value.camera.LaserWorkMode;
import dji.sdk.keyvalue.value.camera.TapZoomMode;
import dji.sdk.keyvalue.value.camera.ZoomTargetPointInfo;
import dji.sdk.keyvalue.value.common.CameraLensType;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.DoublePoint2D;
import dji.sdk.keyvalue.value.common.DoubleRect;
import dji.sdk.keyvalue.value.common.EmptyMsg;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.flightcontroller.FlightControlAuthorityChangeReason;
import dji.sdk.keyvalue.value.flightcontroller.GoHomeState;
import dji.sdk.keyvalue.value.gimbal.GimbalAngleRotation;
import dji.sdk.keyvalue.value.gimbal.GimbalResetType;
import dji.sdk.keyvalue.value.payload.WidgetType;
import dji.sdk.keyvalue.value.payload.WidgetValue;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.video.channel.VideoChannelType;
import dji.v5.common.video.interfaces.IVideoChannel;
import dji.v5.common.video.interfaces.VideoChannelStateChangeListener;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.megaphone.MegaphoneIndex;
import dji.v5.manager.aircraft.megaphone.MegaphoneStatus;
import dji.v5.manager.aircraft.payload.PayloadIndexType;
import dji.v5.manager.aircraft.payload.data.PayloadWidgetInfo;
import dji.v5.manager.aircraft.payload.widget.PayloadWidget;
import dji.v5.manager.aircraft.virtualstick.VirtualStickManager;
import dji.v5.manager.aircraft.virtualstick.VirtualStickState;
import dji.v5.manager.aircraft.virtualstick.VirtualStickStateListener;
import dji.v5.manager.datacenter.MediaDataCenter;
import dji.v5.manager.datacenter.camera.StreamInfo;
import dji.v5.manager.intelligent.AutoSensingInfo;
import dji.v5.manager.intelligent.spotlight.SpotLightInfo;
import dji.v5.manager.interfaces.ICameraStreamManager;
import dji.v5.manager.interfaces.IFlightLogManager;
import dji.v5.network.DJINetworkManager;
import dji.v5.network.IDJINetworkStatusListener;
import dji.v5.utils.common.JsonUtil;
import dji.v5.utils.common.LogUtils;
import dji.v5.utils.common.NetworkUtils;
import dji.v5.ux.accessory.RTKStartServiceHelper;
import dji.v5.ux.cameracore.widget.autoexposurelock.AutoExposureLockWidget;
import dji.v5.ux.cameracore.widget.cameracontrols.CameraControlsWidget;
import dji.v5.ux.cameracore.widget.cameracontrols.exposuresettings.ExposureSettingsPanel;
import dji.v5.ux.cameracore.widget.focusexposureswitch.FocusExposureSwitchWidget;
import dji.v5.ux.cameracore.widget.focusmode.FocusModeWidget;
import dji.v5.ux.cameracore.widget.fpvinteraction.FPVInteractionWidget;
import dji.v5.ux.core.base.SchedulerProvider;
import dji.v5.ux.core.extension.ViewExtensions;
import dji.v5.ux.core.panel.systemstatus.SystemStatusListPanelWidget;
import dji.v5.ux.core.panel.topbar.TopBarPanelWidget;
import dji.v5.ux.core.util.CameraUtil;
import dji.v5.ux.core.util.DataProcessor;
import dji.v5.ux.core.widget.fpv.FPVWidget;
import dji.v5.ux.core.widget.hsi.PrimaryFlightDisplayWidget;
import dji.v5.ux.core.widget.setting.SettingWidget;
import dji.v5.ux.core.widget.systemstatus.SystemStatusWidget;
import dji.v5.ux.training.simulatorcontrol.SimulatorControlWidget;
import dji.v5.ux.visualcamera.CameraVisiblePanelWidget;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import kotlin.Unit;
import me.jessyan.autosize.internal.CancelAdapt;

/**
 * Displays a sample layout of widgets similar to that of the various DJI apps.
 */
public class DefaultLayoutActivity extends AppCompatActivity implements CancelAdapt, OnDrawerCloseListener {

    private final String TAG = LogUtils.getTag(this);

    public FPVWidget primaryFpvWidget;
    protected FPVInteractionWidget fpvInteractionWidget;
    protected FPVWidget secondaryFPVWidget;
    protected RelativeLayout parentView;
    protected RelativeLayout rootView;
    protected SystemStatusListPanelWidget systemStatusListPanelWidget;
    protected SimulatorControlWidget simulatorControlWidget;
    protected CameraVisiblePanelWidget visualCameraPanel;
    protected CameraControlsWidget cameraControlsWidget;
    protected ExposureSettingsPanel exposureSettingsPanel;
    protected AutoExposureLockWidget autoExposureLockWidget;
    protected FocusModeWidget focusModeWidget;
    protected FocusExposureSwitchWidget focusExposureSwitchWidget;
    private ComponentIndexType lastDevicePosition = ComponentIndexType.UNKNOWN;
    private CameraLensType lastLensType = CameraLensType.UNKNOWN;
    protected PrimaryFlightDisplayWidget pfvFlightDisplayWidget;
    protected View cameraConfigBackground;
    protected SettingWidget settingWidget;
    private int widgetHeight;
    private int widgetWidth;
    private int widgetMargin;
    private int deviceWidth;
    private int deviceHeight;
    private CompositeDisposable compositeDisposable;
    private final DataProcessor<CameraSource> cameraSourceProcessor = DataProcessor.create(new CameraSource(ComponentIndexType.UNKNOWN,
            CameraLensType.UNKNOWN));

    public MapController mMapController;
    protected TextureMapView mapView;
    private MapPainter mMapPainter;
    private SensorEventHelper mSensorHelper;
    private int bigWidth;
    private int bigHeight;
    private int smallWidth;
    private int smallHeight;
    public static final int SMALL_WINDOW_RATIO = 4;
    private ActivityDefaultLayoutBinding binding;
    public int currentIndex = Events.IndexEvent.INDEX_MAP;
    public MissionController missionController;
    private PopupWindow cameraModePopupWindow;
    private AircraftFragmentManager mAircraftFragmentManager;
    private PopupWindow mPopupWindow;
    private RtmpController rtmpController;
    private VirtualStickController virtualStickController;
    private boolean isActiveSelect = false; //判断是否是主动切换相机镜头
    private boolean isFPVNow = false;  //判断当前主视角是否是fpv视角
    private String currentCameraMode;
    private VideoChannelStateChangeListener primaryChannelStateListener = null;
    private VideoChannelStateChangeListener secondaryChannelStateListener = null;
    private ThermalAreaMeterView thermalAreaMeterView;
    private AppLatLng aircraftLocation;//获取无人机实时坐标存储起来，方面后面找飞机
    private TemMeasureController temMeasureController;
    private int manualMaxValue = 0;
    private int manualMinValue = 0;
    private CameraThermalPaletteWidget thermalPaletteWidget;
    private MapTypePopup mapTypePopup;
    private OperateViewModel mViewModel;
    private InformAiInfoRequest informAiInfoRequest;
    private StartMisInfoRequest startMisInfoRequest;

    private CameraExposureMode cameraExposureMode;
    // iso
    private DJIKey<List<CameraISO>> isoRangeKey;
    private String iso;
    private List<CameraISO> isoList;

    // 曝光模式
    private DJIKey<CameraExposureMode> exposureModeKey;
    private DJIKey<List<CameraExposureCompensation>> evRangeKey;
    private String evValue;
    private List<CameraExposureCompensation> evList;

    // 快门
    private DJIKey<List<CameraShutterSpeed>> shutterSpeedRangeKey;
    private String shutterSpeed;
    private List<CameraShutterSpeed> shutterSpeedList;
    private View[] exposureModeViews;
    // 当前选中的按钮索引（-1表示没有选中）
    private int currentSelectedButtonIndex = -1;

    private static List<String> loadMapNames = new ArrayList<>();
    private static List<String> downloadings = new ArrayList<>();

    private BasePopupView checkPopup;
    private CameraVideoStreamSourceType cameraVideoStreamSourceType;
    private SpotLightViewModel mSpotLightViewModel;
    //自动感知信息
    private AutoSensingInfo mAutoSensingInfo = new AutoSensingInfo();
    //相机智能跟随信息
    private SpotLightInfo mSpotLightInfo = new SpotLightInfo();
    // 触摸事件的起始坐标和是否需要发送目标区域信息
    private float startX = 0;
    private float startY = 0;
    private DoubleRect mDoubleRect = new DoubleRect();
    private boolean needSendBoundInfo = false;
    private GoHomeState goHomeState;
    //喊话器相关
    private MegaphoneViewModel mSpeakerViewModel;
    private MegaphoneIndex mSpeakerIndex;
    private int closeMode = 1;
    private PayloadViewModel mPayloadViewModel;
    private boolean initPayloadListener = false;
    private PayloadWidgetInfo payloadWidgetInfo;
    RealTimeUploadHelper realTimeUploadHelper;
    public ActivityDefaultLayoutBinding getBinding() {
        return binding;
    }

    private DrawerLayout mDrawerLayout;
    private boolean isOpenCheckList = false;
    private final IDJINetworkStatusListener networkStatusListener = isNetworkAvailable -> {
        if (isNetworkAvailable) {
            LogUtils.d(TAG, "isNetworkAvailable=" + true);
            RTKStartServiceHelper.INSTANCE.startRtkService(false);
        }
    };

    private final ICameraStreamManager.AvailableCameraUpdatedListener availableCameraUpdatedListener = new ICameraStreamManager.AvailableCameraUpdatedListener() {
        @Override
        public void onAvailableCameraUpdated(@NonNull List<ComponentIndexType> availableCameraList) {
            runOnUiThread(() -> updateFPVWidgetSource(availableCameraList));
        }

        @Override
        public void onCameraStreamEnableUpdate(@NonNull Map<ComponentIndexType, Boolean> cameraStreamEnableMap) {}
    };

    private final ICameraStreamManager.ReceiveStreamListener receiveStreamListener = (data, offset, length, info) -> runOnUiThread(() -> {
        updateOverlayParameters(info);
    });

    public AircraftFragmentManager getAircraftFragmentManager() {
        return this.mAircraftFragmentManager;
    }

    public void dismissPopupWindow() {
        if (mPopupWindow != null) {
            mPopupWindow.dismiss();
        }
    }

    public void showPopup(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(690));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(460));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(230));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(900));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, -anchor.getHeight(), Gravity.CENTER);
            fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }

    @SuppressLint("CheckResult")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.e(TAG, String.format("onCreate updateViewLevel: %d", currentIndex));
        binding = DataBindingUtil.setContentView(this, R.layout.activity_default_layout);
        ImmerseUtil.fullScreen(this);
        mViewModel = new ViewModelProvider(this).get(OperateViewModel.class);
        mSpotLightViewModel = new ViewModelProvider(this).get(SpotLightViewModel.class);
        mSpeakerViewModel = new ViewModelProvider(this).get(MegaphoneViewModel.class);
        mPayloadViewModel = new ViewModelProvider(this).get(PayloadViewModel.class);

        widgetHeight = (int) getResources().getDimension(dji.v5.ux.R.dimen.uxsdk_mini_map_height);
        widgetWidth = (int) getResources().getDimension(dji.v5.ux.R.dimen.uxsdk_mini_map_width);
        widgetMargin = (int) getResources().getDimension(dji.v5.ux.R.dimen.uxsdk_mini_map_margin);
        exposureModeViews = new View[]{
                binding.pvCameraIso,
                binding.pvCameraShutter,
                binding.pvCameraEv
        };
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        deviceHeight = displayMetrics.heightPixels;
        deviceWidth = displayMetrics.widthPixels;

        // Setup top bar state callbacks
        TopBarPanelWidget topBarPanel = findViewById(R.id.panel_top_bar);
        SystemStatusWidget systemStatusWidget = topBarPanel.getSystemStatusWidget();
        if (systemStatusWidget != null) {
            systemStatusWidget.setOnClickListener(v -> ViewExtensions.toggleVisibility(systemStatusListPanelWidget));
        }

        mDrawerLayout = findViewById(R.id.drawer_view);
        // Add DrawerListener to handle touch events for the right drawer
        mDrawerLayout.addDrawerListener(new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {
            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                drawerView.setOnTouchListener((v, event) -> true);
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                drawerView.setOnTouchListener(null);
            }

            @Override
            public void onDrawerStateChanged(int newState) {
            }
        });

        settingWidget = topBarPanel.getSettingWidget();
        if (settingWidget != null) {
            settingWidget.setOnClickListener(v -> toggleRightDrawer());
        }

        primaryFpvWidget = findViewById(R.id.widget_primary_fpv);
        secondaryFPVWidget = findViewById(R.id.widget_secondary_fpv);
        fpvInteractionWidget = findViewById(dji.v5.ux.R.id.widget_fpv_interaction);
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
            secondaryFPVWidget.setVisibility(View.GONE);
        }

        fpvInteractionWidget = findViewById(R.id.widget_fpv_interaction);
        parentView = findViewById(R.id.root_view);
        rootView = findViewById(R.id.fly_page_main);
        systemStatusListPanelWidget = findViewById(R.id.widget_panel_system_status_list);
        simulatorControlWidget = findViewById(R.id.widget_simulator_control);
        cameraControlsWidget = findViewById(R.id.widget_camera_controls);
        visualCameraPanel = findViewById(dji.v5.ux.R.id.panel_visual_camera);
        focusModeWidget = findViewById(dji.v5.ux.R.id.widget_focus_mode);
        focusExposureSwitchWidget = findViewById(dji.v5.ux.R.id.widget_focus_exposure_switch);
        thermalPaletteWidget = findViewById(R.id.widget_thermal_palette);
        autoExposureLockWidget = findViewById(R.id.widget_auto_exposure_lock);
        initClickListener();
        //MediaDataCenter.getInstance().getVideoStreamManager().addStreamSourcesListener(sources -> runOnUiThread(() -> updateFPVWidgetSource(sources)));
        MediaDataCenter.getInstance().getCameraStreamManager().addAvailableCameraUpdatedListener(availableCameraUpdatedListener);
        primaryFpvWidget.setOnFPVStreamSourceListener((devicePosition, lensType) -> cameraSourceProcessor.onNext(new CameraSource(devicePosition, lensType)));
        //小surfaceView放置在顶部，避免被大的遮挡
        //primaryFpvWidget.setSurfaceViewZOrderOnTop(true);
        //primaryFpvWidget.setSurfaceViewZOrderMediaOverlay(true);
        secondaryFPVWidget.setSurfaceViewZOrderOnTop(true);
        secondaryFPVWidget.setSurfaceViewZOrderMediaOverlay(true);
        mAircraftFragmentManager = new AircraftFragmentManager(getSupportFragmentManager(), binding);
        initMap(savedInstanceState);
        Event.register(this);
        rtmpController = new RtmpController(this);
        rtmpController.onConnected(true);
        missionController = new MissionController(this, rtmpController);
        missionController.onConnect(true);
        virtualStickController = new VirtualStickController(this);
        PopupWindowController.getInstance().setPopupWindowFromLeft(binding.popupWindowFromLeft);
        PopupWindowController.getInstance().setPopupWindowFromCenter(binding.viewMissionPanel.popupWindowCenter);
        temMeasureController = new TemMeasureController(this, missionController);

        thermalAreaMeterView = new ThermalAreaMeterView(this);
        binding.fpvWrapper.addView(thermalAreaMeterView);
        int viewWidth = DensityUtil.getScreenWidth();
        int viewHeight = DensityUtil.getScreenHeight();
        thermalAreaMeterView.startMeter(viewWidth, viewHeight);
        thermalAreaMeterView.setVisibility(View.GONE);
        // 界面首次创建时, 同步显示状态, 确保 map 显示在上层
        syncLayers();

        if (DJIAircraftApplication.getInstance().getProductType() != null) {
            KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyVirtualStickEnabled), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
                @Override
                public void onSuccess(Boolean isVsEnable) {
                    if (isVsEnable != null) {
                        binding.rlVsTips.setVisibility(isVsEnable ? View.VISIBLE : View.GONE);
                        DJIAircraftApplication.getInstance().setVsEnable(isVsEnable);
                    }
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                }
            });
        }

        VirtualStickManager.getInstance().setVirtualStickStateListener(new VirtualStickStateListener() {
            @Override
            public void onVirtualStickStateUpdate(@NonNull VirtualStickState stickState) {
                if (stickState != null) {
                    //ToastUtil.show("虚拟摇杆：" + stickState.isVirtualStickEnable());
                    binding.rlVsTips.setVisibility(stickState.isVirtualStickEnable() ? View.VISIBLE : View.GONE);
                    DJIAircraftApplication.getInstance().setVsEnable(stickState.isVirtualStickEnable());
                }
            }

            @Override
            public void onChangeReasonUpdate(@NonNull FlightControlAuthorityChangeReason reason) {

            }
        });

        DJINetworkManager.getInstance().addNetworkStatusListener(networkStatusListener);
        //设置手动对焦值
        setManualValue();

        mapTypePopup = new MapTypePopup(this, position -> {
            if (position == 0) {
                mapView.getMap().setMapType(AMap.MAP_TYPE_NORMAL);
            }else  {
                mapView.getMap().setMapType(AMap.MAP_TYPE_SATELLITE);
            }
        });
        //切换地图类型
        binding.btnMapType.setOnClickListener(v -> {
            if (mapTypePopup.isShow()) {
                mapTypePopup.dismiss();
            } else {
                PopupExtKt.showMapTypePopup(DefaultLayoutActivity.this, mapTypePopup, v);
            }
        });
        //获取下发数据以及AI数据
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            startMisInfoRequest = new StartMisInfoRequest();
            String missionStartStr = bundle.getString("MISSION_START", "");
            startMisInfoRequest = JsonUtil.toBean(missionStartStr, StartMisInfoRequest.class);
            //获取下发数据
            mViewModel.issueTask(startMisInfoRequest);
            informAiInfoRequest = new InformAiInfoRequest();
            String aiInfoStr = bundle.getString("INFORM_AI", "");
            informAiInfoRequest = JsonUtil.toBean(aiInfoStr, InformAiInfoRequest.class);
        }

        mViewModel.getIssueTaskLiveData().observe(this, issueTaskResponse -> {
            if (issueTaskResponse != null) {
                Toaster.show("任务下发成功，开始执行!");
                informAiInfoRequest.setReport_id(issueTaskResponse);
                informAiInfoRequest.setData_time(CommExtKt.conversionTime(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                mViewModel.addTaskUavId(informAiInfoRequest);
            }
        });
        mViewModel.getAiOpenLiveData().observe(this, Toaster::show);
        //相机信息相关
        cameraInfoListener();
        //导航功能相关
        naviInfoListener();
        //任务结束后置空检查单
        LiveDataEvent.INSTANCE.isMissionEnd().observe(this, isEnd -> {
            if (isEnd) {
                if (checkPopup != null) {
                    checkPopup.smartDismiss();
                    checkPopup = null;
                }
            }
        });
        //下发任务错误回调
        LiveDataEvent.INSTANCE.getMissionError().observe(this, Toaster::show);
        //相机智能跟踪相关
        ProductType productType = DJIAircraftApplication.getInstance().getProductType();
        if (productType != null) {
            if (productType == ProductType.M350_RTK || productType == ProductType.M300_RTK) {
                mSpotLightViewModel.initListener();
                spotLightListener();
            }
        }
      /*  //监听无人机是否飞行中
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyIsFlying), this, (aBoolean, isFlying) -> {
            if (isFlying != null && !isFlying && binding.btnAutoSensing.isSelected()) {
                binding.btnAutoSensing.callOnClick();
            }
        });*/
        //监听无人机返航状态
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyGoHomeStatus), this, (aBoolean, goHomeState) -> {
            if (goHomeState != null) {
                this.goHomeState = goHomeState;
                XLogUtil.INSTANCE.d(TAG, "当前无人机返航状态: " + goHomeState);
                if (goHomeState != GoHomeState.IDLE && binding.btnAutoSensing.isSelected()) {
                    binding.btnAutoSensing.callOnClick();
                }
            }
        });
        mSpeakerViewModel.addListener();
        //喊话器VM监听
        speakerVMListener();
        //初始化科大讯飞语音合成SDK
        initSparkChain();
        //探照灯相关监听
        initSearchLight();
        //气体传感器数据返回监听
        initSensorListener();
    }

    private void initSensorListener() {
        mPayloadViewModel.getSensorData().observe(this, sensorData -> {
            if (sensorData.isCompleteData() && sensorData.isMergedPacket()) {
                XLogUtil.INSTANCE.i(TAG, "收到气体传感器数据: " + CommExtKt.toJsonStr(sensorData));
                updateSensorDisplay(sensorData);
            }
        });
    }

    private void initSearchLight() {
        mPayloadViewModel.getPayloadBasicInfo().observe(this, payloadBasicInfo -> {
            if (payloadBasicInfo != null) {
                XLogUtil.INSTANCE.d(TAG, "当前负载信息: " + CommExtKt.toJsonStr(payloadBasicInfo));
            }
        });
        mPayloadViewModel.getPayloadWidgetInfo().observe(this, payloadWidgetInfo -> {
            if (payloadWidgetInfo == null) {
                return;
            }
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (payloadWidgetInfo.getMainInterfaceWidgetList() == null || payloadWidgetInfo.getMainInterfaceWidgetList().isEmpty()) {
                    mPayloadViewModel.pullWidgetInfo();
                }
            }, 500);
            this.payloadWidgetInfo = payloadWidgetInfo;
            List<PayloadWidget> widgetList = payloadWidgetInfo.getMainInterfaceWidgetList();
            if (widgetList == null || widgetList.isEmpty()) {
                return;
            }

            PayloadWidget switchWidget = null;
            PayloadWidget rangeWidget = null;
            for (PayloadWidget widget : widgetList) {
                if (widget.getWidgetType() == WidgetType.SWITCH && widget.getWidgetValue() == 1) {
                    switchWidget = widget;
                } else if (widget.getWidgetType() == WidgetType.RANGE && widget.getWidgetValue() != 0) {
                    rangeWidget = widget;
                }
            }
            if (switchWidget != null && rangeWidget != null) {
                binding.searchlightControlView.setBrightness(rangeWidget.getWidgetValue());
            }

            try {
                XLogUtil.INSTANCE.d(TAG, "当前负载组件信息: " + CommExtKt.toJsonStr(payloadWidgetInfo));
            } catch (Exception e) {
                XLogUtil.INSTANCE.e(TAG, "日志记录失败" + e.getLocalizedMessage());
            }
        });

        //探照灯组件点击监听
        binding.searchlightControlView.setOnBrightnessChangeListener(newBrightness -> {
            if (newBrightness != null) {
                if (newBrightness > 40) {
                    Toaster.show("未起飞限制最大亮度，起飞后恢复至设置值");
                }
                List<PayloadWidget> widgetList = payloadWidgetInfo.getMainInterfaceWidgetList();
                if (widgetList == null) {
                    return null;
                }
                Optional<PayloadWidget> optionalWidget = widgetList.stream()
                        .filter(widget -> "brightiness".equals(widget.getWidgetName())
                                && widget.getWidgetType() == WidgetType.RANGE)
                        .findFirst();
                if (optionalWidget.isPresent()) {
                    PayloadWidget matchedWidget = optionalWidget.get();
                    WidgetValue widgetValue = new WidgetValue();
                    widgetValue.setType(matchedWidget.getWidgetType());
                    widgetValue.setValue(newBrightness);
                    widgetValue.setIndex(matchedWidget.getWidgetIndex());
                    mPayloadViewModel.setWidgetValue(widgetValue);
                }
            }
            return null;
        });

        binding.searchlightControlView.setOnSelectedTypeChangeListener(selectedType -> {
            if (selectedType == SearchlightControlView.SelectedType.OFF) {
                handleWidgetUpdate("on/off", 0);
            } else if (selectedType == SearchlightControlView.SelectedType.ON) {
                // 检查blink组件值是否为1
                List<PayloadWidget> widgetList = payloadWidgetInfo.getMainInterfaceWidgetList();
                if (widgetList != null) {
                    for (PayloadWidget widget : widgetList) {
                        if (Objects.equals(widget.getWidgetName(), "blink") && widget.getWidgetType() == WidgetType.SWITCH) {
                            // 如果blink值为1，先将其置0
                            if (widget.getWidgetValue() == 1) {
                                handleWidgetUpdate("blink", 0);
                            }
                            break;
                        }
                    }
                }
                handleWidgetUpdate("on/off", 1);
            } else if (selectedType == SearchlightControlView.SelectedType.MODE_SLOW) {
                handleWidgetUpdate("blink", 1);
            }
            return null;
        });
    }

    /**
     * 处理负载组件更新
     * @param widgetName
     * @param value
     */
    private void handleWidgetUpdate(String widgetName, int value) {
        List<PayloadWidget> widgetList = payloadWidgetInfo.getMainInterfaceWidgetList();
        if (widgetList == null) {
            XLogUtil.INSTANCE.i(TAG, "widgetList is null");
            return;
        }

        for (PayloadWidget widget : widgetList) {
            if (Objects.equals(widget.getWidgetName(), widgetName) && widget.getWidgetType() == WidgetType.SWITCH) {
                WidgetValue widgetValue = new WidgetValue();
                widgetValue.setType(widget.getWidgetType());
                widgetValue.setValue(value);
                widgetValue.setIndex(widget.getWidgetIndex());
                mPayloadViewModel.setWidgetValue(widgetValue);
                return; // 找到后立即返回，避免继续遍历
            }
        }
        XLogUtil.INSTANCE.i(TAG, "未找到匹配的 widget: " + widgetName);
    }

    private void initSparkChain() {
        XLogUtil.INSTANCE.i(TAG, "开始初始化科大讯飞语音合成SDK");

        // 创建工作目录
        File workDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "skysys/audio");
        if (!workDir.exists()) {
            boolean created = workDir.mkdirs();
            if (!created) {
                XLogUtil.INSTANCE.e(TAG, "创建工作目录失败：" + workDir.getAbsolutePath());
                return;
            }
            XLogUtil.INSTANCE.i(TAG, "创建工作目录成功：" + workDir.getAbsolutePath());
        }

        SparkChainConfig sparkChainConfig = SparkChainConfig.builder();
        sparkChainConfig.appID(getResources().getString(R.string.appid))
                .apiKey(getResources().getString(R.string.apikey))
                .apiSecret(getResources().getString(R.string.apiSecret))
                .workDir(workDir.getAbsolutePath())
                .logPath(LogConfig.xLogPath)
                .logLevel(4);
        int ret = SparkChain.getInst().init(getApplicationContext(), sparkChainConfig);
        if (ret == 0) {
            XLogUtil.INSTANCE.i(TAG, "科大讯飞语音合成SDK初始化成功");
        } else {
            XLogUtil.INSTANCE.i(TAG, "科大讯飞语音合成SDK初始化失败，错误码：" + ret);
        }
    }

    private void speakerVMListener() {
        mSpeakerViewModel.getConnectedPayloadPosition().observe(this, payloadPosition -> {
            mSpeakerIndex = payloadPosition;
            if (payloadPosition != null) {
                mSpeakerViewModel.setMegaphoneIndex(payloadPosition, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        XLogUtil.INSTANCE.i(TAG, "喊话器连接成功");
                        binding.btnSpeaker.setVisibility(View.VISIBLE);
                        megaphoneValue();
                        initializeComponent();
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError idjiError) {
                        XLogUtil.INSTANCE.i(TAG, "喊话器连接失败：" + idjiError.description());
                    }
                });
            }
            XLogUtil.INSTANCE.i(TAG, "payloadPosition: " + payloadPosition);
        });
        mSpeakerViewModel.getMegaphonePlayState().observe(this, megaphonePlayState -> {
            if (megaphonePlayState != null) {
                if (megaphonePlayState.getStatus() == MegaphoneStatus.PLAYING) {
                    binding.btnSpeaker.startAnimation();
                } else {
                    binding.btnSpeaker.stopAnimation();
                }
            }
        });
    }

    //默认值这是
    private void megaphoneValue() {
        if (mSpeakerIndex != null) {
            /*//音量设置
            mSpeakerViewModel.setVolume(20, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    XLogUtil.INSTANCE.i(TAG, "喊话器音量设置成功：" + 20);
                }

                @Override
                public void onFailure(@NonNull IDJIError idjiError) {
                    XLogUtil.INSTANCE.i(TAG, "喊话器音量设置失败：" + idjiError.description());
                }
            });*/
            //默认开启AGC
            mSpeakerViewModel.enableAgc(true);
        }
    }

    //初始化喊话器设置组件
    private void initializeComponent() {
        binding.speakerMainComponent.initialize(getSupportFragmentManager(), getLifecycle(), this);
    }

    @Override
    public void onNavigationClose() {
        XLogUtil.INSTANCE.i(TAG, "通过导航图标关闭");
        closeMode = 0;
        // 获取RealTimeFragment实例
        RealTimeFragment realTimeFragment = binding.speakerMainComponent.findRealTimeFragment();
        boolean wasRecording = realTimeFragment != null && realTimeFragment.isRecording();
        // 如果实时界面正在录音，先停止
        if (wasRecording) {
            realTimeFragment.stopRecording();
            // 停止录音
            binding.speakerWidget.setVisibility(View.GONE);
            binding.btnSpeaker.setSelected(false);
            binding.speakerWidget.stopRecording();
            mSpeakerViewModel.stopRecord();
        }
        // 关闭抽屉
        binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNDEFINED, binding.speakerMainComponent);
        binding.drawerView.closeDrawer(binding.speakerMainComponent);
        binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED, binding.speakerMainComponent);
        // Allow touch events when the drawer is closed
        binding.drawerView.setOnTouchListener(null);

        if (binding.btnSpeaker.isSelected()) {
            binding.speakerWidget.setVisibility(View.GONE);
            binding.btnSpeaker.setSelected(false);
        }
    }

    @Override
    public void onExpandButtonClose() {
        XLogUtil.INSTANCE.i(TAG, "通过扩展按钮关闭");
        closeMode = 1;
        // 获取RealTimeFragment实例
        RealTimeFragment realTimeFragment = binding.speakerMainComponent.findRealTimeFragment();
        boolean wasRecording = realTimeFragment != null && realTimeFragment.isRecording();
        if (wasRecording) {
            realTimeFragment.stopRecording();
        }
        // 关闭抽屉
        binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNDEFINED, binding.speakerMainComponent);
        binding.drawerView.closeDrawer(binding.speakerMainComponent);
        binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED, binding.speakerMainComponent);
        // Allow touch events when the drawer is closed
        binding.drawerView.setOnTouchListener(null);

        // 如果之前在录音，延迟后再开启SpeakerWidget的录音
        if (wasRecording) {
            binding.speakerWidget.postDelayed(() -> {
                binding.speakerWidget.startRecording();
                mSpeakerViewModel.startRecord();
            }, 200); // 200ms延迟，必要时可加大
        }
        binding.speakerWidget.setVisibility(View.VISIBLE);
        binding.btnSpeaker.setSelected(true);
    }

    private void spotLightListener() {
        mSpotLightViewModel.getIntelligentFlightInfo().observe(this, intelligentFlightInfo -> {
            if (intelligentFlightInfo != null) {
                XLogUtil.INSTANCE.d(TAG, "intelligentFlightInfo: " + CommExtKt.toJsonStr(intelligentFlightInfo));
            }
        });
        mSpotLightViewModel.getDeviceMessages().observe(this, deviceMessages -> {
            if (deviceMessages != null) {
                if (!deviceMessages.isEmpty()) {
                    if (deviceMessages.get(0).getCode().contains("0x1B092C") || deviceMessages.get(0).getCode().contains("0x1B0930") || deviceMessages.get(0).getCode().contains("0x1B010")) {
                        Toaster.show(deviceMessages.get(0).getDescription());
                        binding.btnSpotlight.setSelected(false);
                    }
                }
                XLogUtil.INSTANCE.d(TAG, "deviceMessages: " + CommExtKt.toJsonStr(deviceMessages));
            }
        });
        mSpotLightViewModel.getAutoSensingSwitch().observe(this, sensingStatus -> {
            if (sensingStatus != null) {
                String openMsg = sensingStatus.get("open");
                String closeMsg = sensingStatus.get("close");
                if (openMsg != null) {
                    if (openMsg.isEmpty()) {
                        updateAutoSensingUI(true, true);
                    } else {
                        Toaster.show("自动感知开启失败：" + openMsg);
                        updateAutoSensingUI(false, false);
                    }
                }

                if (closeMsg != null) {
                    if (closeMsg.isEmpty()) {
                        updateAutoSensingUI(false, false);
                    } else {
                        Toaster.show("自动感知关闭失败：" + closeMsg);
                        updateAutoSensingUI(true, true);
                    }
                }
            }
        });
        mSpotLightViewModel.getCameraSpotlightSwitch().observe(this, cameraSpotlightSwitch -> {
            if (cameraSpotlightSwitch != null) {
                if (cameraSpotlightSwitch.get("open") != null) {
                    if (cameraSpotlightSwitch.get("open").isEmpty()) {
                        binding.btnSpotlight.setSelected(true);
                    } else {
                        Toaster.show("相机智能追踪开启失败：" + cameraSpotlightSwitch.get("open"));
                        binding.btnSpotlight.setSelected(false);
                    }
                }

                if (cameraSpotlightSwitch.get("close") != null) {
                    if (cameraSpotlightSwitch.get("close").isEmpty()) {
                        binding.btnSpotlight.setSelected(false);
                    } else {
                        Toaster.show("相机智能追踪关闭失败：" + cameraSpotlightSwitch.get("close"));
                        binding.btnSpotlight.setSelected(true);
                    }
                }
            }
        });
        mSpotLightViewModel.getAutoSensingInfo().observe(this, autoSensingInfo -> {
            if (autoSensingInfo != null) {
                mAutoSensingInfo = autoSensingInfo;
                binding.overLayerView.onTargetChange(autoSensingInfo.getTargets());
                DJIAircraftApplication.getInstance().setAutoSensingInfo(mAutoSensingInfo);
            }
        });
        mSpotLightViewModel.getSpotLightInfo().observe(this, spotLightInfo -> {
            if (spotLightInfo != null) {
                mSpotLightInfo = spotLightInfo;
                XLogUtil.INSTANCE.d(TAG, "spotLightInfo: " + CommExtKt.toJsonStr(spotLightInfo));
            }
        });
        mSpotLightViewModel.getSpotLightTarget().observe(this, spotLightTarget -> {
            if (spotLightTarget != null) {
                XLogUtil.INSTANCE.d(TAG, "spotLightTarget: " + CommExtKt.toJsonStr(spotLightTarget));
            }
        });
    }

    // 保存上一次更新的视频尺寸
    private int lastVideoWidth = 0;
    private int lastVideoHeight = 0;

    // 更新坐标转换参数
    private void updateOverlayParameters(StreamInfo info) {
        if (info == null) {
            return;
        }

        int videoWidth = info.getWidth();
        int videoHeight = info.getHeight();

        // 检查视频尺寸是否发生变化，如果没有变化则不更新
        if (videoWidth == lastVideoWidth && videoHeight == lastVideoHeight) {
            return;
        }

        // 保存当前视频尺寸
        lastVideoWidth = videoWidth;
        lastVideoHeight = videoHeight;

        int viewWidth = binding.overLayerView.getWidth();
        int viewHeight = binding.overLayerView.getHeight();

        // 如果视图尺寸还未初始化，延迟更新
        if (viewWidth <= 0 || viewHeight <= 0) {
            binding.overLayerView.post(() -> updateOverlayParameters(info));
            return;
        }

        // 计算视频流与屏幕的宽高比
        float videoRatio = (float) videoWidth / videoHeight;
        float screenRatio = (float) viewWidth / viewHeight;

        // 根据CENTER_INSIDE缩放规则计算实际显示尺寸和偏移量
        float scaleRatioX, scaleRatioY, offsetX, offsetY;

        if (videoRatio > screenRatio) {
            // 视频比屏幕宽，以宽度为基准，上下有黑边
            float scaledHeight = viewWidth / videoRatio;
            scaleRatioX = (float) viewWidth / videoWidth;
            scaleRatioY = scaledHeight / videoHeight;
            offsetX = 0;
            offsetY = (viewHeight - scaledHeight) / 2;
        } else {
            // 视频比屏幕窄，以高度为基准，左右有黑边
            float scaledWidth = viewHeight * videoRatio;
            scaleRatioX = scaledWidth / videoWidth;
            scaleRatioY = (float) viewHeight / videoHeight;
            offsetX = (viewWidth - scaledWidth) / 2;
            offsetY = 0;
        }

        // 将计算好的参数传递给OverLayerTopView
        binding.overLayerView.updateOverlayParameters(videoWidth, videoHeight, scaleRatioX, scaleRatioY, offsetX, offsetY);

        XLogUtil.INSTANCE.d(TAG, "updateOverlayParameters: videoSize=" + videoWidth + "x" + videoHeight +
                            ", viewSize=" + viewWidth + "x" + viewHeight +
                            ", scaleX=" + scaleRatioX + ", scaleY=" + scaleRatioY +
                            ", offsetX=" + offsetX + ", offsetY=" + offsetY);
    }

    /**
     * 更新UI
     * @param isAutoSensingOn
     * @param showOverlay
     */
    private void updateAutoSensingUI(boolean isAutoSensingOn, boolean showOverlay) {
        binding.btnAutoSensing.setSelected(isAutoSensingOn);
        int visibility = showOverlay ? View.VISIBLE : View.INVISIBLE;
        if (binding.overLayerView.getVisibility() != visibility) {
            binding.overLayerView.setVisibility(visibility);
        }
    }

    //显示任务检查单
    private void showChecklistPopup(MissionJson missionInfo) {
        if (checkPopup != null) {
            checkPopup = null;
        }
        checkPopup = new XPopup.Builder(this)
                .isViewMode(true)
                .hasStatusBar(false)
                .hasNavigationBar(false)
                .autoFocusEditText(false)
                .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
                .asCustom(new TaskChecklistPopup(this, lastDevicePosition, missionInfo, () -> {
                    if (binding.btnCheckList.getVisibility() == View.GONE) {
                        binding.btnCheckList.setVisibility(View.VISIBLE);
                    }
                    return Unit.INSTANCE;
                }, (resetMission) -> {
                    missionController.onRemoteMission(resetMission);
                    return Unit.INSTANCE;
                }));
        checkPopup.show();
    }


    private void naviInfoListener() {
        //监听无人机当前状态是否链接
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), this, (aBoolean, isConnected) -> {
            if (isConnected == null) return;
            binding.ivNaviToAircraft.setVisibility(isConnected ? View.VISIBLE : View.GONE);
        });
        binding.ivNaviToAircraft.setOnClickListener(v -> {
            if (!NetworkUtils.isNetworkAvailable()) {
                Toaster.showShort("暂不支持离线导航！");
                return;
            }
            showNaviTypePopup();
        });
    }

    private void cameraInfoListener() {
        //相机参数相关
        binding.ivManual.setOnClickListener(v -> {
            closeAllPicView();
            currentSelectedButtonIndex = -1;
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                KeyOperateUtil.INSTANCE.setExposureMode(this, CameraExposureMode.PROGRAM, lastLensType, lastDevicePosition, (cameraExposureMode -> {
                    this.cameraExposureMode = cameraExposureMode;
                    updateExposureUI(cameraExposureMode);
                    return Unit.INSTANCE;
                }), djiError ->{
                    Toaster.show(djiError);
                    return Unit.INSTANCE;
                });
            } else if (cameraExposureMode == CameraExposureMode.PROGRAM) {
                KeyOperateUtil.INSTANCE.setExposureMode(this, CameraExposureMode.MANUAL, lastLensType, lastDevicePosition, (cameraExposureMode -> {
                    this.cameraExposureMode = cameraExposureMode;
                    updateExposureUI(cameraExposureMode);
                    return Unit.INSTANCE;
                }), djiError ->{
                    Toaster.show(djiError);
                    return Unit.INSTANCE;
                });
            }
        });
        //曝光锁监听
        autoExposureLockWidget.setOnAELockChangeListener( isLocked -> {
            if (ViewExtKt.isVisible(autoExposureLockWidget)) {
                updateEVMode(isLocked);
            }
        });
        //ISO点击监听
        binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                showOnlyPickView(0);
            }
        });
        binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setOnIsoValueChange(value -> {
            if (binding.pvCameraIso.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.MANUAL && !TextUtils.isEmpty(value)) {
                iso = value;
                getIsoValue();
            }
        });
        binding.pvCameraIso.addOnSelectedItemListener(index -> {
            if (isoList != null && !isoList.isEmpty()) {
                CameraISO currISO = isoList.get(index);
                if (isoList.contains(currISO)) {
                    KeyOperateUtil.INSTANCE.setCameraISOValue(DefaultLayoutActivity.this, currISO, lastDevicePosition, lastLensType,  (()-> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });

        //快门点击监听
        binding.panelVisualCamera.getCameraConfigShutterWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.MANUAL) {
                showOnlyPickView(1);
            }
        });
        binding.panelVisualCamera.getCameraConfigShutterWidget().setOnShutterValueChange(value -> {
            if (binding.pvCameraShutter.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.MANUAL && !TextUtils.isEmpty(value)) {
                shutterSpeed = value;
                getShutterValue();
            }
        });
        binding.pvCameraShutter.addOnSelectedItemListener(index ->{
            if (shutterSpeedList != null && !shutterSpeedList.isEmpty()) {
                CameraShutterSpeed currShutter = shutterSpeedList.get(index);
                if (shutterSpeedList.contains(currShutter)) {
                    KeyOperateUtil.INSTANCE.setShutterSpeedValue(DefaultLayoutActivity.this, currShutter, lastDevicePosition, lastLensType, (()-> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });
        //EV点击监听
        binding.panelVisualCamera.getCameraConfigEVWidget().setOnClickListener(v -> {
            if (cameraExposureMode == CameraExposureMode.PROGRAM) {
                showOnlyPickView(2);
            }
        });
        binding.panelVisualCamera.getCameraConfigEVWidget().setOnEVValueChange(value -> {
            if (binding.pvCameraEv.getVisibility() == View.VISIBLE) {
                return;
            }
            if (cameraExposureMode == CameraExposureMode.PROGRAM && !TextUtils.isEmpty(value)) {
                evValue = value;
                getEvValue();
            }
        });
        binding.pvCameraEv.addOnSelectedItemListener(index -> {
            if (evList != null && !evList.isEmpty()) {
                CameraExposureCompensation currEV = evList.get(index);
                if (evList.contains(currEV)) {
                    KeyOperateUtil.INSTANCE.setCameraExposureCompensationValue(DefaultLayoutActivity.this, currEV, lastDevicePosition, lastLensType, (()-> Unit.INSTANCE), onError -> {
                        Toaster.showShort(onError);
                        return Unit.INSTANCE;
                    });
                }
            }
            return Unit.INSTANCE;
        });
    }

    //获取ISO值
    private void getIsoValue() {
        isoRangeKey = KeyTools.createCameraKey(CameraKey.KeyISORange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(isoRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraISO>>() {
            @Override
            public void onSuccess(List<CameraISO> cameraISOS) {
                isoList = cameraISOS;
                updateIsoRange(cameraISOS);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取ISO值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    private void getShutterValue() {
        shutterSpeedRangeKey = KeyTools.createCameraKey(CameraKey.KeyShutterSpeedRange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(shutterSpeedRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraShutterSpeed>>() {
            @Override
            public void onSuccess(List<CameraShutterSpeed> cameraShutterSpeeds) {
                shutterSpeedList = cameraShutterSpeeds;
                updateShutterSpeedRange(cameraShutterSpeeds);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取快门值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    private void getEvValue() {
        evRangeKey = KeyTools.createCameraKey(CameraKey.KeyExposureCompensationRange, lastDevicePosition, lastLensType);
        KeyManager.getInstance().getValue(evRangeKey, new CommonCallbacks.CompletionCallbackWithParam<List<CameraExposureCompensation>>() {
            @Override
            public void onSuccess(List<CameraExposureCompensation> cameraExposureCompensations) {
                evList = cameraExposureCompensations;
                updateEvRange(cameraExposureCompensations);
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG,  "获取曝光补偿值失败\t" + idjiError.errorCode() + "\t" + idjiError.description());
            }
        });
    }

    /**
     * 更新ISO范围
     * @param cameraISOS ISO列表
     */
    private void updateIsoRange(List<CameraISO> cameraISOS) {
        if (TextUtils.isEmpty(iso) || iso.equals("AUTO")) {
            return;
        }
        List<String> isoNameList;
        if (isoList != null && !isoList.isEmpty()) {
            isoNameList = new ArrayList<>();
            for (CameraISO iso : cameraISOS) {
                isoNameList.add(iso.name().replace("ISO_", ""));
            }
            if (!isoNameList.isEmpty()) {
                binding.pvCameraIso.setData(isoNameList);
                if (isoNameList.contains(iso)) {
                    binding.pvCameraIso.selectedItem(iso);
                } else {
                    binding.pvCameraIso.selectedEndItem();
                }
            }
        }
    }

    /**
     * 更新快门范围
     * @param cameraShutterSpeeds
     */
    private void updateShutterSpeedRange(List<CameraShutterSpeed> cameraShutterSpeeds) {
        List<String> shutterSpeedNameList;
        if (cameraShutterSpeeds != null && !cameraShutterSpeeds.isEmpty()) {
            shutterSpeedNameList = new ArrayList<>();
            for (CameraShutterSpeed shutterSpeed : cameraShutterSpeeds) {
                shutterSpeedNameList.add(CameraUtil.shutterSpeedDisplayName(shutterSpeed));
            }
            if (!shutterSpeedNameList.isEmpty()) {
                binding.pvCameraShutter.setData(shutterSpeedNameList);
                if (shutterSpeedNameList.contains(shutterSpeed)) {
                    binding.pvCameraShutter.selectedItem(shutterSpeed);
                } else {
                    binding.pvCameraShutter.selectedEndItem();
                }
            }
        }
    }

    /**
     * 更新EV范围
     * @param cameraExposureCompensations
     */
    private void updateEvRange(List<CameraExposureCompensation> cameraExposureCompensations) {
        List<String> evNameList;
        if (cameraExposureCompensations != null && !cameraExposureCompensations.isEmpty()) {
            evNameList = new ArrayList<>();
            for (CameraExposureCompensation ev : cameraExposureCompensations) {
                evNameList.add(CameraUtil.exposureValueDisplayName(ev));
            }
            if (!evNameList.isEmpty()) {
                binding.pvCameraEv.setData(evNameList);
                if (evNameList.contains(evValue)) {
                    binding.pvCameraEv.selectedItem(evValue);
                } else {
                    binding.pvCameraEv.selectedEndItem();
                }
            }
        }
    }

    /**
     * 初始化相机参数
     * @param devicePosition
     * @param lensType
     */
    private void initCameraParam(ComponentIndexType devicePosition, CameraLensType lensType) {
        if (devicePosition == lastDevicePosition) {
            //相机曝光模式
            exposureModeKey = KeyTools.createCameraKey(CameraKey.KeyExposureMode, devicePosition, lensType);
            KeyManager.getInstance().cancelListen(exposureModeKey);
            KeyManager.getInstance().listen(exposureModeKey, this, (oldValue, cameraExposureMode) ->{
                this.cameraExposureMode = cameraExposureMode;
                updateExposureUI(cameraExposureMode);
                XLogUtil.INSTANCE.i(this.getClass().getName(), "updateExposureUI: " + cameraExposureMode);
            });
        }
    }

    private void updateExposureUI(CameraExposureMode cem) {
        if (cem == CameraExposureMode.PROGRAM) {
            binding.ivManual.setImageResource(R.drawable.selector_camera_exposure_auto);
            //ISO控件文字颜色
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            //快门控件文字颜色
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            //曝光补偿控件文字颜色
            binding.panelVisualCamera.getCameraConfigEVWidget().setEnabled(true);
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
        } else if (cem == CameraExposureMode.MANUAL){
            binding.ivManual.setImageResource(R.drawable.selector_camera_exposure_mode);
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));

            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
            binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));

            binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
            binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent));
        }
    }

    private void updateEVMode(boolean isLock) {
        closeAllPicView();
        binding.panelVisualCamera.getCameraConfigEVWidget().setEnabled(!isLock);
        int widgetColor = isLock ? CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white_33_percent) : CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white);
        binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(widgetColor);
        binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(widgetColor);
    }

    /**
     * 关闭所有View
     */
    private void closeAllPicView() {
        for (View v : exposureModeViews) {
            v.setVisibility(View.GONE);
        }
    }

    /**
     * 显示指定下标的View
     * @param index
     */
    private void showOnlyPickView(int index) {
        // 检查当前按钮是否已经被选中
        if (currentSelectedButtonIndex == index) {
            // 如果已经选中，则恢复默认颜色并隐藏对应控件
            resetButtonColors(index);
            exposureModeViews[index].setVisibility(View.GONE);
            currentSelectedButtonIndex = -1;
        } else {
            // 如果当前按钮没有被选中，则切换颜色并显示对应控件
            resetButtonColors(currentSelectedButtonIndex); // 恢复其他按钮的颜色
            setButtonSelectedColor(index); // 设置当前按钮的颜色
            for (View v : exposureModeViews) {
                if (v.getId() == exposureModeViews[index].getId()) {
                    v.setVisibility(View.VISIBLE);
                } else {
                    v.setVisibility(View.GONE);
                }
            }
            currentSelectedButtonIndex = index; // 记录当前选中的按钮
        }
    }

    //恢复按钮的默认颜色
    private void resetButtonColors(int index) {
        switch (index) {
            case 0:
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
            case 1:
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
            case 2:
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_white));
                break;
        }
    }

    //设置按钮为选中状态的颜色
    private void setButtonSelectedColor(int index) {
        switch (index) {
            case 0:
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEITitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigISOAndEIWidget().setISOAndEIValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
            case 1:
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigShutterWidget().setShutterValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
            case 2:
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVTitleTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                binding.panelVisualCamera.getCameraConfigEVWidget().setEVValueTextColor(CommExtKt.getColorExt(dji.v5.ux.R.color.uxsdk_yellow_in_light));
                break;
        }
    }

    //选择导航方式
    private void showNaviTypePopup() {
        new XPopup.Builder(this)
                .isDestroyOnDismiss(true)
                .hasStatusBar(false)
                .hasNavigationBar(false)
                .hasShadowBg(false)
                .customHostLifecycle(getLifecycle())
                .borderRadius(XPopupUtils.dp2px(this, 15f))
                .asBottomList("请选择导航方式", new String[]{"驾车导航", "步行导航", "骑行导航"}, (position, text) -> {
                    switch (position) {
                        case 0:
                            startNavi(AmapNaviType.DRIVER);
                            break;
                        case 1:
                            startNavi(AmapNaviType.WALK);
                            break;
                        case 2:
                            startNavi(AmapNaviType.RIDE);
                            break;
                    }
                })
                .show();

    }

    NaviLatLng startLatLng, endLatLng;
    private void startNavi(AmapNaviType naviType) {
        try {
            AMapLocationUtil.INSTANCE.getLocation(this, (lat, lon) -> {
                startLatLng = new NaviLatLng(lat, lon);
            });
            if (aircraftLocation == null) {
                Toaster.showShort("无法获取无人机定位，导航失败！");
                return;
            }
            LocateInfo appLatLng = GCJ02_WGS84.wgs84_To_Gcj02(aircraftLocation.getLat(), aircraftLocation.getLng());
            endLatLng = new NaviLatLng(appLatLng.getLatitude(), appLatLng.getLongitude());
            Bundle bundle = new Bundle();
            bundle.putString("start_latlng", JsonUtil.toJson(startLatLng));
            bundle.putString("end_latlng", JsonUtil.toJson(endLatLng));
            bundle.putString("navi_type", JsonUtil.toJson(naviType));
            CommExtKt.toStartActivity(AMapNaviActivity.class, bundle);
        } catch (Exception e) {
            e.printStackTrace();
            XLogUtil.INSTANCE.e(TAG, "----导航异常：" + e.getMessage());
        }
    }

    private void toggleRightDrawer() {
        mDrawerLayout.openDrawer(GravityCompat.END);
    }

    @Override
    public void onBackPressed() {
        if (checkPopup != null && checkPopup.isShow()) {
            if (binding.btnCheckList.getVisibility() == View.GONE) {
                binding.btnCheckList.setVisibility(View.VISIBLE);
            }
            checkPopup.dismiss();
            return;
        }
        if (mDrawerLayout.isDrawerOpen(GravityCompat.END)) {
            mDrawerLayout.closeDrawers();
            // mDrawerLayout's touch listener is handled by its DrawerListener
        } else if (mDrawerLayout.isDrawerOpen(binding.speakerMainComponent)) {
            binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNDEFINED, binding.speakerMainComponent);
            binding.drawerView.closeDrawer(binding.speakerMainComponent);
            binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED, binding.speakerMainComponent);
            // Allow touch events when the drawer is closed
            binding.drawerView.setOnTouchListener(null);
        } else if (mAircraftFragmentManager.onBackPressed()) {
            super.onBackPressed();
        }

    }

    @SuppressLint({"ClickableViewAccessibility", "CheckResult"})
    private void initClickListener() {
        Log.e(TAG, "initClickListener: ");
        secondaryFPVWidget.setOnClickListener(v -> {
            Log.e(TAG, "swapVideoSource: ");
            swapVideoSource();
        });

        binding.cameraMode.setOnClickListener(this::showModeView);

      /*  binding.setting.setOnClickListener(v -> {
            Bundle bundle = new Bundle();
            bundle.putInt("mode", AircraftSettingFragment.TAB_AIRCRAFT);
            getAircraftFragmentManager().addFragment(AircraftFragmentManager.SETTING, bundle);
        });*/

        binding.closeVs.setOnClickListener(v -> virtualStickController.disableVS());

        binding.btnCheckList.setOnClickListener(v -> {
            if (checkPopup != null) {
                checkPopup.show();
            }
        });

        binding.btnSpotlight.setOnClickListener(v -> {
            boolean isSelected = v.isSelected();
            //获取飞机当前返航状态
            goHomeState =  KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyGoHomeStatus));
            if (goHomeState != null && goHomeState != GoHomeState.IDLE && !isSelected) {
                Toaster.showShort("正在返航中，无法开启！");
                return;
            }
            if (!binding.btnAutoSensing.isSelected()) {
                binding.btnAutoSensing.callOnClick();
            }
            // 防止 ViewModel 为空导致的崩溃
            if (mSpotLightViewModel != null) {
                if (!isSelected) {
                    if (!binding.btnAutoSensing.isSelected()) {
                        mSpotLightViewModel.startAutoSensing();
                    }
                    if (mDoubleRect.getWidth() > 0 && mDoubleRect.getHeight() > 0 && needSendBoundInfo) {
                        binding.overLayerView.onSelectBoundInfo(null);
                        mSpotLightViewModel.startSpotlight(mDoubleRect);
                    } else {
                        Toaster.show("请选择跟随目标");
                    }
                } else {
                    mSpotLightViewModel.stopSpotlight();
                }
            }
        });

        binding.btnAutoSensing.setOnClickListener(v -> {
            boolean isSelected = v.isSelected();
            //获取飞机当前返航状态
            goHomeState =  KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyGoHomeStatus));
            if (goHomeState != null && goHomeState != GoHomeState.IDLE && !isSelected) {
                Toaster.showShort("正在返航中，无法开启！");
                return;
            }
            //切换镜头到变焦
            if(cameraVideoStreamSourceType != null && cameraVideoStreamSourceType != CameraVideoStreamSourceType.ZOOM_CAMERA) {
                onModeSelected(1);
            }
            // 防止 ViewModel 为空导致的崩溃
            if (mSpotLightViewModel != null) {
                if (!isSelected) {
                    mSpotLightViewModel.startAutoSensing();
                } else {
                    if (binding.btnSpotlight.isSelected()) {
                        mSpotLightViewModel.stopSpotlight();
                    }
                    mSpotLightViewModel.stopAutoSensing();
                    mDoubleRect = new DoubleRect();
                    mAutoSensingInfo = new AutoSensingInfo();
                }
            }
        });

        binding.overLayerView.setOnTrackingTextClickListener(target -> {
            if (target != null) {
                XLogUtil.INSTANCE.d("onTrackingTextClickListener", "target: " + CommExtKt.toJsonStr(target));
                // 获取目标矩形数据
               if (mSpotLightViewModel != null) {
                   binding.overLayerView.onSelectBoundInfo(null);
                   boolean isAutoSensingSelected = binding.btnAutoSensing.isSelected();
                   boolean isSpotlightSelected = binding.btnSpotlight.isSelected();
                   if (isAutoSensingSelected) {
                       if (!isSpotlightSelected) {
                           mSpotLightViewModel.startSpotlight(target);
                       } else {
                           mSpotLightViewModel.selectManualTarget(target);
                       }
                   }
               }
            }
        });

        binding.overLayerView.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    startX = event.getX();
                    startY = event.getY();
                    binding.overLayerView.onSelectBoundInfo(null);
                    needSendBoundInfo = false;
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (binding.overLayerView.calcManhattanDistance(startX, startY, event.getX(), event.getY()) < 20) {
                        needSendBoundInfo = false;
                        return true;
                    }

                    // 使用OverLayerTopView提供的方法将屏幕坐标转换为视频流相对坐标
                    double startVideoX = binding.overLayerView.screenToVideoX(startX);
                    double startVideoY = binding.overLayerView.screenToVideoY(startY);
                    double currentVideoX = binding.overLayerView.screenToVideoX(event.getX());
                    double currentVideoY = binding.overLayerView.screenToVideoY(event.getY());

                    // 计算中心点和宽高
                    mDoubleRect.setX((startVideoX + currentVideoX) / 2);
                    mDoubleRect.setY((startVideoY + currentVideoY) / 2);
                    mDoubleRect.setWidth(Math.abs(startVideoX - currentVideoX));
                    mDoubleRect.setHeight(Math.abs(startVideoY - currentVideoY));

                    binding.overLayerView.onSelectBoundInfo(mDoubleRect);
                    needSendBoundInfo = true;
                    break;
            }
            return true;
        });

        //喊话器组件
        binding.btnSpeaker.setOnClickListener(v -> {
            binding.btnSpeaker.setSelected(!binding.btnSpeaker.isSelected());
            if (closeMode == 1) {
                if (binding.speakerWidget.getVisibility() == View.GONE) {
                    binding.speakerWidget.setVisibility(View.VISIBLE);
                } else {
                    binding.speakerWidget.setVisibility(View.GONE);
                }
                // Make sure the drawer itself does not consume touches when speakerWidget is toggled.
                // The main content area should handle touches.
                binding.drawerView.setOnTouchListener(null);
            } else {
                // 禁用底层控件的点击事件
                binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED, binding.speakerMainComponent);
                binding.drawerView.openDrawer(binding.speakerMainComponent);
                binding.drawerView.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_OPEN, binding.speakerMainComponent);
                // Consume touch events when the drawer is open
                binding.drawerView.setOnTouchListener((v1, event) -> true);
            }
        });
        //探照灯组件
        binding.btnSearchLight.setOnClickListener(v ->{
            binding.btnSearchLight.setSelected(!binding.btnSearchLight.isSelected());
            boolean isVisible = binding.btnSearchLight.isSelected();
            binding.searchlightControlView.setVisibility(isVisible ? View.VISIBLE : View.GONE);
        });
        //实时喊话
        binding.speakerWidget.setOnSpeakerClickListener(isRecording -> {
            if (isRecording) {
                mSpeakerViewModel.startRecord();
            } else {
                mSpeakerViewModel.stopRecord();
            }
            return null;
        });
        //实时喊话组件设置点击
        binding.speakerWidget.setOnSettingClickListener(() ->{
            // 获取当前speakerWidget的录音状态
            boolean isRecording = binding.speakerWidget.getCurrentState() == MegaphoneStatus.PLAYING;

            // 如果正在录音，先停止录音
            if (isRecording) {
                mSpeakerViewModel.stopRecord();
                binding.speakerWidget.stopRecording();
                binding.speakerWidget.setVisibility(View.GONE);
            }

            // 打开抽屉
            mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED, binding.speakerMainComponent);
            mDrawerLayout.openDrawer(binding.speakerMainComponent);
            mDrawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_OPEN, binding.speakerMainComponent);

            // 如果之前在录音，通知RealTimeFragment开始录音
            if (isRecording) {
                binding.speakerMainComponent.postDelayed(() -> {
                    RealTimeFragment realTimeFragment = binding.speakerMainComponent.findRealTimeFragment();
                    if (realTimeFragment != null && !realTimeFragment.isRecording()) {
                        realTimeFragment.startRecording();
                    }
                }, 200);
            }

            return null;
        });
        //边飞边传开关按钮
        binding.btnRealtimeUpload.setOnClickListener(v -> {
            v.setSelected(!v.isSelected());
            //测试代码
            if (realTimeUploadHelper != null) {
                if (v.isSelected()) {
                    if (Objects.requireNonNull(binding.widgetCameraControls.getCameraCaptureWidget().getShootPhotoWidget()).isShootingPhoto() != null) {
                        // 添加一个标志位，用于忽略首次触发
                        final boolean[] isFirstEmit = {true};
                        binding.widgetCameraControls.getCameraCaptureWidget().getShootPhotoWidget().isShootingPhoto().subscribeOn(SchedulerProvider.ui()).subscribe(isShootingPhoto -> {
                            // 忽略首次触发
                            if (isFirstEmit[0]) {
                                isFirstEmit[0] = false;
                                return;
                            }
                            // 只有从拍照状态变为非拍照状态时才触发上传
                            if (!isShootingPhoto) {
                                realTimeUploadHelper.onPhotoTakenAndRequestUpload(null);
                            }
                        });
                    }
                } else {
                    realTimeUploadHelper.cleanup();
                }
            }

        });
    }

    private void showModeView(View view) {
        if (cameraModePopupWindow == null) {
            cameraModePopupWindow = new PopupWindow();
            SelectedOptionPanel panel = new SelectedOptionPanel(this);
            panel.setCallback((index, result) -> {
                String text = "镜头/" + result;
                binding.cameraMode.setText(text);
                //tvMode.setText(result);
                onModeSelected(index);
                cameraModePopupWindow.dismiss();
            });

            //panel.setCurrentText(((Button) view).getText().toString().trim());

            List<String> data = new ArrayList<>();
            data.add("红外");
            data.add("变焦");
            data.add("广角");
            panel.setData(data);

            cameraModePopupWindow.setContentView(panel);
            cameraModePopupWindow.setOutsideTouchable(true);
            cameraModePopupWindow.setWidth(view.getMeasuredWidth());
            cameraModePopupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        }

        cameraModePopupWindow.showAsDropDown(view, 0, 0);
    }

    private void onModeSelected(int index) {
        isActiveSelect = true;
        cameraVideoStreamSourceType = null;
        if (index == 0) {
            binding.tvMode.setText("红外");
            binding.zoomPanel.setVisibility(View.INVISIBLE);
            binding.thermalZoomPanel.setVisibility(View.VISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.INFRARED_CAMERA;
        } else if (index == 1) {
            binding.tvMode.setText("变焦");
            binding.zoomPanel.setVisibility(View.VISIBLE);
            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.ZOOM_CAMERA;
        } else if (index == 2) {
            binding.tvMode.setText("广角");
            binding.zoomPanel.setVisibility(View.INVISIBLE);
            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
            cameraVideoStreamSourceType = CameraVideoStreamSourceType.WIDE_CAMERA;
        }
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource, lastDevicePosition), cameraVideoStreamSourceType, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                XLogUtil.INSTANCE.d(TAG, "----切换镜头成功");
                if (index == 0) {
                    binding.thermalZoomPanel.updateThermalZoomNum();
                } else if (index == 1) {
                    binding.zoomPanel.updateH20THybridZoomNum();
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.d(TAG, "----切换镜头失败：" + error.description());
            }
        });
    }

    private void getCameraMode() {
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource, lastDevicePosition), new CommonCallbacks.CompletionCallbackWithParam<CameraVideoStreamSourceType>() {
            @Override
            public void onSuccess(CameraVideoStreamSourceType cameraVideoStreamSourceType) {
                if (cameraVideoStreamSourceType != null) {
                    switch (cameraVideoStreamSourceType) {
                        case INFRARED_CAMERA:
                            binding.cameraMode.setText("镜头/红外");
                            binding.tvMode.setText("红外");
                            binding.zoomPanel.setVisibility(View.INVISIBLE);
                            binding.thermalZoomPanel.setVisibility(View.VISIBLE);
                            binding.thermalZoomPanel.updateThermalZoomNum();
                            DJIAircraftApplication.getInstance().setDisplayMode(2);
                            break;
                        case ZOOM_CAMERA:
                            binding.cameraMode.setText("镜头/变焦");
                            binding.tvMode.setText("变焦");
                            binding.thermalZoomPanel.setVisibility(View.INVISIBLE);
                            binding.zoomPanel.setVisibility(View.VISIBLE);
                            binding.zoomPanel.updateH20THybridZoomNum();
                            DJIAircraftApplication.getInstance().setDisplayMode(1);
                            break;
                        case WIDE_CAMERA:
                            binding.cameraMode.setText("镜头/广角");
                            binding.tvMode.setText("广角");
                            DJIAircraftApplication.getInstance().setDisplayMode(5);
                            break;
                    }
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource), this, new CommonCallbacks.KeyListener<CameraVideoStreamSourceType>() {
            @Override
            public void onValueChange(@Nullable CameraVideoStreamSourceType oldValue, @Nullable CameraVideoStreamSourceType newValue) {
                XLogUtil.INSTANCE.e(TAG, "当前相机模式---->onValueChange: " + newValue);
                if (newValue != null) {
                    if (newValue == CameraVideoStreamSourceType.ZOOM_CAMERA && DJIAircraftApplication.getInstance().getZoom() >= 7) {
                        updateManualUI();
                } else {
                        binding.manualZoomWidget.setVisibility(View.GONE);
                    }
                    switch (newValue) {
                        case INFRARED_CAMERA:
                            DJIAircraftApplication.getInstance().setDisplayMode(2);
                            if (!isActiveSelect) {  //如果不是主动切换镜头的，说明是飞控系统触发的，我们要更新一下界面内容
                                binding.cameraMode.setText("镜头/红外");
                                binding.tvMode.setText("红外");
                                binding.zoomPanel.setVisibility(View.GONE);
                                binding.thermalZoomPanel.setVisibility(View.VISIBLE);
;                            }
                            isActiveSelect = false;
                            break;
                        case WIDE_CAMERA:
                            DJIAircraftApplication.getInstance().setDisplayMode(5);
                            if (!isActiveSelect) {  //如果不是主动切换镜头的，说明是飞控系统触发的，我们要更新一下界面内容
                                binding.cameraMode.setText("镜头/广角");
                                binding.tvMode.setText("广角");
                                binding.zoomPanel.setVisibility(View.GONE);
                                binding.thermalZoomPanel.setVisibility(View.GONE);
                            }
                            isActiveSelect = false;
                            break;
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyCameraZoomRatios, lastDevicePosition, CameraLensType.CAMERA_LENS_ZOOM), this, new CommonCallbacks.KeyListener<Double>() {
            @Override
            public void onValueChange(@Nullable Double oldValue, @Nullable Double newValue) {
                XLogUtil.INSTANCE.e(TAG, "当前变焦---->onValueChange: " + newValue);
                if (newValue != null) {
                    binding.zoomPanel.setRatios(newValue);
                    DJIAircraftApplication.getInstance().setZoom(newValue.intValue());
                    getManualZoomRange();
                    if (newValue.intValue() >= 7 && DJIAircraftApplication.getInstance().getDisplayMode() == 1) {
                        updateManualUI();
                    } else {
                        binding.manualZoomWidget.setVisibility(View.GONE);
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyThermalZoomRatios, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), this, new CommonCallbacks.KeyListener<Double>() {
            @Override
            public void onValueChange(@Nullable Double oldValue, @Nullable Double newValue) {
                XLogUtil.INSTANCE.e(TAG, "当前红外变焦---->onValueChange: " + newValue);
                if (newValue != null) {
                    binding.thermalZoomPanel.setRatios(newValue);
                    //DJIAircraftApplication.getInstance().setZoom(newValue.intValue());
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraStorageInfos, lastDevicePosition), this, new CommonCallbacks.KeyListener<CameraStorageInfos>() {
            @Override
            public void onValueChange(@Nullable CameraStorageInfos oldValue, @Nullable CameraStorageInfos cameraStorageInfos) {
                if (cameraStorageInfos != null) {
                    CameraStorageInfo sdcardInfo = cameraStorageInfos.getCameraStorageInfoByLocation(CameraStorageLocation.SDCARD);
                    if (sdcardInfo != null) {
                        DJIAircraftApplication.getInstance().setLeftStorageCapacity(sdcardInfo.getStorageLeftCapacity());
                        DJIAircraftApplication.getInstance().setStorageCapacity(sdcardInfo.getStorageCapacity());
                    }
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation), this, new CommonCallbacks.KeyListener<LocationCoordinate2D>() {
            @Override
            public void onValueChange(@Nullable LocationCoordinate2D oldValue, @Nullable LocationCoordinate2D newValue) {
                if (newValue != null) {
                    if (aircraftLocation == null) {
                        aircraftLocation = new AppLatLng();
                    }
                    aircraftLocation.setLat(newValue.getLatitude());
                    aircraftLocation.setLng(newValue.getLongitude());
                    SpUtil.setAircraftLastLocation(aircraftLocation);
                }
            }
        });

        KeyManager.getInstance().listen(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), this, new CommonCallbacks.KeyListener<CameraThermalPalette>() {
            @Override
            public void onValueChange(@Nullable CameraThermalPalette cameraThermalPalette, @Nullable CameraThermalPalette t1) {
                if (t1 != null) {
                    DJIAircraftApplication.getInstance().setCameraThermalPalette(t1);
                    //ToastUtil.show("thermalPalette: " + t1.name());
                }
            }
        });
    }

    @Override
    protected void onStart() {
        super.onStart();
        mMapPainter.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mMapPainter.onStop();
    }

    @Override
    protected void onDestroy() {
        // 清理地图资源
        if (mMapPainter != null) {
            mMapPainter.onStop();
            mMapPainter.onDestroy();
            mMapPainter = null;
        }

        // 确保地图控制器资源被释放
        if (mMapController != null) {
            // 取消所有监听器注册
            if (mMapController instanceof AMapController) {
                AMapController aMapController = (AMapController) mMapController;
                // 确保TextureMapView被正确释放
                TextureMapView mapView = aMapController.getMapView(this);
                if (mapView != null) {
                    // 移除所有监听器
                    AMap aMap = mapView.getMap();
                    if (aMap != null) {
                        aMap.setOnCameraChangeListener(null);
                        aMap.setOnMapLoadedListener(null);
                    }
                }
            }

            mMapController.onDestroy();
            mMapController = null;
        }

        // 确保地图视图被释放
        if (mapView != null) {
            mapView.onDestroy();
            mapView = null;
        }

        // 清理传感器助手
        if (mSensorHelper != null) {
            mSensorHelper.release();
            mSensorHelper = null;
        }

        // 清理其他资源
        if (compositeDisposable != null && !compositeDisposable.isDisposed()) {
            compositeDisposable.dispose();
        }
        super.onDestroy();
        Event.unregister(this);
        missionController.onDestroy();
        rtmpController.onDestroy();
        //MediaDataCenter.getInstance().getVideoStreamManager().clearAllStreamSourcesListeners();
        MediaDataCenter.getInstance().getCameraStreamManager().removeAvailableCameraUpdatedListener(availableCameraUpdatedListener);
        MediaDataCenter.getInstance().getCameraStreamManager().removeReceiveStreamListener(receiveStreamListener);
        KeyManager.getInstance().cancelListen(this);
        removeChannelStateListener();

        if (DJIAircraftApplication.getInstance().isVsEnable()) {
            virtualStickController.disableVS();
        }
        VirtualStickManager.getInstance().clearAllVirtualStickStateListener();

        DJINetworkManager.getInstance().removeNetworkStatusListener(networkStatusListener);
        if (checkPopup != null) {
            checkPopup = null;
        }
        //移除智能飞行
        if (binding.btnAutoSensing.isSelected()) {
            mSpotLightViewModel.stopAutoSensing();
        }
        if (mSpotLightViewModel != null) {
            mSpotLightViewModel.cleanListener();
            mSpotLightViewModel = null;
        }
        mSpeakerViewModel.removeMegaphoneInfoListener();
        mSpeakerViewModel.removeRealTimeListener();
        //AI识别信息置空
        DJIAircraftApplication.getInstance().setAutoSensingInfo(null);
        SparkChain.getInst().unInit();

        // 关闭所有弹窗
        if (binding.searchlightControlView != null) {
            binding.searchlightControlView.dismissAllPopups();
        }

        if (realTimeUploadHelper != null) {
            realTimeUploadHelper.cleanup();
            realTimeUploadHelper = null;
        }

        if (mPayloadViewModel != null) {
            mPayloadViewModel = null;
        }
    }

    @Subscribe
    public void onReceiveFlyWarning(MissionJson missionJson) {
        Log.e(TAG, "onReceiveFlyWarning: " + CommExtKt.toJsonStr(missionJson));
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (missionJson == null || missionJson.getType() == null) {
                    return;
                }
                if (TextUtils.equals(missionJson.getType(), "code")) {
                    if (TextUtils.equals(missionJson.getCode(), "palette")) {
                        setThermalPalette(missionJson.getThlLensParam().getPalette());
                    } else if (TextUtils.equals(missionJson.getCode(), "displaymode")) {
                        changeDisplayMode(missionJson.getDisplaymode());
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbalheading")) {
                        float gimbalheading = missionJson.getGimbalheading();
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbal")) {
                        int gimbal = missionJson.getGimbal();
                        rotateGimbalPitch(gimbal);
                    } else if (TextUtils.equals(missionJson.getCode(), "zoom")) {
                        int zoom = missionJson.getZoom();
                        //只有御3行业进阶才支持小于2倍的变焦
                        if (zoom < 2 && DJIAircraftApplication.getInstance().getProductType() != ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
                            zoom = 2;
                        }
                        binding.zoomPanel.setRatios(zoom);
                        binding.zoomPanel.setZoom();
                    } else if (TextUtils.equals(missionJson.getCode(), "measure")) {
                        temMeasureController.startMeasureTem(false);
                    } else if (TextUtils.equals(missionJson.getCode(), "gimbalreform")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(GimbalKey.KeyGimbalReset), GimbalResetType.PITCH_YAW, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                Toaster.show("云台复位成功");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError idjiError) {
                                XLogUtil.INSTANCE.d(TAG, "云台角度复位失败" + idjiError.description());
                            }
                        });
                    } else if (TextUtils.equals(missionJson.getCode(), "joystick")) {
                        virtualStickController.onJoyStick(missionJson);
                    } else if (TextUtils.equals(missionJson.getCode(), "photo")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStartShootPhoto), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("开始拍照");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                ToastUtil.show("拍照失败:" + error.description());
                            }
                        });

                    } else if (TextUtils.equals(missionJson.getCode(), "record")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStartRecord), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("开始录像");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                ToastUtil.show("开始录像失败，请确认是否开启录像模式");
                            }
                        });
                    } else if (TextUtils.equals(missionJson.getCode(), "stoprecord")) {
                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStopRecord), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                ToastUtil.show("结束录像");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                            }
                        });
                    } else  if (TextUtils.equals(missionJson.getCode(), "focus")) { //自动对焦
                        FocusParam focusParam = missionJson.getFocusParam();
                        DoublePoint2D doublePoint = new DoublePoint2D();
                        doublePoint.setX(focusParam.getAutoFocusTarget().get(0));
                        doublePoint.setY(focusParam.getAutoFocusTarget().get(1));
                        float targetX = Math.round(doublePoint.getX().floatValue() * fpvInteractionWidget.getWidth());
                        float targetY = Math.round(doublePoint.getY().floatValue() * fpvInteractionWidget.getHeight());
                        setTriggerFocus(fpvInteractionWidget, MotionEvent.ACTION_UP, targetX, targetY);
                    } else if (TextUtils.equals(missionJson.getCode(), "tapzoom")) { //指点对焦
                        float[] areaPoint = missionJson.getAreapoint();
                        if (areaPoint != null) {
                            double x = (areaPoint[0] + areaPoint[2]) / 2;
                            double y = (areaPoint[1] + areaPoint[3]) / 2;
                            tapZoomAtTarget(x, y);
                        } else {
                            float x = missionJson.getX();
                            float y = missionJson.getY();
                            tapZoomAtTarget(x, y);
                        }

                    } else if (TextUtils.equals(missionJson.getCode(), "gesturemode")) { //开启智能跟踪
                        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.M300_RTK || DJIAircraftApplication.getInstance().getProductType() == ProductType.M350_RTK) {
                            if (goHomeState != GoHomeState.IDLE){
                                Toaster.show("正在返航中，无法开启！");
                                return;
                            }
                            if (missionJson.isGesturemode()) {
                                if (binding.btnAutoSensing.isSelected()) return;
                                binding.btnAutoSensing.callOnClick();
                            } else {
                                if (binding.btnAutoSensing.isSelected()) {
                                    binding.btnAutoSensing.callOnClick();
                                }
                            }
                        } else {
                            Toaster.show("当前机型不支持智能追踪");
                        }
                    } else if (TextUtils.equals(missionJson.getCode(), "targettrace")) { //跟踪区域数据
                        if (DJIAircraftApplication.getInstance().getProductType() != ProductType.M300_RTK && DJIAircraftApplication.getInstance().getProductType() != ProductType.M350_RTK) {
                            return;
                        }
                        float[] areaPoint = missionJson.getAreapoint();
                        if (areaPoint != null && areaPoint.length == 4) {
                            // 获取屏幕尺寸
                            int screenWidth = DensityUtil.getScreenWidth();
                            int screenHeight = DensityUtil.getScreenHeight();
                            // 将归一化坐标转换为实际像素坐标
                            double x1 = areaPoint[0] * screenWidth;
                            double y1 = areaPoint[1] * screenHeight;
                            double x2 = areaPoint[2] * screenWidth;
                            double y2 = areaPoint[3] * screenHeight;
                            // 计算中心点(像素坐标)
                            double centerX = (x1 + x2) / 2;
                            double centerY = (y1 + y2) / 2;
                            // 计算宽高(像素)
                            double width = Math.abs(x2 - x1);
                            double height = Math.abs(y2 - y1);
                            // 将像素坐标转回归一化坐标
                            mDoubleRect.setX(centerX / screenWidth);
                            mDoubleRect.setY(centerY / screenHeight);
                            mDoubleRect.setWidth(width / screenWidth);
                            mDoubleRect.setHeight(height / screenHeight);
                            mSpotLightViewModel.selectManualTarget(mDoubleRect);
                        }
                    } else if (TextUtils.equals(missionJson.getCode(), "laserEnabled")) { //激光测距
                        laserAtTarget(missionJson.isLaserEnabled());
                    } else if (TextUtils.equals(missionJson.getCode(), "rths")) { //返航指令
                        if (binding.btnAutoSensing.isSelected()) { //如果开启AI则关闭
                            binding.btnAutoSensing.callOnClick();
                        }
                        //返航关闭测距
                        laserAtTarget(false);
                        missionController.onControlMission(missionJson);
                    } else {
                        binding.zoomPanel.initZoom();
                        missionController.onControlMission(missionJson);
                    }
                } else if (missionJson.getType().equals("adjust")) {
                    virtualStickController.onAdjustAircraft(missionJson);
                } else if (missionJson.getType().equals("joystick")) {// {"msgFrom":"GID_ConsoleDebug@@@skysys010","type":"joystick","mode":1,"joystick":{"vertical":0,"pitch":10,"roll":0,"yaw":0}}
                    virtualStickController.onJoyStick(missionJson);
                } else {
                    //binding.zoomPanel.initZoom();
                    int videoSourceType = missionJson.getCameraSetting().getVideoSourceType();
                    if (videoSourceType == 4) {
                        changeDisplayMode(2);
                    } else if (videoSourceType == 3 || videoSourceType == 1) {
                        changeDisplayMode(1);
                    }

                    int zoom = missionJson.getCameraSetting().getZoomLensDefaultValue().intValue();
                    if(zoom > 0){
                        binding.zoomPanel.setRatios(zoom);
                        binding.zoomPanel.setZoom();
                    }
                    realTimeUploadHelper = new RealTimeUploadHelper(binding, missionJson.getMissionBatch());
                    //changeDisplayMode(2);
                    closeVMode();//关闭超分模式
                    //initThermalPalette();//初始化红外的色盘
                    isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
                    if (isOpenCheckList) {
                        if (checkPopup != null && checkPopup.isShow()) {
                            Toaster.show("重复下发，请关闭检查单后重新下发");
                            return;
                        }
                        showChecklistPopup(missionJson);
                    } else {
                        missionController.onRemoteMission(missionJson);
                    }
                }
            }
        });
    }

    //激光测距
    private void laserAtTarget(boolean isEnable) {
        //先获取当前激光测距模式
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyLaserWorkMode), new CommonCallbacks.CompletionCallbackWithParam<LaserWorkMode>() {
            @Override
            public void onSuccess(LaserWorkMode laserWorkMode) {
                if (laserWorkMode == LaserWorkMode.OPEN_ON_DEMAND) {
                    laserEnable(isEnable);
                } else {
                    //先设置激光测距模式
                    KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserWorkMode), LaserWorkMode.OPEN_ON_DEMAND, new CommonCallbacks.CompletionCallback() {
                        @Override
                        public void onSuccess() {
                            XLogUtil.INSTANCE.d(TAG, "设置激光模式成功");
                            laserEnable(isEnable);
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError idjiError) {
                            XLogUtil.INSTANCE.d(TAG, "设置激光模式失败：" + idjiError);
                        }
                    });
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.d(TAG, "获取激光模式失败" + idjiError);
            }
        });

        //监听激光测距数据
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyLaserMeasureInformation), this, (oldValue, newValue) -> {
            if (newValue != null) {
                XLogUtil.INSTANCE.d(TAG, "激光测距数据：" + CommExtKt.toJsonStr(newValue));
                DJIAircraftApplication.getInstance().setLaserMeasureInformation(newValue);
            }
        });
    }

    /**
     * 开启激光测距
     * @param isEnable 开启或关闭
     */
    private void laserEnable(boolean isEnable) {
        //设置成功后开始开关激光测距
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserMeasureEnabled), isEnable, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                String laserAtTarget = isEnable ? "开启" : "关闭";
                XLogUtil.INSTANCE.d(TAG, "设置激光测距[" + laserAtTarget + "]成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                String laserAtTarget = isEnable ? "开启" : "关闭";
                XLogUtil.INSTANCE.d(TAG, "设置激光测距[" + laserAtTarget + "]失败：" + idjiError);
            }
        });
    }

    //框选变焦
    private void tapZoomAtTarget(double x, double y) {
        ZoomTargetPointInfo zoomTargetPointInfo = new ZoomTargetPointInfo();
        zoomTargetPointInfo.setX(x);
        zoomTargetPointInfo.setY(y);
        zoomTargetPointInfo.setMode(TapZoomMode.GIMBAL_FOLLOW);
        zoomTargetPointInfo.setTapZoomModeEnable(true);
        KeyManager.getInstance().performAction(KeyTools.createCameraKey(CameraKey.KeyTapZoomAtTarget, lastDevicePosition, CameraLensType.CAMERA_LENS_ZOOM),
                zoomTargetPointInfo, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                    @Override
                    public void onSuccess(EmptyMsg emptyMsg) {
                        ToastUtil.show("框选变焦成功");
                        binding.zoomPanel.tapZoom();
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError idjiError) {
                        ToastUtil.show("框选变焦失败"+idjiError.description());
                    }
                });
    }

    private void rotateGimbalPitch(int gimbalPitch) {
        GimbalAngleRotation gimbalAngleRotation = new GimbalAngleRotation();
        gimbalAngleRotation.setPitch((double) gimbalPitch);
        gimbalAngleRotation.setRollIgnored(true);
        gimbalAngleRotation.setYawIgnored(true);
        KeyManager.getInstance().performAction(KeyTools.createKey(GimbalKey.KeyRotateByAngle), gimbalAngleRotation, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                //ToastUtil.show("设置云台俯仰角度成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("设置云台俯仰角度失败:" + error.description());
            }
        });
    }

    private void closeVMode() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalSuperResolution, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), false, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("关闭超分模式成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("关闭超分模式失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void setThermalPalette(String palette){
        CameraThermalPalette cameraThermalPalette = CameraThermalPalette.IRONBOW1;
        switch (palette){
            case "WHITE_HOT":
                cameraThermalPalette = CameraThermalPalette.WHITE_HOT;
                break;
            case "BLACK_HOT":
                cameraThermalPalette = CameraThermalPalette.BLACK_HOT;
                break;
            case "RED_HOT":
                cameraThermalPalette = CameraThermalPalette.RED_HOT;
                break;
            case "IRONBOW1":
                cameraThermalPalette = CameraThermalPalette.IRONBOW1;
                break;
            case "COLOR2":
                cameraThermalPalette = CameraThermalPalette.COLOR2;
                break;
            case "ICE_FIRE":
                cameraThermalPalette = CameraThermalPalette.ICE_FIRE;
                break;
            case "GREEN_HOT":
                cameraThermalPalette = CameraThermalPalette.GREEN_HOT;
                break;
            case "COLOR1":
                cameraThermalPalette = CameraThermalPalette.COLOR1;
                break;
            case "RAINBOW":
                cameraThermalPalette = CameraThermalPalette.RAINBOW;
                break;
            case "RAIN":
                cameraThermalPalette = CameraThermalPalette.RAIN;
                break;
        }
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), cameraThermalPalette, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("设置红外模式为铁红");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("设置色盘失败", "onFailure: " + error.description());
                }

            }
        });
    }

    private void initThermalPalette() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalPalette, lastDevicePosition, CameraLensType.CAMERA_LENS_THERMAL), CameraThermalPalette.IRONBOW1, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //ToastUtil.show("设置红外模式为铁红");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("设置色盘失败", "onFailure: " + error.description());
                }

            }
        });
    }

    //监听到切换视角的指令
    public void changeDisplayMode(int displayMode) {
        switch (displayMode) {
            case 1: //可见光
                onModeSelected(1);
                binding.cameraMode.setText("镜头/变焦");
                break;
            case 2://红外
                onModeSelected(0);
                binding.cameraMode.setText("镜头/红外");
                break;
            case 3://画中画
                break;
            case 4://混合
                break;
            case 5://广角
                onModeSelected(2);
                binding.cameraMode.setText("镜头/广角");
                break;
        }
    }

    public void startMeasureTem() {
        temMeasureController.startMeasureTem(true);
    }

    public void stopMeasureTem() {
        temMeasureController.stopMeasureTem();
        //thermalAreaMeterView.stopMeasureTem();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapController.onResume();
        compositeDisposable = new CompositeDisposable();
        compositeDisposable.add(systemStatusListPanelWidget.closeButtonPressed()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(pressed -> {
                    if (pressed) {
                        ViewExtensions.hide(systemStatusListPanelWidget);
                    }
                }));
        compositeDisposable.add(simulatorControlWidget.getUIStateUpdates()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(simulatorControlWidgetState -> {
                    if (simulatorControlWidgetState instanceof SimulatorControlWidget.UIState.VisibilityUpdated) {
                        if (((SimulatorControlWidget.UIState.VisibilityUpdated) simulatorControlWidgetState).isVisible()) {
                            hideOtherPanels(simulatorControlWidget);
                        }
                    }
                }));
        compositeDisposable.add(cameraSourceProcessor.toFlowable()
                .observeOn(SchedulerProvider.io())
                .throttleLast(500, TimeUnit.MILLISECONDS)
                .subscribeOn(SchedulerProvider.io())
                .subscribe(result -> runOnUiThread(() -> onCameraSourceUpdated(result.devicePosition, result.lensType)))
        );
        //负载监听
        compositeDisposable.add(
                mPayloadViewModel.getPayloadConnectedStatusMapProcessor()
                        .toFlowableOnUI()
                        .subscribe(payloadMap -> {
                            boolean externalConnected = Boolean.TRUE.equals(payloadMap.get(PayloadIndexType.EXTERNAL));
                            boolean isVisible = !payloadMap.isEmpty() && externalConnected;
                            binding.btnSearchLight.setVisibility(isVisible ? View.VISIBLE : View.GONE);
                            if (isVisible && !initPayloadListener) {
                                initPayloadListener = true;
                                mPayloadViewModel.initListener(PayloadIndexType.EXTERNAL);
                            }
                        })
        );

    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapController.onPause();
    }
    //endregion

    public MapController getMapController() {
        return this.mMapController;
    }

    /**
     * 初始化地图
     *
     * @param savedInstanceState
     */
    private void initMap(Bundle savedInstanceState) {
        mMapController = MapServiceFactory.produceMapController();
        mapView = mMapController.getMapView(this);
        //mapView.setVisibility(View.INVISIBLE);

        mSensorHelper = new SensorEventHelper(ContextUtil.getApplicationContext());
        mSensorHelper.init();

        mMapPainter = new MapPainter(this, mMapController, mSensorHelper, false);

        mMapController.onCreate(savedInstanceState);
        mMapPainter.onCreate();

        initUI();
    }

    private void initUI() {
        //videoView = binding.panelFpv;

        initWindowSize();

        //binding.fpvHolder.bringChildToFront(binding.ivFpvBg);
        // updateSmallWindowParams(mapView, smallWidth, smallHeight); todo 初始化 map 尺寸

        RelativeLayout container = binding.fpvHolder.findViewById(R.id.map_container);
        container.addView(mapView);

       /* binding.spinnerStation.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                //Log.e("getStationInfo", "onItemSelected: " + position);
                if (locationList != null) {
                    ((AMapController) mMapController).setLocation(locationList.get(position).getLocation());
                    //((AMapController) mMapController).useOMCMap();
                    mMapController.newLatLngZoom(true, new AppLatLng(locationList.get(position).getLocation_latitude(), locationList.get(position).getLocation_longitude()), 16);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        //动态更新组件位置
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.leftMargin = smallWidth + 30;
        lp.bottomMargin = DensityUtil.dp2px(5);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        binding.viewFlyParamPanel.main.setLayoutParams(lp);*/

        initAnimation();
    }

    private void initAnimation() {
        binding.ivAnimation.setParam(smallWidth, smallHeight, bigWidth, bigHeight);
        updateSmallWindowParams(binding.ivAnimation, smallWidth, smallHeight);
        binding.ivAnimation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.ivAnimation.startAnimation();
                animate();
            }
        });
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
        //binding.flyPageMain.bringChildToFront(binding.viewShootRecorder.main);
    }

    /**
     * 同步层叠顺序: 修改 fpvWrapper 的大小, 然后把 FPVWidget 重新放入, 强迫它适应外层容器大小
     */
    private void syncLayers() {
        /*FPVWidget bigFpv = binding.fpvHolder.findViewById(R.id.widget_primary_fpv);
        View mapContainer = binding.fpvHolder.findViewById(R.id.map_container);*/

        // 调整大小: 如果是视屏在上, 地图全屏; 反之...
        if (currentIndex == Events.IndexEvent.INDEX_LIVE) {
            updateFullWindowParams(binding.mapContainer);
            updateSmallWindowParams(binding.fpvWrapper, smallWidth, smallHeight);

           /* fpvWrapper.removeView(bigFpv);
            fpvWrapper.addView(bigFpv);*/
            binding.widgetPrimaryFpv.setSurfaceViewZOrderMediaOverlay(true);
            binding.fpvHolder.bringChildToFront(binding.fpvWrapper);
            binding.btnMapType.setVisibility(View.VISIBLE);
        } else {
            updateFullWindowParams(binding.fpvWrapper);
            updateSmallWindowParams(binding.mapContainer, smallWidth, smallHeight);
            /*fpvWrapper.removeView(bigFpv);
            fpvWrapper.addView(bigFpv);*/
            binding.widgetPrimaryFpv.setSurfaceViewZOrderMediaOverlay(false);
            binding.fpvHolder.bringChildToFront(binding.mapContainer);
            binding.btnMapType.setVisibility(View.GONE);
        }

        binding.fpvHolder.invalidate();
        binding.flyPageMain.bringChildToFront(binding.ivAnimation);
        Event.post(new Events.IndexEvent(currentIndex));
    }

    public void animate() {
        // 切换图层状态
        currentIndex = currentIndex == Events.IndexEvent.INDEX_LIVE ? Events.IndexEvent.INDEX_MAP : Events.IndexEvent.INDEX_LIVE;
        // 同步显示状态
        syncLayers();
    }

    /**
     * 设置窗口为全屏
     *
     * @param view
     */
    private void updateFullWindowParams(View view) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        view.setLayoutParams(lp);
    }

    private void updateSmallWindowParams(View view, int width, int height) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(width, height);
        lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        view.setLayoutParams(lp);
    }

    private void initWindowSize() {
        bigWidth = DensityUtil.getScreenWidth();
        bigHeight = DensityUtil.getScreenHeight();

        smallWidth = bigWidth / SMALL_WINDOW_RATIO;
        smallHeight = smallWidth * 9 / 16;
    }

    private void hideOtherPanels(@Nullable View widget) {
        View[] panels = {simulatorControlWidget};
        for (View panel : panels) {
            if (widget != panel) {
                panel.setVisibility(View.GONE);
            }
        }
    }

    private void updateFPVWidgetSource(List<ComponentIndexType> availableCameraList) {
        LogUtils.i(TAG, JsonUtil.toJson(availableCameraList));
        if (availableCameraList == null) {
            return;
        }

        ArrayList<ComponentIndexType> cameraList = new ArrayList<>(availableCameraList);

        //没有数据
        if (cameraList.isEmpty()) {
            secondaryFPVWidget.setVisibility(View.GONE);
            return;
        }

        //仅一路数据
        if (cameraList.size() == 1) {
            primaryFpvWidget.updateVideoSource(availableCameraList.get(0));
            secondaryFPVWidget.setVisibility(View.GONE);
            return;
        }

        //大于两路数据
        ComponentIndexType primarySource = getSuitableSource(cameraList, ComponentIndexType.LEFT_OR_MAIN);
        primaryFpvWidget.updateVideoSource(primarySource);
        cameraList.remove(primarySource);

        ComponentIndexType secondarySource = getSuitableSource(cameraList, ComponentIndexType.FPV);
        secondaryFPVWidget.updateVideoSource(secondarySource);

        secondaryFPVWidget.setVisibility(View.VISIBLE);
    }

    private ComponentIndexType getSuitableSource(List<ComponentIndexType> cameraList, ComponentIndexType defaultSource) {
        if (cameraList.contains(ComponentIndexType.LEFT_OR_MAIN)) {
            return ComponentIndexType.LEFT_OR_MAIN;
        } else if (cameraList.contains(ComponentIndexType.RIGHT)) {
            return ComponentIndexType.RIGHT;
        } else if (cameraList.contains(ComponentIndexType.UP)) {
            return ComponentIndexType.UP;
        } else if (cameraList.contains(ComponentIndexType.PORT_1)) {
            return ComponentIndexType.PORT_1;
        } else if (cameraList.contains(ComponentIndexType.PORT_2)) {
            return ComponentIndexType.PORT_2;
        } else if (cameraList.contains(ComponentIndexType.PORT_3)) {
            return ComponentIndexType.PORT_4;
        } else if (cameraList.contains(ComponentIndexType.PORT_4)) {
            return ComponentIndexType.PORT_4;
        } else if (cameraList.contains(ComponentIndexType.VISION_ASSIST)) {
            return ComponentIndexType.VISION_ASSIST;
        }
        return defaultSource;
    }
    private void onCameraSourceUpdated(ComponentIndexType devicePosition, CameraLensType lensType) {
        LogUtils.i(TAG, devicePosition, lensType);
        if (devicePosition == lastDevicePosition && lensType == lastLensType){
            return;
        }
        if (devicePosition != ComponentIndexType.UNKNOWN && devicePosition != lastDevicePosition) {
            MediaDataCenter.getInstance().getCameraStreamManager().removeReceiveStreamListener(receiveStreamListener);
            MediaDataCenter.getInstance().getCameraStreamManager().addReceiveStreamListener(lastDevicePosition, receiveStreamListener);
        }
        lastDevicePosition = devicePosition;
        lastLensType = lensType;
        initCameraParam(devicePosition, lensType);
        updateViewVisibility(devicePosition, lensType);
        updateInteractionEnabled();
        getCameraMode();
        //如果无需使能或者显示的，也就没有必要切换了。
        if (fpvInteractionWidget.isInteractionEnabled()) {
            fpvInteractionWidget.updateCameraSource(devicePosition, lensType);
        }
        if (focusModeWidget.getVisibility() == View.VISIBLE) {
            focusModeWidget.updateCameraSource(devicePosition, lensType);
        }
        if (focusExposureSwitchWidget.getVisibility() == View.VISIBLE) {
            focusExposureSwitchWidget.updateCameraSource(devicePosition, lensType);
        }
        if (autoExposureLockWidget.getVisibility() == View.VISIBLE) {
            autoExposureLockWidget.updateCameraSource(devicePosition, lensType);
        }
        if (cameraControlsWidget.getVisibility() == View.VISIBLE) {
            cameraControlsWidget.updateCameraSource(devicePosition, lensType);
        }

        if (thermalPaletteWidget.getVisibility() == View.VISIBLE) {
            thermalPaletteWidget.updateCameraSource(devicePosition, lensType);
        }
        if (visualCameraPanel.getVisibility() == View.VISIBLE) {
            visualCameraPanel.updateCameraSource(devicePosition, lensType);
        }
        if (binding.zoomPanel.getVisibility() == View.VISIBLE) {
            binding.zoomPanel.updateCameraSource(devicePosition, lensType);
        }
        if (binding.thermalZoomPanel.getVisibility() == View.VISIBLE) {
            binding.thermalZoomPanel.updateCameraSource(devicePosition, lensType);
        }
    }

    private void updateViewVisibility(ComponentIndexType devicePosition, CameraLensType lensType) {
        //fpv下不显示
        focusModeWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        focusExposureSwitchWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        cameraControlsWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        autoExposureLockWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);

        thermalPaletteWidget.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        thermalPaletteWidget.setVisibility(lensType == CameraLensType.CAMERA_LENS_THERMAL ? View.VISIBLE : View.INVISIBLE);

        visualCameraPanel.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);

        binding.constraintCamera.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        binding.constraintCamera.setVisibility(lensType == CameraLensType.CAMERA_LENS_ZOOM ? View.VISIBLE : View.GONE);

        binding.ivManual.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.M300_RTK || DJIAircraftApplication.getInstance().getProductType() == ProductType.M350_RTK) {
            binding.btnSpotlight.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
            binding.btnAutoSensing.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        }
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.DJI_MATRICE_4_SERIES || DJIAircraftApplication.getInstance().getProductType() == ProductType.DJI_MATRICE_4D_SERIES) {
            binding.btnSearchLight.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
            binding.btnSpeaker.setVisibility(CameraUtil.isFPVTypeView(devicePosition)? View.INVISIBLE : View.VISIBLE);
        }
//        binding.btnRealtimeUpload.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
        binding.ivNaviToAircraft.setVisibility(CameraUtil.isFPVTypeView(devicePosition) ? View.INVISIBLE : View.VISIBLE);
    }

    /**
     * Helper method to resize the FPV and Map Widgets.
     *
     * @param viewToEnlarge The view that needs to be enlarged to full screen.
     * @param viewToShrink  The view that needs to be shrunk to a thumbnail.
     */
    private void resizeViews(View viewToEnlarge, View viewToShrink) {
        //enlarge first widget
        ResizeAnimation enlargeAnimation = new ResizeAnimation(viewToEnlarge, widgetWidth, widgetHeight, deviceWidth, deviceHeight, 0);
        viewToEnlarge.startAnimation(enlargeAnimation);

        //shrink second widget
        ResizeAnimation shrinkAnimation = new ResizeAnimation(viewToShrink, deviceWidth, deviceHeight, widgetWidth, widgetHeight, widgetMargin);
        viewToShrink.startAnimation(shrinkAnimation);
    }

    private void removeChannelStateListener() {
        IVideoChannel primaryChannel =
                MediaDataCenter.getInstance().getVideoStreamManager().getAvailableVideoChannel(VideoChannelType.PRIMARY_STREAM_CHANNEL);
        IVideoChannel secondaryChannel =
                MediaDataCenter.getInstance().getVideoStreamManager().getAvailableVideoChannel(VideoChannelType.SECONDARY_STREAM_CHANNEL);
        if (primaryChannel != null) {
            primaryChannel.removeVideoChannelStateChangeListener(primaryChannelStateListener);
        }
        if (secondaryChannel != null) {
            secondaryChannel.removeVideoChannelStateChangeListener(secondaryChannelStateListener);
        }
    }

    /**
     * Swap the video sources of the FPV and secondary FPV widgets.
     */
    private void swapVideoSource() {
        Log.e(TAG, "swapVideoSource called");
        ComponentIndexType primarySource = primaryFpvWidget.getWidgetModel().getCameraIndex();
        ComponentIndexType secondarySource = secondaryFPVWidget.getWidgetModel().getCameraIndex();
        //两个source都存在的情况下才进行切换
        if (primarySource != ComponentIndexType.UNKNOWN && secondarySource != ComponentIndexType.UNKNOWN) {
            primaryFpvWidget.updateVideoSource(secondarySource);
            secondaryFPVWidget.updateVideoSource(primarySource);

            isFPVNow = !isFPVNow;
            binding.cameraMode.setVisibility(isFPVNow ? View.GONE : View.VISIBLE);
            if (isFPVNow) {
                currentCameraMode = binding.tvMode.getText().toString();
            }
            binding.tvMode.setText(isFPVNow ? "FPV" : currentCameraMode);
        }
    }

    private void updateInteractionEnabled() {
        fpvInteractionWidget.setInteractionEnabled(primaryFpvWidget.getWidgetModel().getCameraIndex() != ComponentIndexType.FPV);
    }

    /**
     * Animation to change the size of a view.
     */
    private static class ResizeAnimation extends Animation {

        private static final int DURATION = 300;

        private View view;
        private int toHeight;
        private int fromHeight;
        private int toWidth;
        private int fromWidth;
        private int margin;

        private ResizeAnimation(View v, int fromWidth, int fromHeight, int toWidth, int toHeight, int margin) {
            this.toHeight = toHeight;
            this.toWidth = toWidth;
            this.fromHeight = fromHeight;
            this.fromWidth = fromWidth;
            view = v;
            this.margin = margin;
            setDuration(DURATION);
        }

        @Override
        protected void applyTransformation(float interpolatedTime, Transformation t) {
            float height = (toHeight - fromHeight) * interpolatedTime + fromHeight;
            float width = (toWidth - fromWidth) * interpolatedTime + fromWidth;
            ConstraintLayout.LayoutParams p = (ConstraintLayout.LayoutParams) view.getLayoutParams();
            p.height = (int) height;
            p.width = (int) width;
            p.rightMargin = margin;
            p.bottomMargin = margin;
            view.requestLayout();
        }
    }

    private static class CameraSource {
        ComponentIndexType devicePosition;
        CameraLensType lensType;

        public CameraSource(ComponentIndexType devicePosition, CameraLensType lensType) {
            this.devicePosition = devicePosition;
            this.lensType = lensType;
        }
    }

    private void fullScreenImmersive(View view) {
        int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        view.setSystemUiVisibility(uiOptions);
    }

    @SuppressLint("NewApi")
    private void getManualZoomRange() {
        // Step 1: 获取最大值
        CompletableFuture<Void> maxValueFuture = new CompletableFuture<>();
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusRingMaxValue), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {

            @Override
            public void onSuccess(Integer newValue) {
//                KLog.e(TAG, "当前手动对焦最大值---->onValueChange: " + newValue);
                if (newValue != null) {
                    manualMaxValue = newValue;
                    maxValueFuture.complete(null);
                } else {
                    maxValueFuture.completeExceptionally(new RuntimeException("Failed to get max value"));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦最大值---->onFailure: " + idjiError.description());
            }
        });

        // Step 2: 获取最小值
        CompletableFuture<Void> minValueFuture = new CompletableFuture<>();
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusRingMinValue), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {

            @Override
            public void onSuccess(Integer newValue) {
//                KLog.e(TAG, "当前手动对焦最小值---->onValueChange: " + newValue);
                if (newValue != null) {
                    manualMinValue = newValue;
                    minValueFuture.complete(null);
                } else {
                    minValueFuture.completeExceptionally(new RuntimeException("Failed to get min value"));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦最小值---->onFailure: " + idjiError.description());
            }
        });

        // 等待两个值都获取到后，设置范围并获取当前值
        maxValueFuture.thenCombine(minValueFuture, (v1, v2) -> {
                    runOnUiThread(() -> {
                        if (manualMinValue >= 0 && manualMaxValue >= 0) {
                            binding.manualZoomWidget.setRange(manualMinValue, manualMaxValue);
                        }
                    });
                    return null;
                }).thenRun(this::getManualCurrentValue)
                .exceptionally(ex -> {
                    XLogUtil.INSTANCE.e(TAG, "无法获取最小值或最大值" + ex);
                    return null;
                });
    }

    private void getManualCurrentValue() {
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusRingValue), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {
            @Override
            public void onSuccess(Integer value) {
//                KLog.e(TAG, "当前手动对焦值---->onSuccess: " + value);
                if (value != null) {
                    binding.manualZoomWidget.setCurrentValue(value);
                }
            }
            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "获取手动对焦值失败---->onFailure: " + idjiError.description());
            }
        });
    }

    private void updateManualUI() {
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraFocusMode), new CommonCallbacks.CompletionCallbackWithParam<CameraFocusMode>() {
                    @Override
                    public void onSuccess(CameraFocusMode cameraFocusMode) {
                        if (cameraFocusMode != null) {
                            if (cameraFocusMode == CameraFocusMode.MANUAL && DJIAircraftApplication.getInstance().getZoom() >= 7.0) {
                                binding.manualZoomWidget.setVisibility(View.VISIBLE);
                            } else {
                                binding.manualZoomWidget.setVisibility(View.GONE);
                            }
                        }
                    }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {

            }
        });
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraFocusMode), this, (cameraFocusMode, newValue) -> {
            if (newValue != null) {
                if (newValue == CameraFocusMode.MANUAL && DJIAircraftApplication.getInstance().getZoom() >= 7.0) {
                    binding.manualZoomWidget.setVisibility(View.VISIBLE);
                } else {
                    binding.manualZoomWidget.setVisibility(View.GONE);
                }
            }
        });
    }

    //手动设置当前变焦值
    private void setManualValue() {
        binding.manualZoomWidget.setOnValueChangeListener(value -> KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyCameraFocusRingValue, lastDevicePosition, CameraLensType.CAMERA_LENS_ZOOM), value, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
//                KLog.e(TAG, "手动设置变焦成功---->onSuccess: ");
            }
            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.e(TAG, "手动设置变焦失败---->onFailure: " + error.description());
            }
        }));
    }

    //指点对焦
    private void setTapZoomAtTarget(ZoomTargetPointInfo zoomTargetPointInfo) {
        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyTapZoomAtTarget), zoomTargetPointInfo, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                XLogUtil.INSTANCE.e(TAG, "指点对焦成功---->onSuccess: ");
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e(TAG, "指点对焦失败---->onFailure: " + idjiError);
            }
        });
    }

    //触发对焦监听
    private void setTriggerFocus(View view, int action, float x, float y) {
        long downTime = SystemClock.uptimeMillis();
        long eventTime = SystemClock.uptimeMillis();
        MotionEvent motionEvent = MotionEvent.obtain(downTime, eventTime, action, x, y, 0);
        view.dispatchTouchEvent(motionEvent);
    }

    private void updateSensorDisplay(SensorData sensorData) {
        binding.tvCo2Value.setText("CO2：" + String.format("%.2f", sensorData.getCo2Level()));
        binding.tvHumidityValue.setText("湿度：" + String.format("%.2f", sensorData.getHumidity()));
        binding.tvTempValue.setText("温度：" + String.format("%.2f", sensorData.getTemperature()));
        binding.tvPressureValue.setText("压力：" + String.format("%.2f", sensorData.getPressure()));
        if (sensorData.getCo2Level() < 380) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#0E29CC"));
        } else if(sensorData.getCo2Level() < 400) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#08FF99"));
        } else if(sensorData.getCo2Level() < 425) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#66FF33"));
        } else if(sensorData.getCo2Level() < 450) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#99FF33"));
        } else if(sensorData.getCo2Level() < 475) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#CBFF33"));
        } else if(sensorData.getCo2Level() < 500) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#FEFF00"));
        } else if(sensorData.getCo2Level() < 550) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#FE9A33"));
        } else if(sensorData.getCo2Level() < 600) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#FE6700"));
        } else if(sensorData.getCo2Level() < 650) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#FE0200"));
        } else if(sensorData.getCo2Level() < 700) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#CB0100"));
        } else if(sensorData.getCo2Level() < 750) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#A40021"));
        } else if(sensorData.getCo2Level() < 800) {
            binding.tvCo2Value.setTextColor(Color.parseColor("#800000"));
        } else {
            binding.tvCo2Value.setTextColor(Color.parseColor("#6b011a"));
        }
    }

    /**
     * 将屏幕X坐标转换为视频流相对坐标
     * @param screenX 屏幕X坐标
     * @param viewWidth 视图宽度
     * @param actualWidth 实际视频流宽度
     * @param scaleRatioX X轴缩放比例
     * @param offsetX X轴偏移量
     * @return 视频流相对坐标的X值
     */
    private double screenToVideoX(float screenX, float viewWidth, float actualWidth, float scaleRatioX, float offsetX) {
        if (scaleRatioX <= 0 || actualWidth <= 0) {
            return screenX / viewWidth;
        }
        // 减去偏移量，然后除以缩放比例和实际宽度
        return (screenX - offsetX) / (scaleRatioX * actualWidth);
    }

    /**
     * 将屏幕Y坐标转换为视频流相对坐标
     * @param screenY 屏幕Y坐标
     * @param viewHeight 视图高度
     * @param actualHeight 实际视频流高度
     * @param scaleRatioY Y轴缩放比例
     * @param offsetY Y轴偏移量
     * @return 视频流相对坐标的Y值
     */
    private double screenToVideoY(float screenY, float viewHeight, float actualHeight, float scaleRatioY, float offsetY) {
        if (scaleRatioY <= 0 || actualHeight <= 0) {
            return screenY / viewHeight;
        }
        // 减去偏移量，然后除以缩放比例和实际高度
        return (screenY - offsetY) / (scaleRatioY * actualHeight);
    }
}
