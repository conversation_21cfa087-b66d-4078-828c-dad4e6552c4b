package dji.sampleV5.aircraft.view.adapter;

import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.view.BatteryEnergyView;
import dji.sampleV5.aircraft.view.lineView;

public class ViewHolder extends RecyclerView.ViewHolder {
    private SparseArray<View> mViews;
    public  View mConvertView;

    public ViewHolder(Context context, View itemView, ViewGroup parent) {
        super(itemView);
        mConvertView = itemView;
        mViews = new SparseArray<>();
    }

    public static ViewHolder get(Context context, ViewGroup parent, int layoutId) {
        View itemView = LayoutInflater.from(context).inflate(layoutId, parent, false);
        return new ViewHolder(context, itemView, parent);
    }


    /**
     * 通过viewId获取控件
     *
     */
    public <T extends View> T getView(int viewId) {
        View view = mViews.get(viewId);
        if (view == null) {
            view = mConvertView.findViewById(viewId);
            mViews.put(viewId, view);
        }
        return (T) view;
    }

    public ViewHolder setText(int viewId, String text) {
        TextView tv = getView(viewId);
        tv.setText(text);
        return this;
    }
    public void setPercent(int viewId, final float Power) {
         BatteryEnergyView tv = getView(viewId);
         tv.getPercent(Power);
    }

    public void setPower(int viewId, final int Power, final boolean b) {
        final lineView tv = getView(viewId);
        ContextUtil.getHandler().post(() -> tv.setPower(Power,b));
    }

    public ViewHolder setImageResource(int viewId, int resId) {
        ImageView view = getView(viewId);
        view.setImageResource(resId);
        return this;
    }

    public void setImageVisible(int viewId, boolean visible) {
        ImageView view = getView(viewId);
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
    }

    public void setImageBackgroundResource(int viewId, int resId) {
        ImageView view = getView(viewId);
        view.setBackgroundResource(resId);
    }

    public void setTextHide(int viewId, boolean hide){
        TextView view = getView(viewId);
        if (hide){
            view.setVisibility(View.VISIBLE);
        }else {
            view.setVisibility(View.GONE);
        }
    }

    public ViewHolder setImageResource(int viewId, BitmapDrawable bitmap) {
        ImageView view = getView(viewId);
        view.setBackground(bitmap);
        return this;
    }

    public void setImageTag(int viewId, String tag){
        ImageView view = getView(viewId);
        view.setTag(tag);
    }

    public String getImageTag(int viewId){
        ImageView view = getView(viewId);
        return (String) view.getTag();
    }


    public ViewHolder setOnClickListener(int viewId, View.OnClickListener listener) {
        View view = getView(viewId);
        view.setOnClickListener(listener);
        return this;
    }

    public ViewHolder setText(int viewId, String text, boolean b) {
        TextView tv = getView(viewId);
        if (b){
            tv.setTextColor(ContextUtil.getColor(R.color.blue));
        }else {
            tv.setTextColor(ContextUtil.getColor(R.color.white));
        }
        tv.setText(text);
        return this;
    }
}
