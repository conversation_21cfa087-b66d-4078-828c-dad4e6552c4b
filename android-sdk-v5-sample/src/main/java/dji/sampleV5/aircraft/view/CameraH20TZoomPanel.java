package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.util.FormatUtil;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.key.RemoteControllerKey;
import dji.sdk.keyvalue.value.common.CameraLensType;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.ux.core.base.widget.ConstraintLayoutWidget;
import dji.sampleV5.aircraft.R;


public class CameraH20TZoomPanel extends ConstraintLayoutWidget {

    private ComponentIndexType cameraIndex = ComponentIndexType.LEFT_OR_MAIN;
    private CameraLensType  lensType = CameraLensType.CAMERA_LENS_ZOOM;

    private TextView tvZoomT;
    private TextView tvZoomW;
    private TextView tvZoomR;
    private TextView tvZoomNum;
    private double ratios = 2;
    private double defaltRatios = 2;

    /*M300 H20T：变焦2倍，红外1倍
    M300 H30T：变焦2倍，红外2倍
    M30T：变焦2倍 红外2倍
    M3T：变焦1倍 红外1倍
    M3TD：变焦1倍 红外1倍*/
    private Handler handler = new Handler();
    private Handler uiHandle = new Handler();

    public void setRatios(double ratios) {
        this.ratios = ratios;
        setZoomNumText();
    }

    public double getRatios() {
        return ratios;
    }

    public void updateCameraSource(@NonNull ComponentIndexType cameraIndex, @NonNull CameraLensType lensType) {
        this.cameraIndex = cameraIndex;
        this.lensType = lensType;
    }

    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
            updateH20THybridZoomNum();
            handler.postDelayed(this, 100);
        }
    };

    public CameraH20TZoomPanel(@NonNull Context context) {
        super(context);
    }

    public CameraH20TZoomPanel(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CameraH20TZoomPanel(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void initView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        View view = inflate(context, R.layout.ext_panel_h20t_zoom, this);
        tvZoomT = view.findViewById(R.id.tv_zoom_t);
        tvZoomW = view.findViewById(R.id.tv_zoom_w);
        tvZoomR = view.findViewById(R.id.tv_zoom_r);
        tvZoomNum = view.findViewById(R.id.tv_zoom_number);
        ProductType productType = DJIAircraftApplication.getInstance().productType;
        if(productType == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES /*|| productType == ProductType.M30_SERIES*/){
            Log.e("TAG", "initView: "+productType.name());
            defaltRatios = 1;
            ratios = 1;
        }
        addH20TClickListener();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        KeyManager.getInstance().listen(KeyTools.createKey(RemoteControllerKey.KeyScrollWheel), this, (aDouble, newValue) -> {
            if (newValue != null) {
                if (newValue < 0) {
                    // 缩小逻辑
                    double step = ratios <= 5 ? 0.6 : 1.0;
                    ratios = ratios - step * Math.abs(newValue);
                    ratios = Math.max(ratios, defaltRatios);
                    setZoom();
                } else if (newValue > 0) {
                    // 放大逻辑
                    double step = ratios < 5 ? 0.6 : 1.0;
                    ratios = ratios + step * newValue;
                    ratios = Math.min(ratios, 28);
                    setZoom();
                }
                XLogUtil.INSTANCE.d("TAG", "initView: "+newValue + "    当前变焦倍数:" + ratios);
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        KeyManager.getInstance().cancelListen(this);
    }

    @Override
    protected void reactToModelChanges() {

    }

    @NonNull
    @Override
    public String getIdealDimensionRatioString() {
        return null;
    }


    public void updateH20THybridZoomNum() {
        KeyManager.getInstance().getValue(KeyTools.createCameraKey(CameraKey.KeyCameraZoomRatios, cameraIndex, lensType), new CommonCallbacks.CompletionCallbackWithParam<Double>() {
            @Override
            public void onSuccess(Double value) {
                if (value != null) {
                    ratios = value;
                    setZoomNumText();
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
    }

    public void tapZoom(){
        ratios += 2;
        setZoom();
    }

    public void setZoom() {
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyCameraZoomRatios, cameraIndex, lensType), ratios, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                setZoomNumText();
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
    }

    public void initZoom() {
        ratios = defaltRatios;
        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyCameraZoomRatios, cameraIndex, lensType), ratios, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                setZoomNumText();
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });

        KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalZoomRatios, cameraIndex, lensType), ratios, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (error != null) {
                    Log.e("KeyCameraZoomRatios", "onFailure: " + error.description());
                }

            }
        });
    }

    private void setZoomNumText() {
        String value = FormatUtil.getDoubleByFormat(ratios, 1) + "x";
        tvZoomNum.setText(value);
    }

    private void addH20TClickListener() {
        tvZoomT.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ratios = (int) (ratios / 2);
                ratios = ratios < defaltRatios ? defaltRatios : ratios;
                setZoom();
            }
        });
        tvZoomW.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ratios = (int) (ratios * 2);
                ratios = ratios > 400 ? 400 : ratios;
                setZoom();
            }
        });
        tvZoomR.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ratios = defaltRatios;
                setZoom();
            }
        });
        //放大聚焦
        /*tvZoomT.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    tvZoomT.setPressed(true);
                    //handler.postDelayed(runnable, 100);
                    //zoomLens.startContinuousOpticalZoom(SettingsDefinitions.ZoomDirection.ZOOM_IN, SettingsDefinitions.ZoomSpeed.NORMAL, null);
                    break;
                case MotionEvent.ACTION_MOVE:
                    tvZoomT.setPressed(true);
                    break;
                case MotionEvent.ACTION_UP:
                    tvZoomT.performClick();
                    tvZoomT.setPressed(false);
                    //zoomLens.stopContinuousOpticalZoom(null);
                    //handler.removeCallbacks(runnable);
                    //updateH20THybridZoomNum();
                    break;
                default:
                    break;
            }
            return true;
        });

        //缩小聚焦
        tvZoomW.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    //handler.postDelayed(runnable, 100);
                    tvZoomW.setPressed(true);
                    //zoomLens.startContinuousOpticalZoom(SettingsDefinitions.ZoomDirection.ZOOM_OUT, SettingsDefinitions.ZoomSpeed.NORMAL, null);
                    break;
                case MotionEvent.ACTION_MOVE:
                    tvZoomW.setPressed(true);
                    break;
                case MotionEvent.ACTION_UP:
                    tvZoomW.performClick();
                    tvZoomW.setPressed(false);
                    //zoomLens.stopContinuousOpticalZoom(null);
                    //handler.removeCallbacks(runnable);
                    //updateH20THybridZoomNum();
                    break;

            }
            return true;
        });*/

        //恢复初始值
        /*tvZoomR.setOnClickListener(v -> zoomLens.getHybridZoomSpec(new CommonCallbacks.CompletionCallbackWith<SettingsDefinitions.HybridZoomSpec>() {
            @Override
            public void onSuccess(SettingsDefinitions.HybridZoomSpec hybridZoomSpec) {
                int minFocalLength = hybridZoomSpec.getMinHybridFocalLength();
                zoomLens.setHybridZoomFocalLength(minFocalLength, djiError -> {
                    if (djiError == null) {
                        uiHandle.post(() -> tvZoomNum.setText("1x"));
                    }
                });


            }

            @Override
            public void onFailure(DJIError djiError) {
            }
        }));*/
    }


}
