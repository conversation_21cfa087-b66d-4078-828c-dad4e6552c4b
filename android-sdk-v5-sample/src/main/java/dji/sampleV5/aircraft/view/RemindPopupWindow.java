package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;


public class RemindPopupWindow extends LinearLayout {
    private boolean isHasViewGroup = true;

    public RemindPopupWindow(Context context) {
        this(context, null);
    }

    public RemindPopupWindow(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public RemindPopupWindow(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void addWarnView(@StringRes int id) {
        addWarnView(ContextUtil.getString(id));
    }

    public void addWarnView(String text) {
        final View view = LayoutInflater.from(getContext()).inflate(R.layout.popupwindow_left_text_warn, null);
        TextView pop_left_text_warn = view.findViewById(R.id.pop_left_text_warn);
        pop_left_text_warn.setText(text);
        addTranslateAnimation(view);
        this.post(() -> RemindPopupWindow.this.addView(view, 0));

        RemindPopupWindow.this.postDelayed(() -> RemindPopupWindow.this.removeView(view), 4000);
    }

    public void addWaringViewWithCloseButton(@StringRes int id, int color) {
        final View view = LayoutInflater.from(getContext()).inflate(R.layout.popupwindow_left, null);
        TextView tvWarning = view.findViewById(R.id.tv_popupwindow_left);
        tvWarning.setText(id);
        if (color != -1) {
            tvWarning.setTextColor(ContextUtil.getColor(color));
        }
        TextView tvCancel = view.findViewById(R.id.tv_popupwindow_cancel);
        tvCancel.setOnClickListener(view1 -> RemindPopupWindow.this.post(() -> {
            RemindPopupWindow.this.removeView(view);
            isHasViewGroup = true;
        }));
        addTranslateAnimation(view);
        if (isHasViewGroup) {
            this.post(() -> {
                RemindPopupWindow.this.addView(view, 0);
                isHasViewGroup = false;
            });
        }
    }

    public void addRemindingView(@StringRes int id) {
        addRemindingView(getResources().getString(id), false);
    }

    public void addRemindingView(String text, boolean isWarning) {
        final View view = LayoutInflater.from(getContext()).inflate(R.layout.popupwindow_left_text, null);
        TextView tvRemind = view.findViewById(R.id.pop_left_text);
        tvRemind.setText(text);
        if (isWarning) {
            tvRemind.setTextColor(Color.RED);
            view.findViewById(R.id.image_icon).setBackgroundResource(R.drawable.ic_check_warning_mark);
        }
        addTranslateAnimation(view);
        this.post(() -> RemindPopupWindow.this.addView(view, 0));

        RemindPopupWindow.this.postDelayed(() -> RemindPopupWindow.this.removeView(view), 3000);
    }

    public void addRemindingView(String text, int level) {
        final View view = LayoutInflater.from(getContext()).inflate(R.layout.popupwindow_left_text, null);
        TextView tvRemind = view.findViewById(R.id.pop_left_text);
        tvRemind.setText(text);
        if (level == 1) {
            tvRemind.setTextColor(Color.parseColor("#FF524D"));
            view.findViewById(R.id.image_icon).setBackgroundResource(R.drawable.ic_waring_triangle_red);
        } else {
            tvRemind.setTextColor(Color.parseColor("#FFBA4E"));
            view.findViewById(R.id.image_icon).setBackgroundResource(R.drawable.ic_waring_triangle_yellow);
        }
        addTranslateAnimation(view);
        this.post(() -> RemindPopupWindow.this.addView(view, 0));

        RemindPopupWindow.this.postDelayed(() -> RemindPopupWindow.this.removeView(view), 3000);
    }

    public void addRemindingView(@StringRes int id, boolean isWarning) {
        addRemindingView(getResources().getString(id), isWarning);
    }

    public void addItemView(String text, NotificationType color) {
        final TextView textView = new TextView(ContextUtil.getApplicationContext());
        textView.setText(text);
        textView.setTextColor(color.getColorCode());
        textView.setBackgroundColor(ContextUtil.getColor(R.color.transparent_black));
        textView.setGravity(Gravity.CENTER);
        addTranslateAnimation(textView);
        this.post(() -> RemindPopupWindow.this.addView(textView, 0));
        RemindPopupWindow.this.postDelayed(() -> RemindPopupWindow.this.removeView(textView), 3000);
    }

    private void addTranslateAnimation(View view) {
        TranslateAnimation ta = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, -1f, Animation.RELATIVE_TO_SELF, 0,
                Animation.RELATIVE_TO_SELF, 0, Animation.RELATIVE_TO_SELF, 0);
        // 动画播放的时间长度
        ta.setDuration(500);

        // 设置重复播放的模式
        ta.setRepeatMode(Animation.REVERSE);
        // 让iv播放aa动画
        view.startAnimation(ta);
    }
}
