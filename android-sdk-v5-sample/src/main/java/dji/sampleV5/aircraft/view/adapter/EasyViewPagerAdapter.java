package dji.sampleV5.aircraft.view.adapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;

import java.util.List;


public abstract class EasyViewPagerAdapter<T> extends PagerAdapter{
    private final List<T> mList;

    public EasyViewPagerAdapter(List<T> mList) {
        this.mList=mList;
    }

    @Override
    public int getItemPosition(Object object) {
        return PagerAdapter.POSITION_NONE;
    }

    @Override
    public int getCount() {
        return mList.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        T t = mList.get(position);
        View view = getView(t,position);
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View) object);
    }

    @Override
    public void setPrimaryItem(ViewGroup container, int position, Object object) {
        setPrimaryItemData(container,position);
    }

    public abstract  View  getView(T t, int position);
    public abstract void setPrimaryItemData(ViewGroup container, int position);
}
