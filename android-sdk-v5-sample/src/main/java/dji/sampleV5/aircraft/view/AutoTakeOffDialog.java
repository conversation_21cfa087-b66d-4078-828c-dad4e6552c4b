package dji.sampleV5.aircraft.view;

import android.app.Dialog;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.fly.controller.PopupWindowController;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.common.EmptyMsg;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;

import java.util.Objects;


public class AutoTakeOffDialog implements View.OnClickListener, SlideLockView.OnLockListener {

    private Dialog dialog;

    public AutoTakeOffDialog(boolean autoFly) {

        dialog = new Dialog(Objects.requireNonNull(ContextUtil.getCurrentActivity()), R.style.AddPlanDialog);

        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.auto_take_off_dialog, null);
        SlideLockView slideLockView = view.findViewById(R.id.sb_slide_lock_view_take_off);
        ImageView cancel = view.findViewById(R.id.cancel);

        slideLockView.setmLockListener(this);
        cancel.setOnClickListener(this);

        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);

        ImmerseUtil.showDialog(dialog);

       /* if (autoFly) {
            ContextUtil.getHandler().postDelayed(this::onOpenLockSuccess, 5000);
        }*/
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.cancel) {
            dialog.dismiss();
        }
    }


    @Override
    public void onOpenLockSuccess() {
        dialog.dismiss();
        KeyManager.getInstance().performAction(KeyTools.createKey(FlightControllerKey.KeyStartTakeoff), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                RemindPopupWindow left = PopupWindowController.getInstance().getPopupWindowFromLeft();
                if (left != null) {
                    left.addRemindingView(R.string.start_take_off);
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                RemindPopupWindow left = PopupWindowController.getInstance().getPopupWindowFromLeft();
                if (left != null) {
                    left.addItemView(ContextUtil.getString(R.string.take_off_failed),
                            NotificationType.ERROR);
                }
            }
        });
    }
}
