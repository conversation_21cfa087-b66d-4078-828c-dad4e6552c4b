package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ArrayAdapter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.R;

public class D<PERSON>pinner extends androidx.appcompat.widget.AppCompatSpinner {


    private String[] array;

    public DWSpinner(Context context) {
        super(context);
        initView(context, null, 0);
    }

    public DWSpinner(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView(context, attrs, 0);
    }


    public DWSpinner(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs, defStyleAttr);
    }

    public void initView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {

        if (attrs != null) {
            initAttributes(context, attrs);
        }

        setBackgroundResource(R.drawable.bg_customer_spinner);

        if (array != null) {
            ArrayAdapter<String> adapter = new ArrayAdapter<>(getContext(), R.layout.spinner_customer_text);
            for (String s : array) {
                adapter.add(s);
            }
            setAdapter(adapter);
        }

        setPopupBackgroundResource(R.drawable.bg_customer_pop_spinner);

        getViewTreeObserver().addOnGlobalLayoutListener(() -> setDropDownVerticalOffset(getMeasuredHeight() + 10));
    }

    public void setData(String[] myArray){
        if (myArray != null) {
            ArrayAdapter<String> adapter = new ArrayAdapter<>(getContext(), R.layout.spinner_customer_text);
            for (String s : myArray) {
                adapter.add(s);
            }
            setAdapter(adapter);
        }
    }

    private void initAttributes(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.customer_spinner);
        if (typedArray.hasValue(R.styleable.customer_spinner_android_entries)) {
            int array = typedArray.getResourceId(R.styleable.customer_spinner_android_entries, 0);
            this.array = getResources().getStringArray(array);
        }
        typedArray.recycle();
    }
}
