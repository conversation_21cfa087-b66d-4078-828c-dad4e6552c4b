package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;

/**
 * This is a base class for widgets requiring LinearLayout.
 */
public abstract class LinearLayoutWidget extends LinearLayout {
    //region Constants
    protected static final String TAG = "LinearLayoutWidget";
    protected static final int INVALID_RESOURCE = -1;
    protected static final int INVALID_COLOR = 0;
    protected static final float INVALID_DIMENSION = 0f;
    //endregion
    //region Fields
    private CompositeDisposable reactionDisposables;
    private CompositeDisposable compositeDisposable;
    //endregion

    //region Constructors
    public LinearLayoutWidget(@NonNull Context context) {
        super(context);
        initView(context, null, 0);
    }

    public LinearLayoutWidget(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context, attrs, 0);
    }

    public LinearLayoutWidget(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs, defStyleAttr);
    }
    //endregion

    //region Lifecycle
    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (isInEditMode()) {
            return;
        }
        reactionDisposables = new CompositeDisposable();
        compositeDisposable = new CompositeDisposable();
        reactToModelChanges();
    }

    @Override
    protected void onDetachedFromWindow() {
        unregisterReactions();
        disposeAll();
        super.onDetachedFromWindow();
    }

    /**
     * Invoked after the view is created, during the initialization of the class.
     * FindViewById load, AttributeSet in here.
     *
     * @param context      Context
     * @param attrs        Attribute set
     * @param defStyleAttr Style attribute
     */
    protected abstract void initView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr);

    /**
     * Call addReaction here to bind to the model.
     */
    protected abstract void reactToModelChanges();

    /**
     * Add a disposable which is automatically disposed with the view's lifecycle.
     *
     * @param disposable the disposable to add
     */
    protected void addDisposable(@NonNull Disposable disposable) {
        if (compositeDisposable != null) {
            compositeDisposable.add(disposable);
        }
    }
    //endregion

    //region Customization

    /**
     * Ideal dimension ratio in the format width:height.
     *
     * @return dimension ratio string.
     */
    @NonNull
    public abstract String getIdealDimensionRatioString();
    //endregion

    //region Reactions

    /**
     * Add a reaction which is automatically disposed with the view's lifecycle.
     *
     * @param reaction the reaction to add.
     */
    protected void addReaction(@NonNull Disposable reaction) {
        if (reactionDisposables == null) {
            throw new IllegalStateException("Called this method only from reactToModelChanges.");
        }

        reactionDisposables.add(reaction);
    }

    private void unregisterReactions() {
        if (reactionDisposables != null) {
            reactionDisposables.dispose();
            reactionDisposables = null;
        }
    }

    private void disposeAll() {
        if (compositeDisposable != null) {
            compositeDisposable.dispose();
            compositeDisposable = null;
        }
    }
    //endregion

    //region Helpers


}