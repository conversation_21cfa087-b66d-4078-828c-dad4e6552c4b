package dji.sampleV5.aircraft.view.task;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yc.video.controller.BaseVideoController;
import com.yc.video.player.InterVideoPlayer;


public class TaskInfoController extends BaseVideoController   {




    public TaskInfoController(@NonNull Context context) {
        super(context);

    }

    public TaskInfoController(@NonNull Context context, @Nullable AttributeSet attrs) {
        //创建
        super(context, attrs, 0);

    }

    public TaskInfoController(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }





    @Override
    protected int getLayoutId() {
        return 0;
    }

    @Override
    public void startProgress() {
        super.startProgress();

    }

    @Override
    public void togglePlay() {
        super.togglePlay();

    }

    @Override
    public void destroy() {

    }

    @Override
    protected void setProgress(int duration, int position) {
        super.setProgress(duration, position);

    }

    @Override
    public void setMediaPlayer(InterVideoPlayer mediaPlayer) {
        super.setMediaPlayer(mediaPlayer);

    }

    @Override
    public void onWindowFocusChanged(boolean hasWindowFocus) {

    }




}
