package dji.sampleV5.aircraft.view;

import android.animation.ValueAnimator;
import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Build;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.RequiresApi;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;


public class SlideLockView extends View {
    private Bitmap mLockBitmap;
    private int mLockDrawableId;
    private Paint mPaint;
    private int mLockRadius;
    private String mTipText;
    private int mTipsTextSize;
    private int mTipsTextColor;
    private Rect mTipsTextRect = new Rect();

    private float mLocationX = 5;
    private boolean mIsDragable = false;
    private OnLockListener mLockListener;
    private int width1;
    private int picHeight, picwidth;

    public SlideLockView(Context context) {
        this(context, null);

    }

    public SlideLockView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SlideLockView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray tp = context.obtainStyledAttributes(attrs, R.styleable.SlideLockView, defStyleAttr, 0);
        mLockDrawableId = tp.getResourceId(R.styleable.SlideLockView_lock_drawable, -1);
        mLockRadius = tp.getDimensionPixelOffset(R.styleable.SlideLockView_lock_radius, 1);
        mTipText = tp.getString(R.styleable.SlideLockView_lock_tips_tx);
        mTipsTextSize = tp.getDimensionPixelOffset(R.styleable.SlideLockView_locl_tips_tx_size, 12);
        mTipsTextColor = tp.getColor(R.styleable.SlideLockView_lock_tips_tx_color, Color.BLACK);

        tp.recycle();

        if (mLockDrawableId == -1) {
            throw new RuntimeException("未设置滑动解锁图片");
        }

        init(context);

    }

    private void init(Context context) {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setTextSize(mTipsTextSize);
        mPaint.setColor(mTipsTextColor);

        mLockBitmap = BitmapFactory.decodeResource(context.getResources(), mLockDrawableId);
        picHeight = mLockBitmap.getHeight();
        picwidth = mLockBitmap.getWidth();

        /*int newSize = mLockRadius * 2;
        //按比例缩小
        float scale = newSize * 1.0f / oldSize;*/
        //设置图片的大小
        Matrix matrix = new Matrix();
        matrix.setScale(1, 1.2f);
        mLockBitmap = Bitmap.createBitmap(mLockBitmap, 0, 0, picwidth, picHeight, matrix, true);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        width1 = getWidth();
    }

    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onDraw(Canvas canvas) {
        mPaint.setColor(ContextUtil.getColor(R.color.text_black_111111));
        //画椭圆
        //canvas.drawRoundRect(0,5,width1,mLockRadius * 2-5,90,90,mPaint);
        canvas.drawRect(0, 0, width1, getHeight(), mPaint);

        canvas.getClipBounds(mTipsTextRect);
        int cHeight = mTipsTextRect.height();
        int cWidth = mTipsTextRect.width();
        mPaint.setTextAlign(Paint.Align.LEFT);
        mPaint.getTextBounds(mTipText, 0, mTipText.length(), mTipsTextRect);
        float x = cWidth / 2f - mTipsTextRect.width() / 2f - mTipsTextRect.left;
        float y = cHeight / 2f + mTipsTextRect.height() / 2f - mTipsTextRect.bottom;
        mPaint.setColor(mTipsTextColor);
        //画文字
        canvas.drawText(mTipText, x, y, mPaint);

        int rightMax = getWidth() - mLockRadius * 2;

        if (mLocationX < 0) {
            //画图片
            canvas.drawBitmap(mLockBitmap, 5, 0, mPaint);
        } else if (mLocationX > rightMax) {

            canvas.drawBitmap(mLockBitmap, rightMax, 0, mPaint);
        } else {
            mPaint.setColor(ContextUtil.getColor(R.color.text_green_check));
            //canvas.drawRoundRect(0,5,mLocationX+mLockRadius * 2-5,mLockRadius * 2-5,90,90,mPaint);
            canvas.drawBitmap(mLockBitmap, mLocationX, (getHeight() - (int)(picHeight*1.2)) / 2, mPaint);
        }

    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN: {
                float xPos = event.getX();
                float yPos = event.getY();
                if (isTouchLock(xPos, yPos)) {
                    mLocationX = xPos - mLockRadius;
                    mIsDragable = true;
                    invalidate();
                } else {
                    mIsDragable = false;
                }
                return true;
            }
            case MotionEvent.ACTION_MOVE: {

                if (!mIsDragable) return true;
                //滑动到最右边
                int rightMax = getWidth() - mLockRadius * 2;

                resetLocationX(event.getX(), rightMax);
                invalidate();

                return true;
            }
            case MotionEvent.ACTION_UP: {
                if (!mIsDragable) return true;
                resetLock();
                //滑动到最右边
                int rightMax = getWidth() - mLockRadius * 2;

                if (mLocationX >= rightMax) {
                    mIsDragable = false;
                    mLocationX = 5;
                    invalidate();
                    if (mLockListener != null) {
                        mLockListener.onOpenLockSuccess();
                    }

                }

                break;
            }
        }
        return super.onTouchEvent(event);
    }

    private void resetLock() {
        ValueAnimator anim = ValueAnimator.ofFloat(mLocationX, 5);
        anim.setDuration(300);
        anim.addUpdateListener(valueAnimator -> {
            mLocationX = (Float) valueAnimator.getAnimatedValue();
            invalidate();
        });
        anim.start();
    }

    private void resetLocationX(float eventXPos, float rightMax) {
        //获得当前X位置
        mLocationX = eventXPos - mLockRadius;
        if (mLocationX < 0) {
            mLocationX = 5;
        } else if (mLocationX >= rightMax) {
            mLocationX = rightMax;
        }
    }

    private boolean isTouchLock(float xPos, float yPox) {
        float centerX = mLocationX + mLockRadius;
        float diffX = xPos - centerX;
        float diffY = yPox - mLockRadius;

        return diffX * diffX + diffY * diffY < mLockRadius * mLockRadius;
    }


    public void setmLockListener(OnLockListener mLockListener) {
        this.mLockListener = mLockListener;
    }


    public interface OnLockListener {
        void onOpenLockSuccess();
    }
}
