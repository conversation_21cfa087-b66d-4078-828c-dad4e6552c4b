package dji.sampleV5.aircraft.view.task;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.yc.video.config.ConstantKeys;
import com.yc.video.controller.BaseVideoController;
import com.yc.video.player.InterVideoPlayer;

import dji.sampleV5.aircraft.util.task.MapUtil;



public class TaskInfoPlayer implements InterVideoPlayer {
    private Context mContext;
    private long position = 0;
    private BaseVideoController baseVideoController;
    private boolean isExit = false;
    private float speed = 1;
    private boolean isPause = false;
    private boolean isPlaying = false;
    //相当于video
    private MapUtil mapUtil;
    private long duration;
    private long startTimp;


    public TaskInfoPlayer(@NonNull Context context) {
        super();

    }

    public TaskInfoPlayer(@NonNull Context context, @Nullable AttributeSet attrs) {
        super();
    }

    public TaskInfoPlayer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super();
    }

    public interface VideoImlCallback {
        void start();

        void pause();

        void seekTo(long pos);

        void end();

        void getTime(long pos);
    }

    private VideoImlCallback videoImlCallback;

    public void setVideoImlCallback(VideoImlCallback videoImlCallback) {
        this.videoImlCallback = videoImlCallback;
    }

    public void play() {
        Thread thread = new Thread(mShowProgress);
        thread.start();
    }


    @Override
    public void setUrl(String url) {

    }

    @Override
    public String getUrl() {
        return null;
    }

    @Override
    public void start() {
        baseVideoController.setPlayState(ConstantKeys.CurrentState.STATE_PLAYING);
        baseVideoController.startProgress();
        isPause = false;
        isPlaying = true;
        videoImlCallback.start();

    }


    @Override
    public void pause() {
        baseVideoController.setPlayState(ConstantKeys.CurrentState.STATE_PAUSED);
        isPause = true;
        isPlaying = false;
        videoImlCallback.pause();
    }


    public void setController(BaseVideoController baseVideoController) {
        this.baseVideoController = baseVideoController;
        this.baseVideoController.setMediaPlayer(this);

    }

    public void setExit(boolean isExit) {
        this.isExit = isExit;
    }

    @Override
    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public void setMap(MapUtil mapUtil) {
        this.mapUtil = mapUtil;
    }

    public void setStartTimp(long startTimp) {
        this.startTimp = startTimp;
    }

    @Override
    public long getCurrentPosition() {
        return position;
    }

    @Override
    public void seekTo(long pos) {
        Log.e("TAG", ": "+pos );
        pause();
        videoImlCallback.seekTo(pos);
        position = pos;
        try {
            mapUtil.seekTo(position);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isPlaying() {
        return isPlaying;
    }

    @Override
    public int getBufferedPercentage() {
        return 100;
    }

    @Override
    public void startFullScreen() {

    }

    @Override
    public void stopFullScreen() {

    }

    @Override
    public boolean isFullScreen() {
        return false;
    }

    @Override
    public void setMute(boolean isMute) {

    }

    @Override
    public boolean isMute() {
        return false;
    }

    @Override
    public void setScreenScaleType(int screenScaleType) {

    }

    @Override
    public void setSpeed(float speed) {
        this.speed = speed;
    }

    @Override
    public float getSpeed() {
        return speed;
    }

    @Override
    public long getTcpSpeed() {
        return 0;
    }

    @Override
    public void replay(boolean resetPosition) {

    }

    @Override
    public void setMirrorRotation(boolean enable) {

    }

    @Override
    public Bitmap doScreenShot() {
        return null;
    }

    @Override
    public int[] getVideoSize() {
        return new int[0];
    }

    @Override
    public void setRotation(float rotation) {

    }

    @Override
    public void startTinyScreen() {

    }

    @Override
    public void stopTinyScreen() {

    }

    @Override
    public boolean isTinyScreen() {
        return false;
    }


    protected Runnable mShowProgress = new Runnable() {
        @Override
        public void run() {
            while (!isExit) {
                if (position < getDuration() && !isPause) {
                    position = position + 500;
                    mapUtil.addPoint(position);
                    if (position > duration - 2000) { //解决视频如果结束了不能重复播放的问题
                        //handler.sendEmptyMessage(0);
                        videoImlCallback.end();
                    }
                    videoImlCallback.getTime(position);
                }
                try {
                    Thread.sleep((long) (500 / getSpeed()));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    };

 private Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            // mapUtil.addPoint(startTimp+position);
            videoImlCallback.end();
        }
    };


}
