package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageButton;

import dji.sampleV5.aircraft.R;

public class ProgressImageView extends AppCompatImageButton {
    private int mWidth;
    private int mHeight;
    private Paint mPaint;
    private float percent;
    private RectF rectF = new RectF(3, 3, 0, 0);

    public ProgressImageView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setBackgroundColor(Color.TRANSPARENT);
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeWidth(6);
        mPaint.setColor(getResources().getColor(R.color.colorAccent));
    }

    public void updateProgress(float percent) {
        this.percent = percent;
        invalidate();
    }

    public void backToNormal() {
        this.percent = -1;
        invalidate();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = getWidth();
        mHeight = getHeight();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (percent >= 0) {
            int progress = (int) (this.percent * 360);
            rectF.left = 3;
            rectF.top = 3;
            rectF.right = mWidth - 3;
            rectF.bottom = mHeight - 3;
            canvas.drawArc(rectF, -90, progress, false, mPaint);
        }
    }
}
