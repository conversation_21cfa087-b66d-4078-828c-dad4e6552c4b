/*******************************************************************************
 * Copyright 2011, 2012 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/
package dji.sampleV5.aircraft.view.pullToRefresh.extras;

import android.content.Context;
import android.view.View;

import dji.sampleV5.aircraft.view.pullToRefresh.PullToRefreshBase;
import dji.sampleV5.aircraft.view.pullToRefresh.PullToRefreshBase.Mode;
import dji.sampleV5.aircraft.view.pullToRefresh.PullToRefreshBase.State;

public class SoundPullEventListener<V extends View> implements PullToRefreshBase.OnPullEventListener<V> {

//	private final Context mContext;
//	private final HashMap<State, Integer> mSoundMap;

//	private MediaPlayer mCurrentMediaPlayer;

	/**
	 * Constructor
	 * 
	 * @param context - Context
	 */
	public SoundPullEventListener(Context context) {
//		mContext = context;
//		mSoundMap = new HashMap<State, Integer>();
	}

	@Override
	public final void onPullEvent(PullToRefreshBase<V> refreshView, State event, Mode direction) {
//		Integer soundResIdObj = mSoundMap.get(event);
//		if (null != soundResIdObj) {
//			playSound(soundResIdObj.intValue());
//		}
	}

	/**
	 * Set the Sounds to be played when a Pull Event happens. You specify which
	 * sound plays for which events by calling this method multiple times for
	 * each event.
	 * <p/>
	 * If you've already set a sound for a certain event, and add another sound
	 * for that event, only the new sound will be played.
	 * 
	 * @param event - The event for which the sound will be played.
	 * @param resId - Resource Id of the sound file to be played (e.g.
	 *            <var>R.raw.pull_sound</var>)
	 */
	public void addSoundEvent(State event, int resId) {
//		mSoundMap.put(event, resId);
	}

	/**
	 * Clears all of the previously set sounds and events.
	 */
	public void clearSounds() {
//		mSoundMap.clear();
	}

	/**
	 * Gets the current (or last) MediaPlayer instance.
	 */
//	public MediaPlayer getCurrentMediaPlayer() {
//		return mCurrentMediaPlayer;
//		return null;
//	}

//	private void playSound(int resId) {
//		// Stop current player, if there's one playing
//		if (null != mCurrentMediaPlayer) {
//			mCurrentMediaPlayer.stop();
//			mCurrentMediaPlayer.release();
//		}
//
//		mCurrentMediaPlayer = MediaPlayer.create(mContext, resId);
//		if (null != mCurrentMediaPlayer) {
//			mCurrentMediaPlayer.start();
//		}
//	}

}
