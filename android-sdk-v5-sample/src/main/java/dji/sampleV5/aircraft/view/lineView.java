package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

public class lineView extends View {
    private Paint mPaint = new Paint();
    private int mHeight;
    private int power;

    private boolean flg;

    public lineView(Context context) {
        super(context);
    }

    public lineView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public lineView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        //获取view的宽高
        int mWidth = getWidth();
        mHeight = getHeight();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        mPaint.setAntiAlias(true);
        mPaint.setStrokeWidth(4);
        if (power<=-60&&power>=-110){
        if (flg){
            mPaint.setColor(Color.BLUE);
        }else {
            if (power > -90) {
                mPaint.setColor(Color.RED);
            } else if (power<=-90){
                mPaint.setColor(Color.GREEN);
            }
        }
            canvas.drawLine(0,  mHeight*((Math.abs(power)-50)/50.0f), 0, mHeight, mPaint);

        }
    }
    public void setPower(int power,boolean flg){
        this.power=power;
        this.flg=flg;
        invalidate();
    }


}
