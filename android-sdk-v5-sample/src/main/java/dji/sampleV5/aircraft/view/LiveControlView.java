package dji.sampleV5.aircraft.view;

import android.content.Context;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.manager.SDKManager;


public class LiveControlView extends LinearLayout implements View.OnClickListener {

    private TextView tvFPS;
    private TextView tvStatus;
    private LinearLayout llStatus;
    private LinearLayout llText;

    private ImageView ivTv;
    private OnClickCallback listener;

    public LiveControlView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {

        setOrientation(HORIZONTAL);

        ivTv = new ImageView(getContext());
        ivTv.setBackgroundResource(R.drawable.rtmp_start);
        ivTv.setScaleType(ImageView.ScaleType.CENTER_CROP);

        LayoutParams imageParams = new LayoutParams(ContextUtil.getDimen(R.dimen.space_24), ContextUtil.getDimen(R.dimen.space_24));
        imageParams.gravity = Gravity.CENTER_VERTICAL;
        imageParams.leftMargin = ContextUtil.getDimen(R.dimen.space_6);
        addView(ivTv, imageParams);

        TextView tvStream = new TextView(getContext());
        tvStream.setTextSize(TypedValue.COMPLEX_UNIT_PX, ContextUtil.getDimen(R.dimen.text_size_12));
        tvStream.setTextColor(ContextUtil.getColor(R.color.white_gray));
        tvStream.setText("开启直播");

        LayoutParams textParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        textParams.gravity = Gravity.CENTER_VERTICAL;
        textParams.leftMargin = ContextUtil.getDimen(R.dimen.space_3);
        textParams.rightMargin = ContextUtil.getDimen(R.dimen.space_3);

        llText = new LinearLayout(getContext());
        llText.addView(tvStream, textParams);

        addView(llText, new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT));

        llStatus = new LinearLayout(getContext());
        llStatus.setOrientation(HORIZONTAL);
        llStatus.setVisibility(GONE);
        llStatus.setPadding(ContextUtil.getDimen(R.dimen.space_6), 0, 0, 0);

        tvStatus = new TextView(getContext());
        tvStatus.setTextSize(TypedValue.COMPLEX_UNIT_PX, ContextUtil.getDimen(R.dimen.text_size_8));
        tvStatus.setTextColor(ContextUtil.getColor(R.color.white_gray));
        tvStatus.setText(ContextUtil.getString(R.string.live_connecting));

        tvFPS = new TextView(getContext());
        tvFPS.setTextSize(TypedValue.COMPLEX_UNIT_PX, ContextUtil.getDimen(R.dimen.text_size_8));
        tvFPS.setTextColor(ContextUtil.getColor(R.color.white_gray));
        String temp = " FPS:0";
        tvFPS.setText(temp);

        llStatus.addView(tvStatus);
        llStatus.addView(tvFPS);

        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_VERTICAL;
        addView(llStatus, params);

        setOnClickListener(this);
    }

    public void changeVideoUI(boolean start) {
        llStatus.setVisibility(start ? VISIBLE : GONE);
        llText.setVisibility(start ? GONE : VISIBLE);
        if (!start) {
            ivTv.setBackgroundResource(R.drawable.rtmp_start);
        }else {
            ivTv.setBackgroundResource(R.drawable.rtmp_stop);
        }
    }

    public void updateTextStatus(String status) {
        if (tvStatus != null) {
            tvStatus.setText(status);
        }
    }

    public void updateVideoFPSAndVideoBitrate(float fps, float bitrate) {
        String temp = " FPS:" + (int) fps;
        tvFPS.setText(temp);
    }

    @Override
    public void onClick(View v) {
        if (listener != null) {
            listener.onClickRtmp();
        }
    }

    public void setRtmpClickListener(OnClickCallback clickListener) {
        this.listener = clickListener;
    }

    public boolean isReadyForLive() {
        if (!DJIAircraftApplication.getInstance().isAircraftConnected) {
            ToastUtil.show(ContextUtil.getString(R.string.please_check_connection));
            return false;
        }

        return true;
    }

    public interface OnClickCallback {
        void onClickRtmp();
    }
}
