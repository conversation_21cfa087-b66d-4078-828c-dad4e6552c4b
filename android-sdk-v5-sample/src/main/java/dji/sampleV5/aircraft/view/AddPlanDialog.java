package dji.sampleV5.aircraft.view;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.util.phone.DensityUtil;


public class AddPlanDialog extends Dialog implements View.OnClickListener {

    private Context mContext;
    private MyClickListener listener;

    public AddPlanDialog(@NonNull Context context, MyClickListener listener) {
        super(context, R.style.AddPlanDialog);
        this.mContext = context;
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        init();
    }

    private void init() {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        View view = inflater.inflate(R.layout.dialog_add_plan_new, null);
        setContentView(view);

        Window dialogWindow = getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = DensityUtil.getScreenWidth();
            lp.height = DensityUtil.getScreenHeight();
            dialogWindow.setAttributes(lp);
        }



        findViewById(R.id.iv_close).setOnClickListener(this);
        findViewById(R.id.view_point).setOnClickListener(this);
        findViewById(R.id.view_polygon).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.iv_close) {
            dismiss();
            return;
        }

        switch (v.getId()) {
            case R.id.view_polygon:
                listener.onCreatePolygon();
                break;
            case R.id.view_point:
                listener.onCreatePoint();
                break;
        }
        dismiss();
    }

    public interface MyClickListener {
        void onCreatePolygon();

        void onCreatePoint();

        void onCreateOblique();

        void onCreateCircle();
    }
}
