package dji.sampleV5.aircraft.view.task;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.MissionInfo;

import java.text.DecimalFormat;

public class HistoryAircraftInfoView extends FrameLayout {
    private TextView tvXingHao,tvBianHao,tvJingDu,tvWeiDu,tvGaoDu,tvLength,tvShuiPingSpeed,tvChuiZhiSpeed,
            tvShangXingXinHao,tvXiaXingXinHao;
    private DecimalFormat df = new DecimalFormat("#.000000");
    public HistoryAircraftInfoView(@NonNull Context context) {
        super(context);
        initUI();
    }

    public HistoryAircraftInfoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context,attrs);
        //创建
        initUI();

    }

    public HistoryAircraftInfoView(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context,attrs,defStyleAttr);
        initUI();
    }


    @SuppressLint("MissingInflatedId")
    private void initUI(){
        LayoutInflater layoutInflater = (LayoutInflater) getContext().getSystemService(Service.LAYOUT_INFLATER_SERVICE);
        View view = layoutInflater.inflate(R.layout.view_history_aircraft_info, this, true);
        tvJingDu  = view.findViewById(R.id.tv_jingdu);
        tvWeiDu   = view.findViewById(R.id.tv_weidu);
        tvGaoDu   = view.findViewById(R.id.tv_gaodu);
        tvShuiPingSpeed = view.findViewById(R.id.tv_shuiping_speed);

    }



    public void refresh(MissionInfo.FlightRecordBean bean){
        tvJingDu.setText(df.format(bean.getLatitude()));
        tvWeiDu.setText(df.format(bean.getLongitude()));
        tvGaoDu.setText("高度："+bean.getAltitude()+"米");
        tvShuiPingSpeed.setText("水平速度："+bean.getHorizontalSpeed()+"m/s");
    }

}
