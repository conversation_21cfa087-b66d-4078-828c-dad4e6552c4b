package dji.sampleV5.aircraft.view.task;

import android.app.Service;
import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.MissionInfo;
import dji.sampleV5.aircraft.util.task.TimeUtil;

/**
 * Describe
 */
public class TaskMissionInfoView extends FrameLayout {
    private TextView tvTitleMissionInfo,tvTitleMissionState
            ,tvMissionName,tvHiveName,tvMissionBatch,tvMissionStart,tvMissionEnd,
            tvTakeOff,tvTakeOffState,tvTakeOffTime,
            tvMissioning,tvMissioningState,tvMissioningTime,
            tvClimb,tvClimbState,tvClimbTime,
            tvBack,tvBackState,tvBackTime;

    private ImageView ivTakeoff,ivClimb,ivBack,ivMissioning;
    private View ivMissionInfoBottom,ivMissionStateBottom,llMissionInfo,rlMissionState;
    private Long takeOffTime=Long.valueOf(0),climbTime=Long.valueOf(0),missioningTime=Long.valueOf(0),backTime=Long.valueOf(0);

    public TaskMissionInfoView(@NonNull Context context) {
        super(context);
        initUI();
    }

    public TaskMissionInfoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context,attrs);
        //创建
        initUI();

    }

    public TaskMissionInfoView(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context,attrs,defStyleAttr);
        initUI();
    }



    private void initUI(){
        LayoutInflater layoutInflater = (LayoutInflater) getContext().getSystemService(Service.LAYOUT_INFLATER_SERVICE);
        View view = layoutInflater.inflate(R.layout.view_task_mission_info, this, true);

        view.findViewById(R.id.rl_mission_state);
        ivMissionInfoBottom = view.findViewById(R.id.iv_mission_info_bottom);
        ivMissionStateBottom = view.findViewById(R.id.iv_mission_state_bottom);
        tvTitleMissionInfo = view.findViewById(R.id.tv_title_mission_info);
        tvTitleMissionState = view.findViewById(R.id.tv_title_mission_state);
        llMissionInfo = view.findViewById(R.id.ll_view_mission_info);
        rlMissionState = view.findViewById(R.id.rl_view_mission_state);

        initMissionInfoView(view);
        initMissionStateView(view);
        initRefresh();
        initListener(view);


    }

    private void initMissionInfoView(View view){
        tvMissionName = view.findViewById(R.id.tv_mission_name);
        tvHiveName = view.findViewById(R.id.tv_hive_name);
        tvMissionBatch = view.findViewById(R.id.tv_mission_batch);
        tvMissionStart = view.findViewById(R.id.tv_mission_start);
        tvMissionEnd = view.findViewById(R.id.tv_mission_end);
    }

    private void initMissionStateView(View view){
        ivTakeoff = view.findViewById(R.id.cd_takeoff);
        tvTakeOff = view.findViewById(R.id.tv_take_off);
        tvTakeOffState = view.findViewById(R.id.tv_take_off_state);
        tvTakeOffTime = view.findViewById(R.id.tv_take_off_time);
        ivClimb = view.findViewById(R.id.cd_climb);
        tvClimb = view.findViewById(R.id.tv_climb);
        tvClimbState = view.findViewById(R.id.tv_climb_state);
        tvClimbTime = view.findViewById(R.id.tv_climb_time);
        ivMissioning = findViewById(R.id.cd_missioning);
        tvMissioning = view.findViewById(R.id.tv_missioning);
        tvMissioningState = view.findViewById(R.id.tv_missioning_state);
        tvMissioningTime = view.findViewById(R.id.tv_missioning_time);
        ivBack = view.findViewById(R.id.cd_back);
        tvBack = view.findViewById(R.id.tv_back);
        tvBackState = view.findViewById(R.id.tv_back_state);
        tvBackTime = view.findViewById(R.id.tv_back_time);
    }
    public  void refresh(long point){

        if(point<climbTime){
            toTakeOff(false);
        }else if(point<missioningTime){
            toTakeOff(true);
            toClimb(false);
        }else if(point<backTime){
            toTakeOff(true);
            toClimb(true);
            toMissioning(false);
        }else {
            toTakeOff(true);
            toClimb(true);
            toMissioning(true);
            toBack(false);
        }
    }

    public void initRefresh(){
        tvTakeOff.setTextColor(Color.parseColor("#ffffff"));
        tvTakeOffState.setTextColor(Color.parseColor("#fee61e"));
        tvTakeOffState.setText("执行中");
        tvClimb.setTextColor(Color.parseColor("#a1a1a1"));
        tvClimbState.setTextColor(Color.parseColor("#a1a1a1"));
        tvClimbState.setText("未执行");
        tvMissioning.setTextColor(Color.parseColor("#a1a1a1"));
        tvMissioningState.setTextColor(Color.parseColor("#a1a1a1"));
        tvMissioningState.setText("未执行");
        tvBack.setTextColor(Color.parseColor("#a1a1a1"));
        tvBackState.setTextColor(Color.parseColor("#a1a1a1"));
        tvBackState.setText("未执行");
        ivTakeoff.setBackgroundColor(Color.parseColor("#fee61e"));
        ivClimb.setBackgroundColor(Color.parseColor("#ffffff"));
        ivMissioning.setBackgroundColor(Color.parseColor("#ffffff"));
        ivBack.setBackgroundColor(Color.parseColor("#ffffff"));
    }

    private void toTakeOff(boolean isEnd){
        tvTakeOff.setTextColor(Color.parseColor("#ffffff"));
        if(!isEnd){
            ivTakeoff.setBackgroundColor(Color.parseColor("#fee61e"));
            tvTakeOffState.setTextColor(Color.parseColor("#fee61e"));
            tvTakeOffState.setText("进行中");
        }else {
            ivTakeoff.setBackgroundColor(Color.parseColor("#00c5fe"));
            tvTakeOffState.setTextColor(Color.parseColor("#00c5fe"));
            tvTakeOffState.setText("已完成");
        }
    }

    private void toClimb(boolean isEnd){
        tvClimb.setTextColor(Color.parseColor("#ffffff"));
        if(!isEnd){
            ivClimb.setBackgroundColor(Color.parseColor("#fee61e"));
            tvClimbState.setTextColor(Color.parseColor("#fee61e"));
            tvClimbState.setText("进行中");
        }else {
            ivClimb.setBackgroundColor(Color.parseColor("#00c5fe"));
            tvClimbState.setTextColor(Color.parseColor("#00c5fe"));
            tvClimbState.setText("已完成");
        }
    }

    private void toMissioning(boolean isEnd){
        tvMissioning.setTextColor(Color.parseColor("#ffffff"));
        if(!isEnd){
            ivMissioning.setBackgroundColor(Color.parseColor("#fee61e"));
            tvMissioningState.setTextColor(Color.parseColor("#fee61e"));
            tvMissioningState.setText("进行中");
        }else {
            ivMissioning.setBackgroundColor(Color.parseColor("#00c5fe"));
            tvMissioningState.setTextColor(Color.parseColor("#00c5fe"));
            tvMissioningState.setText("已完成");
        }
    }

    private void toBack(boolean isEnd){
        tvBack.setTextColor(Color.parseColor("#ffffff"));
        if(!isEnd){
            ivBack.setBackgroundColor(Color.parseColor("#fee61e"));
            tvBackState.setTextColor(Color.parseColor("#fee61e"));
            tvBackState.setText("进行中");
        }else {
            ivBack.setBackgroundColor(Color.parseColor("#00c5fe"));
            tvBackState.setTextColor(Color.parseColor("#00c5fe"));
            tvBackState.setText("已完成");
        }
    }


    private void initListener(View view){
        view.findViewById(R.id.rl_mission_info).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                ivMissionInfoBottom.setVisibility(View.VISIBLE);
                tvTitleMissionInfo.setTextColor(Color.parseColor("#ffffff"));
                ivMissionStateBottom.setVisibility(View.INVISIBLE);
                tvTitleMissionState.setTextColor(Color.parseColor("#727272"));
                llMissionInfo.setVisibility(View.VISIBLE);
                rlMissionState.setVisibility(View.INVISIBLE);
            }
        });
        view.findViewById(R.id.rl_mission_state).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                ivMissionInfoBottom.setVisibility(View.INVISIBLE);
                tvTitleMissionInfo.setTextColor(Color.parseColor("#727272"));
                ivMissionStateBottom.setVisibility(View.VISIBLE);
                tvTitleMissionState.setTextColor(Color.parseColor("#ffffff"));
                llMissionInfo.setVisibility(View.INVISIBLE);
                rlMissionState.setVisibility(View.VISIBLE);
            }
        });
        view.findViewById(R.id.iv_back).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                TaskMissionInfoView.this.setVisibility(View.INVISIBLE);
            }
        });
    }

    public void setMissionInfo(MissionInfo bean){
        tvMissionBatch.setText(bean.getMissionBatch());
        tvMissionName.setText(bean.getMissionName());
        tvMissionStart.setText(bean.getUAVStartTime());
        tvMissionEnd.setText(bean.getUAVEndTime());
        tvHiveName.setText(bean.getSiteName());

        if(bean.getFlightRecords().list.size()>0){
            for (MissionInfo.FlightRecordBean flightRecord:
                bean.getFlightRecords().list) {
                switch (flightRecord.getSubState()){
                    case 204:
                        if(takeOffTime==0){
                           takeOffTime = flightRecord.getTimestamp();
                        }
                        continue;
                    case 206:
                        if(climbTime==0){
                            climbTime = flightRecord.getTimestamp();
                        }
                        continue;
                    case 209:
                        if(missioningTime==0){
                            missioningTime = flightRecord.getTimestamp();
                        }
                        continue;
                    case 102:
                        if(backTime==0){
                            backTime = flightRecord.getTimestamp();
                        }
                        break;
                }
            }
        }

        tvTakeOffTime.setText(TimeUtil.transferDate2String(takeOffTime));
        tvClimbTime.setText(TimeUtil.transferDate2String(climbTime));
        tvMissioningTime.setText(TimeUtil.transferDate2String(missioningTime));
        tvBackTime.setText(TimeUtil.transferDate2String(backTime));
    }






}
