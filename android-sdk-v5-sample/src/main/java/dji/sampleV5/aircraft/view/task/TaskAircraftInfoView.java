package dji.sampleV5.aircraft.view.task;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.AttrRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.MissionInfo;
import dji.sampleV5.aircraft.data.task.SiteInfo;

/**
 * Describe
 */
public class TaskAircraftInfoView extends FrameLayout {
    private TextView tvXingHao,tvBianHao,tvJingDu,tvWeiDu,tvGaoDu,tvLength,tvShuiPingSpeed,tvChuiZhiSpeed,
    tvShangXingXinHao,tvXiaXingXinHao;
    public TaskAircraftInfoView(@NonNull Context context) {
        super(context);
        initUI();
    }

    public TaskAircraftInfoView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context,attrs);
        //创建
        initUI();

    }

    public TaskAircraftInfoView(@NonNull Context context, @Nullable AttributeSet attrs, @AttrRes int defStyleAttr) {
        super(context,attrs,defStyleAttr);
        initUI();
    }


    @SuppressLint("MissingInflatedId")
    private void initUI(){
        LayoutInflater layoutInflater = (LayoutInflater) getContext().getSystemService(Service.LAYOUT_INFLATER_SERVICE);
        View view = layoutInflater.inflate(R.layout.view_task_aircraft_info, this, true);
        tvXingHao = view.findViewById(R.id.tv_xinghao);
        tvBianHao = view.findViewById(R.id.tv_bianhao);
        tvJingDu  = view.findViewById(R.id.tv_jingdu);
        tvWeiDu   = view.findViewById(R.id.tv_weidu);
        tvGaoDu   = view.findViewById(R.id.tv_gaodu);
        tvLength  = view.findViewById(R.id.tv_length);
        tvShuiPingSpeed = view.findViewById(R.id.tv_shuiping_speed);
        tvChuiZhiSpeed =view.findViewById(R.id.tv_chuizhi_speed);
        tvShangXingXinHao = view.findViewById(R.id.tv_shangxingxinhao);
        tvXiaXingXinHao = view.findViewById(R.id.tv_xiaxingxinhao);
        view.findViewById(R.id.iv_back).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                TaskAircraftInfoView.this.setVisibility(View.GONE);
            }
        });

    }
    public void setAircraftInfo(MissionInfo bean, SiteInfo siteInfo){
        if(bean!=null)
        tvBianHao.setText(bean.getUAVID());
        if(siteInfo!=null)
        tvXingHao.setText(siteInfo.getUAVInfo().getModel());
    }


    public void refresh(MissionInfo.FlightRecordBean bean){
        tvJingDu.setText(bean.getLatitude()+"");
        tvWeiDu.setText(bean.getLongitude()+"");
        tvGaoDu.setText(bean.getAltitude()+"米");
        tvLength.setText(bean.getDistanceStart()+"米");
        tvShuiPingSpeed.setText(bean.getHorizontalSpeed()+"m/s");
        tvChuiZhiSpeed.setText(bean.getVerticalSpeed()+"m/s");
        tvShangXingXinHao.setText(bean.getUpLink()+"%");
        tvXiaXingXinHao.setText(bean.getDownLink()+"%");

    }

}
