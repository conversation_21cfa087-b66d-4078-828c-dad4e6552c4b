package dji.sampleV5.aircraft.page.ai.vo;

/**
 * 场站位置查询
 */
public class LocationVO {

    private String id;
    private String country;
    private Double bmLatitude;
    private Double bmLongitude;
    private Double longitude;
    private Double latitude;
    private Double topLatitude;
    private Double topLongitude;
    private String province;
    private String cityCode;
    private String street;
    private String townCode;
    private Integer enable;
    private String location;

    public LocationVO() {
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Double getBmLatitude() {
        return bmLatitude;
    }

    public void setBmLatitude(Double bmLatitude) {
        this.bmLatitude = bmLatitude;
    }

    public Double getBmLongitude() {
        return bmLongitude;
    }

    public void setBmLongitude(Double bmLongitude) {
        this.bmLongitude = bmLongitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getTopLatitude() {
        return topLatitude;
    }

    public void setTopLatitude(Double topLatitude) {
        this.topLatitude = topLatitude;
    }

    public Double getTopLongitude() {
        return topLongitude;
    }

    public void setTopLongitude(Double topLongitude) {
        this.topLongitude = topLongitude;
    }
}
