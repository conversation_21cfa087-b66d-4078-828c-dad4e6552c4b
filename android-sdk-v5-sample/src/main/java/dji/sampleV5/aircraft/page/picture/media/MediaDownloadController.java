package dji.sampleV5.aircraft.page.picture.media;


import android.os.Environment;


import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaFileDownloadListener;


public class MediaDownloadController {
    private static MediaDownloadController INSTANCE = new MediaDownloadController();
    private ArrayList<MediaFile> checkedMediaFileList = new ArrayList<>();
    private OnProgressUpdateListener onProgressUpdateListener;
    private int currentPosition;

    private MediaDownloadController() {
    }

    public static MediaDownloadController getInstance() {
        return INSTANCE;
    }

    public int getTotalCount() {
        return checkedMediaFileList.size();
    }

    public int getCurrentPosition() {
        return currentPosition;
    }

    public void setCheckedMediaFileList(ArrayList<MediaFile> checkedList) {
        this.checkedMediaFileList.clear();
        currentPosition = 0;
        this.checkedMediaFileList.addAll(checkedList);
    }

    public void setOnProgressListener(OnProgressUpdateListener onProgressUpdateListener) {
        this.onProgressUpdateListener = onProgressUpdateListener;
    }

    private static String getVideoPath() {
        String path = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Skysys/Video";
        File file = new File(path);
        if (file.mkdirs()) {
            return path;
        }
        return path;
    }

    private static String getImagePath() {
        String path = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Skysys/Image";
        File file = new File(path);
        if (file.mkdirs()) {
            return path;
        }
        return path;
    }

    FileOutputStream outputStream = null;
    public void startDownload() {
        if (currentPosition < checkedMediaFileList.size()) {
           /* if (videoTable == null) {
                videoTable = new VideoTable(ContextUtil.getCurrentActivity());
            }*/
            MediaFile currentMedia = checkedMediaFileList.get(currentPosition);
            String downloadPath = "";
            if (currentMedia.getFileType().value() == 0) {
                downloadPath = getImagePath();
            } else if (currentMedia.getFileType().value() == 3) {
                downloadPath = getVideoPath();
            }
            final String fileName = currentMedia.getFileName();
            //String path = Environment.getExternalStorageDirectory().getPath() + "/NewUavApp/download/";
            //M300现在默认的结尾是大写JPG，但是下载后又会加上小写的jpg，这边我们下载的时候去先去掉结尾，但是这块判断就会有问题，后面记得处理
            File file = new File(downloadPath +"/"+ fileName);
            if (file.exists()) {
                ContextUtil.getHandler().postDelayed(() -> {
                    onProgressUpdateListener.onSuccess(fileName);
                }, 1000);
                ContextUtil.getHandler().postDelayed(() -> {
                    nextDownLoad();
                }, 2000);
            } else {
                //String downloadName = fileName.substring(0, fileName.lastIndexOf("."));

                try {
                    outputStream = new FileOutputStream(file);
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
                BufferedOutputStream bos = new BufferedOutputStream(outputStream);
                long beginTime = System.currentTimeMillis();

                currentMedia.pullOriginalMediaFileFromCamera(0, new MediaFileDownloadListener() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onProgress(long total, long current) {
                        onProgressUpdateListener.onProgressUpdate(total, current, fileName, currentPosition);
                    }

                    @Override
                    public void onRealtimeDataUpdate(byte[] data, long position) {
                        try {
                            bos.write(data, 0, data.length);
                            bos.flush();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFinish() {
                        try {
                            outputStream.close();
                            bos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        if (currentMedia.getFileType().value() == 3) {
                            //boolean isMavicPro = PreferenceUtil.getVideoIsMavicPro(currentMedia.getFileName());
                            //videoTable.save(currentMedia.getFileName(), currentMedia.getTimeCreated(), (long) (currentMedia.getDurationInSeconds() * 1000), isMavicPro);
                        }
                        //FileUtils.updatePhotoMedia(new File(filePath), ContextUtil.getCurrentActivity());
                        onProgressUpdateListener.onSuccess(fileName);
                        nextDownLoad();
                    }

                    @Override
                    public void onFailure(IDJIError error) {
                        nextDownLoad();
                        ContextUtil.getHandler().post(() -> ToastUtil.show("error = " + error.description()));
                    }
                });
            }
        }
    }

    private void nextDownLoad() {
        ContextUtil.getHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                currentPosition++;
                startDownload();
            }
        }, 1000);
    }

    public interface OnProgressUpdateListener {
        void onProgressUpdate(long total, long current, String fileName, int currentPosition);

        void onSuccess(String fileName);
    }
}
