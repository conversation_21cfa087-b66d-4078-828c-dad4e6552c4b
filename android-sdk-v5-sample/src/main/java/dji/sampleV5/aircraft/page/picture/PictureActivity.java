package dji.sampleV5.aircraft.page.picture;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentManager;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityPictureBinding;
import dji.sampleV5.aircraft.page.picture.media.MediaFragment;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import me.jessyan.autosize.internal.CancelAdapt;

public class PictureActivity extends AppCompatActivity implements View.OnSystemUiVisibilityChangeListener, CancelAdapt {
    private ActivityPictureBinding binding;
    private FragmentManager manager;
    private PictureFragmentManager pictureFragmentManager;

    public PictureFragmentManager getPictureFragmentManager() {
        return this.pictureFragmentManager;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(this);
        ImmerseUtil.startImmerse(this);
        manager = getSupportFragmentManager();
        binding = DataBindingUtil.setContentView(this, R.layout.activity_picture);
        pictureFragmentManager = new PictureFragmentManager(getSupportFragmentManager(), binding);
        manager.beginTransaction().add(R.id.container_navigation, MediaFragment.newInstance(), PictureFragmentManager.MEDIA_FRAGMENT).commitAllowingStateLoss();

    }

    @Override
    public void onBackPressed() {
        if (pictureFragmentManager.onBackPressed()) {
            super.onBackPressed();
        }
    }

    @Override
    public void onSystemUiVisibilityChange(int visibility) {
        if (visibility == 0) {
            ContextUtil.getHandler().removeCallbacks(enterImmerseMode);
            ContextUtil.getHandler().postDelayed(enterImmerseMode, 2000);
        }
    }

    private Runnable enterImmerseMode = () -> ImmerseUtil.startImmerse(PictureActivity.this);
}
