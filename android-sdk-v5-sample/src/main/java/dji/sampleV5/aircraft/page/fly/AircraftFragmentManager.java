package dji.sampleV5.aircraft.page.fly;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;

public class AircraftFragmentManager {
    public static final String SETTING = "setting";
    public static final String MEDIA_FRAGMENT = "media_fragment";
    public static final String MEDIA_FRAGMENT_SHOW = "media_fragment_show";

    private FragmentManager manager;
    private ActivityDefaultLayoutBinding binding;

    /**
     * @param manager
     * @param binding 界面绑定
     */
    public AircraftFragmentManager(FragmentManager manager, ActivityDefaultLayoutBinding binding) {
        this.manager = manager;
        this.binding = binding;
    }

    public void addFragment(String tag, Bundle bundle) {
        Fragment fragment = manager.findFragmentByTag(tag);
        if (fragment == null) {
            switch (tag) {
                case SETTING:
                    fragment = AircraftSettingFragment.newInstance(bundle);
                    break;
               /* case MEDIA_FRAGMENT:
                     fragment =MediaFragment.newInstance();
                    break;
                case MEDIA_FRAGMENT_SHOW:
                     fragment = MediaShowFragment.newInstance(bundle);
                    break;*/
            }
            manager.beginTransaction().add(R.id.container_navigation, fragment, tag).commitAllowingStateLoss();
            binding.containerNavigation.bringToFront();
        }
    }


    public Fragment getFragment(String tag) {
        return manager.findFragmentByTag(tag);
    }

    public void removeFragment(String tag) {
        Fragment fragment = manager.findFragmentByTag(tag);
        if (fragment != null) {
            manager.beginTransaction().remove(fragment).commitAllowingStateLoss();
        }
    }

    public boolean onBackPressed() {
        Fragment fragmentSetting = manager.findFragmentByTag(SETTING);
        Fragment fragmentMediaFragment = manager.findFragmentByTag(MEDIA_FRAGMENT);
        Fragment fragmentFragmentShow = manager.findFragmentByTag(MEDIA_FRAGMENT_SHOW);
        if (fragmentFragmentShow!=null){
            removeFragment(MEDIA_FRAGMENT_SHOW);
        }
        if (fragmentMediaFragment!=null){
            removeFragment(MEDIA_FRAGMENT);
            return false;
        }
        if (fragmentSetting != null) {
            removeFragment(SETTING);
            return false;
        }
        return true;
    }


}
