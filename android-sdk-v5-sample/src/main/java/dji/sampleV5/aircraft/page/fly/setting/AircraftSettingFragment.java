package dji.sampleV5.aircraft.page.fly.setting;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.common.TimerScheduler;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityMenuBinding;
import dji.sampleV5.aircraft.page.fly.AircraftFragmentManager;
import dji.sampleV5.aircraft.page.fly.setting.adapter.myPagerAdapter;
import dji.sampleV5.aircraft.page.fly.setting.pager.AircraftParamPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.AircraftVisionPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;
import dji.sampleV5.aircraft.page.fly.setting.pager.BatteryInfoPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.GeneralSettingPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.GimbalSettingPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.RemoteFunctionPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.AircraftVisionAdvancePager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.BaseBatteryInfoPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.BatteryAdvancedSetPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.GimbalAdvancedPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.RTKInfoPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.RemoteCalibrationPager;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.RemoteFrequencySettingPager;
import me.jessyan.autosize.internal.CancelAdapt;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;


public class AircraftSettingFragment extends Fragment implements View.OnClickListener, CancelAdapt {

    public static final int TAB_AIRCRAFT = 0;
    public static final int TAB_VISION = 1;
    public static final int TAB_REMOTE = 2;
    public static final int TAB_HD = 3;
    public static final int TAB_BATTERY = 4;
    public static final int TAB_GIMBAL = 5;
    public static final int TAB_RTK = 6;
    public static final int TAB_GENERAL = 7;


    public static final int PAGER_VISION_ADVANCE = 8;
    public static final int PAGER_REMOTE_FREQUENCY = 9;
    public static final int PAGER_REMOTE_CALIBRATE = 10;
    public static final int PAGER_BATTERY_INFO = 11;
    public static final int PAGER_BATTERY_ADVANCE = 12;
    public static final int PAGER_GIMBAL_ADVANCE = 13;

    private static final String MAVIC = "Mavic";

    private int modePager;
    private boolean remoteMode;
    private boolean isOcuSync = false;
    private List<BasePager> pagerList;

    private BasePager basePager;
    private ScheduledFuture uiTask;
    private ActivityMenuBinding binding;
    private ActivityMenuPresenter activityMenuPresenter;

    public static AircraftSettingFragment newInstance(Bundle bundle) {
        AircraftSettingFragment fragment = new AircraftSettingFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            modePager = bundle.getInt("mode");
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.activity_menu, container, false);
        remoteMode = SpUtil.getRemoteMode();
        activityMenuPresenter = new ActivityMenuPresenter(this);
        binding.setActivityMenuPresenter(activityMenuPresenter);
        return binding.getRoot();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        initData();
        binding.setMenuLeftListener(this);

        Runnable task = () -> ContextUtil.getHandler().post(() -> pagerList.get(binding.vpMenu.getCurrentItem()).onPageSelected());
        uiTask = TimerScheduler.getInstance().scheduleAtFixedRate(task, 0, 1000);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        uiTask.cancel(true);
        for (int i = 0; i < pagerList.size(); i++) {
            BasePager basePager = pagerList.get(i);
            basePager.removeListener();
        }
    }

    public ViewPager getViewPager() {
        return binding.vpMenu;
    }

    private void initData() {
        pagerList = new ArrayList<>();

        pagerList.add(TAB_AIRCRAFT, new AircraftParamPager(this));
        pagerList.add(TAB_VISION, new AircraftVisionPager(this));
        pagerList.add(TAB_REMOTE, new RemoteFunctionPager(this));
        pagerList.add(TAB_HD, new RemoteFunctionPager(this));
        /*if(isOcuSync){
            pagerList.add(TAB_HD, new OcuSyncImageTransPager(this));
        }else {
            pagerList.add(TAB_HD, new LightBrigeTransmissionPager(this));
        }*/
        pagerList.add(TAB_BATTERY, new BatteryInfoPager(this));
        pagerList.add(TAB_GIMBAL, new GimbalSettingPager(this));
        pagerList.add(TAB_RTK, new RTKInfoPager(this));
        //pagerList.add(TAB_RTK, new RTKInfoPager(this));
        pagerList.add(TAB_GENERAL, new GeneralSettingPager(this));

        pagerList.add(PAGER_VISION_ADVANCE, new AircraftVisionAdvancePager(this));
        pagerList.add(PAGER_REMOTE_FREQUENCY, new RemoteFrequencySettingPager(this));
        pagerList.add(PAGER_REMOTE_CALIBRATE, new RemoteCalibrationPager(this));
        pagerList.add(PAGER_BATTERY_INFO, new BaseBatteryInfoPager(this));
        pagerList.add(PAGER_BATTERY_ADVANCE, new BatteryAdvancedSetPager(this));
        pagerList.add(PAGER_GIMBAL_ADVANCE, new GimbalAdvancedPager(this));

        binding.vpMenu.setAdapter(new myPagerAdapter(pagerList));

        binding.vpMenu.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            //当前显示页面
            @Override
            public void onPageSelected(int position) {
                basePager = pagerList.get(position);
                if (!basePager.isLoading) {
                    Log.e("onPageSelected", "onPageSelected: " + position);
                    basePager.initData();
                    basePager.isConnect(true);
                }
            }

            //滑动状态改变
            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        //如果点击了遥控模式则进入遥控设置页面,否则显示第一个页面
        if (remoteMode) {
            binding.vpMenu.setCurrentItem(PAGER_REMOTE_FREQUENCY, false);
            activityMenuPresenter.setRootRc(true);
            SpUtil.setRemoteMode(false);
        } else {
            switch (modePager) {
                case TAB_AIRCRAFT:
                    basePager = pagerList.get(TAB_AIRCRAFT);
                    basePager.initData();
                    activityMenuPresenter.isCheckRemoteA(true);
                    break;
                case TAB_VISION:
                    basePager = pagerList.get(TAB_VISION);
                    activityMenuPresenter.setRootVision(true);
                    binding.vpMenu.setCurrentItem(TAB_VISION, false);
                    break;
                case TAB_REMOTE:
                    activityMenuPresenter.setRootRc(true);
                    binding.vpMenu.setCurrentItem(TAB_REMOTE, false);
                    break;
                case TAB_HD:
                    activityMenuPresenter.setRootHD(true);
                    binding.vpMenu.setCurrentItem(TAB_HD, false);
                    break;
                case TAB_BATTERY:
                    activityMenuPresenter.setRootBattery(true);
                    binding.vpMenu.setCurrentItem(TAB_BATTERY, false);
                    break;
                case TAB_GENERAL:
                    activityMenuPresenter.setRootOther(true);
                    binding.vpMenu.setCurrentItem(TAB_GENERAL, false);
                    break;
            }
        }
    }

    public void finish() {
        DefaultLayoutActivity activity = ((DefaultLayoutActivity) getActivity());
        assert activity != null;
        activity.getAircraftFragmentManager().removeFragment(AircraftFragmentManager.SETTING);
    }

    @Override
    public void onClick(View v) {
        activityMenuPresenter.isCheckRemoteA(false);
        activityMenuPresenter.setRTKMode(false);
        activityMenuPresenter.setRootVision(false);
        activityMenuPresenter.setRootHD(false);
        activityMenuPresenter.setRootBattery(false);
        activityMenuPresenter.setRootGimbal(false);
        activityMenuPresenter.setRootOther(false);
        activityMenuPresenter.setRootRc(false);

        switch (v.getId()) {
            case R.id.TranslucentView:
                finish();
                break;
            case R.id.ll_root_btn_flyc:
                activityMenuPresenter.isCheckRemoteA(true);
                binding.vpMenu.setCurrentItem(TAB_AIRCRAFT, false);
                break;
            case R.id.ll_root_btn_vision:
                activityMenuPresenter.setRootVision(true);
                binding.vpMenu.setCurrentItem(TAB_VISION, false);
                break;
            case R.id.ll_root_btn_rc:
                activityMenuPresenter.setRootRc(true);
                binding.vpMenu.setCurrentItem(TAB_REMOTE, false);
                break;
            case R.id.ll_root_btn_hd:
                activityMenuPresenter.setRootHD(true);
                binding.vpMenu.setCurrentItem(TAB_HD, false);
                break;
            case R.id.ll_root_btn_battery:
                activityMenuPresenter.setRootBattery(true);
                binding.vpMenu.setCurrentItem(TAB_BATTERY, false);
                break;
            case R.id.ll_root_btn_gimbal:
                activityMenuPresenter.setRootGimbal(true);
                binding.vpMenu.setCurrentItem(TAB_GIMBAL, false);
                break;
            case R.id.ll_root_btn_rtk:
                activityMenuPresenter.setRTKMode(true);
                binding.vpMenu.setCurrentItem(TAB_RTK, false);
                break;
            case R.id.ll_root_btn_other:
                activityMenuPresenter.setRootOther(true);
                binding.vpMenu.setCurrentItem(TAB_GENERAL, false);
                break;
        }
    }
}

