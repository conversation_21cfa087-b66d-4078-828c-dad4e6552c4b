package dji.sampleV5.aircraft.page.picture.media;

import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;

import dji.v5.manager.datacenter.media.MediaFile;


public class MediaBean implements Parcelable {

    private Bitmap preview;
    private Bitmap thumbnail;

    private MediaFile media;
    private String fileName;
    private String dateCreated;

    private int mediaType;
    private long timeCreated;
    private float durationInSeconds;

    public MediaBean() {
    }

    public MediaBean(Parcel in) {
        fileName = in.readString();
        preview = in.readParcelable(Bitmap.class.getClassLoader());
        mediaType = in.readInt();
        thumbnail = in.readParcelable(Bitmap.class.getClassLoader());
        durationInSeconds = in.readFloat();
        timeCreated = in.readLong();
    }

    public long getTimeCreated() {
        return timeCreated;
    }

    public void setTimeCreated(long timeCreated) {
        this.timeCreated = timeCreated;
    }

    public float getDurationInSeconds() {
        return durationInSeconds;
    }

    public void setDurationInSeconds(float durationInSeconds) {
        this.durationInSeconds = durationInSeconds;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Bitmap getPreview() {
        return preview;
    }

    public void setPreview(Bitmap preview) {
        this.preview = preview;
    }

    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    public Bitmap getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(Bitmap thumbnail) {
        this.thumbnail = thumbnail;
    }

    public MediaFile getMedia() {
        return media;
    }

    public void setMedia(MediaFile media) {
        this.media = media;
    }


    String getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(String dateCreated) {
        this.dateCreated = dateCreated;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(fileName);
        dest.writeParcelable(preview, flags);
        dest.writeInt(mediaType);
        dest.writeParcelable(thumbnail, flags);
        dest.writeFloat(durationInSeconds);
        dest.writeLong(timeCreated);
    }

    public static final Creator<MediaBean> CREATOR = new Creator<MediaBean>() {
        @Override
        public MediaBean createFromParcel(Parcel in) {
            return new MediaBean(in);
        }

        @Override
        public MediaBean[] newArray(int size) {
            return new MediaBean[size];
        }
    };
}
