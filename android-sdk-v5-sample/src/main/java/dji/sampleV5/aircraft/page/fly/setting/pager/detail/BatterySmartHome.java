package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.widget.ToggleButton;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.databinding.MenuBatteryPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;


public class BatterySmartHome {

    private final ToggleButton tbSmartHome;
    //private final FlightController flightController;

    public BatterySmartHome(MenuBatteryPagerBinding binding) {
        tbSmartHome = binding.menuBatterySmartHome.tbSmartHome;
        //flightController = DJIHelper.getInstance().getFlightController();

        tbSmartHome.setOnCheckedChangeListener((buttonView, isChecked) -> setSmartHome(isChecked));
    }

    private void setSmartHome(boolean isChecked) {
        KeyManager.getInstance().setValue(KeyTools.createKey(FlightControllerKey.KeyLowBatteryRTHEnabled), isChecked, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
               // ToastUtil.show("智能返航设置成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
       /* if (flightController!=null){
            flightController.setSmartReturnToHomeEnabled(isChecked, djiError -> getSmartHome());
        }*/
    }

    public void getSmartHome() {
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyLowBatteryRTHEnabled), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                ContextUtil.getHandler().post(() -> tbSmartHome.setChecked(aBoolean));
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
       /* if (flightController!=null){
            flightController.getSmartReturnToHomeEnabled(new CommonCallbacks.CompletionCallbackWith<Boolean>() {
                @Override
                public void onSuccess(final Boolean aBoolean) {
                    ContextUtil.getHandler().post(() -> tbSmartHome.setChecked(aBoolean));
                }

                @Override
                public void onFailure(DJIError djiError) {
                }
            });

        }*/

    }

    public void remove(){

    }
}
