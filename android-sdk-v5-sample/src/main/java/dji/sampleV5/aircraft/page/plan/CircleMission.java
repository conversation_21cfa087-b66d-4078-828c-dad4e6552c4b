package dji.sampleV5.aircraft.page.plan;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.net.bean.CameraInfoBean;

public class CircleMission extends BaseMission {

    private float radius;                   // 半径
    private boolean isClockWise;            // 是否顺时针
    private AppLatLng centerPoint;          // 中心点
    private float overlap;                  // 航向重叠率
    private int shootNumber;                // 拍照数量
    private int shootDistance;              // 拍照间隔，单位 m
    private float gimbalPitch;              // 云台俯仰角度[-90, 0]，单位：度
    private FinishedAction finishedAction;  // 任务结束后执行动作

    private List<AppLatLng> pointList = new ArrayList<>();  // 航点列表
    private CameraInfoBean cameraSetting = new CameraInfoBean();  // 相机配置信息

    public CircleMission() {

    }

    public AppLatLng getCenterPoint() {
        return centerPoint;
    }

    public void setCenterPoint(AppLatLng centerPoint) {
        this.centerPoint = centerPoint;
    }

    public float getRadius() {
        return radius;
    }

    public void setRadius(float radius) {
        this.radius = radius;
    }

    public boolean isClockWise() {
        return isClockWise;
    }

    public void setClockWise(boolean clockWise) {
        isClockWise = clockWise;
    }

    public CameraInfoBean getCameraSetting() {
        return cameraSetting;
    }

    public void setCameraSetting(CameraInfoBean cameraSetting) {
        this.cameraSetting = cameraSetting;
    }

    public float getOverlap() {
        return overlap;
    }

    public void setOverlap(float overlap) {
        this.overlap = overlap;
    }

    public int getShootNumber() {
        return shootNumber;
    }

    public void setShootNumber(int shootNumber) {
        this.shootNumber = shootNumber;
    }

    public int getShootDistance() {
        return shootDistance;
    }

    public void setShootDistance(int shootDistance) {
        this.shootDistance = shootDistance;
    }

    public float getGimbalPitch() {
        return gimbalPitch;
    }

    public void setGimbalPitch(float gimbalPitch) {
        this.gimbalPitch = gimbalPitch;
    }

    public FinishedAction getFinishedAction() {
        return finishedAction;
    }

    public void setFinishedAction(FinishedAction finishedAction) {
        this.finishedAction = finishedAction;
    }

    public List<AppLatLng> getPointList() {
        return pointList;
    }

    public void setPointList(List<AppLatLng> pointList) {
        this.pointList = pointList;
    }
}
