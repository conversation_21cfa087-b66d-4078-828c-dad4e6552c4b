package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.widget.CompoundButton;
import android.widget.ToggleButton;

import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;


public class ChangeFlyMode implements CompoundButton.OnCheckedChangeListener {

    private final ToggleButton switchChangeMode;

    public ChangeFlyMode(MenuAicraftSetPagerBinding aicraftSetPagerBinding) {
        switchChangeMode = aicraftSetPagerBinding.menuASetChangeFightMode.switchChangeMode;
        switchChangeMode.setOnCheckedChangeListener(this);
    }

    public void connect(Boolean connect) {
        if (connect){
            switchChangeMode.setEnabled(true);
          /*  flightController = DJIHelper.getInstance().getFlightController();
            if (flightController != null) {
                getSwitchChangeMode();
            }*/
        }else {
            switchChangeMode.setEnabled(false);
        }
    }

    public void setSwitchChangeMode(boolean changeMode){
        /*if (flightController!=null){
            flightController.setMultipleFlightModeEnabled(changeMode, djiError -> {

            });
        }*/
    }
    public void getSwitchChangeMode(){
            /*flightController.getMultipleFlightModeEnabled(new CommonCallbacks.CompletionCallbackWith<Boolean>() {
                @Override
                public void onSuccess(final Boolean aBoolean) {
                    ContextUtil.getHandler().post(() -> switchChangeMode.setChecked(aBoolean));
                }

                @Override
                public void onFailure(DJIError djiError) {

                }
            });*/
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        setSwitchChangeMode(isChecked);
    }
}
