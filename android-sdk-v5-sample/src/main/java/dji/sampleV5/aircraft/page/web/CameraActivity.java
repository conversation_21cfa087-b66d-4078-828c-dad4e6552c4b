package dji.sampleV5.aircraft.page.web;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;

import com.otaliastudios.cameraview.BitmapCallback;
import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.PictureResult;

import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.CameraActivityBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import me.jessyan.autosize.internal.CancelAdapt;

public class CameraActivity extends AppCompatActivity implements CancelAdapt {
    private CameraActivityBinding binding;
    private PictureResult pictureResult;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ImmerseUtil.startImmerse(this);
        binding = DataBindingUtil.setContentView(this, R.layout.camera_activity);
        binding.camera.setLifecycleOwner(this);
        binding.camera.addCameraListener(new CameraListener() {
            @Override
            public void onPictureTaken(@NonNull PictureResult result) {
                super.onPictureTaken(result);
                pictureResult = result;
                try {
                    result.toBitmap(new BitmapCallback() {
                        @Override
                        public void onBitmapReady(@Nullable Bitmap bitmap) {
                            binding.image.setImageBitmap(bitmap);
                            //binding.image.setImageResource(R.drawable.test);
                            binding.imageBack.setVisibility(View.VISIBLE);
                        }
                    });
                } catch (Exception e) {
                    ToastUtil.show("Can't preview this format: " + result.getFormat());
                }
            }
        });

        binding.picture.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (binding.camera.isTakingPicture()){
                    ToastUtil.show("正在拍照，请稍后点击");
                    return;
                }
                binding.camera.takePicture();
            }
        });

        binding.upload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                //ToastUtil.show("上传成功");
                /*Intent intent = new Intent();
                intent.putExtra("pic", JsonUtil.toJson(pictureResult));
                setResult(1, intent);*/
                DJIAircraftApplication.getInstance().setPictureResult(pictureResult);
                finish();
            }
        });
    }

    @Override
    protected void onPause() {
        binding.camera.close();
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        binding.camera.destroy();
        super.onDestroy();
    }

    @Override
    public void onResume() {
        if (ContextCompat.checkSelfPermission(CameraActivity.this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            if(!binding.camera.isOpened()){
                binding.camera.open();
            }
        } else {
            ActivityCompat.requestPermissions(CameraActivity.this, new String[]{Manifest.permission.CAMERA}, 1);
        }
        super.onResume();
    }
}
