package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import java.util.Timer;
import java.util.TimerTask;

import dji.sampleV5.aircraft.databinding.MenuRemoteFunctionSetBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;


//摇杆功能设置
public class RemoteFrequencySettingPager extends BasePager {

    private static final int OK_STATUS = 1;
    private static final int CANCEL_STATUS = 2;

    private int time = 60;
    private int status = 1;
    private boolean flg = false;
    private int remote[] = {R.string.japan, R.string.usa, R.string.china};

    private Timer timer;
    private AlertDialog alertDialog;
    private TextView tvDialogText1;
    private TextView tvDialogText2;
    private TextView tvDialogText3;
    private RelativeLayout rlExitRemoteMode;
    //private KeyListener pairingStateListener;
    private RemoteSetFunctionMode mFunctionMode;

    public RemoteFrequencySettingPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading = true;
        activityMenuPresenter.setIsPrevious(true);
        activityMenuPresenter.setPreviousName("RemotePager");
        tvTitle.setText(ContextUtil.getString(R.string.remote_set_pager));

        MenuRemoteFunctionSetBinding remoteFunctionSetBinding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_remote_function_set, null, false);
        remoteFunctionSetBinding.setActivityMenuPresenter(activityMenuPresenter);
        ImageView ivUiRc = remoteFunctionSetBinding.ivUiRc;
        TextView etNumber = remoteFunctionSetBinding.etNumber;
        rlExitRemoteMode = remoteFunctionSetBinding.rlExditRemoteMode;

        ListView mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        mFunctionMode = new RemoteSetFunctionMode(ivUiRc, rlExitRemoteMode, etNumber, activityMenuPresenter, remote, mListView, (DefaultLayoutActivity)fragment.getActivity());

        //遥控器对频
        remoteFunctionSetBinding.tvRockerPairing.setOnClickListener(v -> {
            if (timer == null) {
               /* RemoteController remoteController = DJIHelper.getInstance().getRemoteController();
                if (remoteController != null) {
                    showDialog();
                }*/
            }
        });
        //添加到帧布局
        flContainer.addView(remoteFunctionSetBinding.getRoot());
    }

    private void showDialog() {
       /* AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity());
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.dialog_message_text_two, null);
        tvDialogText1 = view.findViewById(R.id.tv_dialog_text1);
        tvDialogText2 = view.findViewById(R.id.tv_dialog_text2);
        tvDialogText3 = view.findViewById(R.id.tv_dialog_text3);

        tvDialogText1.setText(ContextUtil.getString(R.string.rocker_pairing_dialog_1));
        tvDialogText3.setText(ContextUtil.getString(R.string.dialog_ok));
        tvDialogText3.setOnClickListener(v -> {
            if (status == OK_STATUS) {
                tvDialogText1.setText(ContextUtil.getString(R.string.rocker_pairing_dialog_2));
                timer = new Timer();
                TimerTask timerTask = new TimerTask() {
                    @Override
                    public void run() {
                        time--;
                        ContextUtil.getHandler().post(() -> {
                            String format = String.format(ContextUtil.getString(R.string.rocker_pairing_dialog_3), time);
                            tvDialogText2.setText(format);
                        });
                        if (time == 0) {
                            timer.cancel();
                            ContextUtil.getHandler().post(() -> {
                                tvDialogText1.setText(ContextUtil.getString(R.string.rocker_pairing_dialog_5));
                                tvDialogText3.setText(ContextUtil.getString(R.string.dialog_ok));
                                //连接超时点击关闭
                                status = CANCEL_STATUS;
                            });
                        }
                    }
                };
                timer.schedule(timerTask, 0, 1000);
                tvDialogText3.setText(ContextUtil.getString(R.string.dialog_cancel));
                status = CANCEL_STATUS;
                KeyManager.getInstance().performAction(RemoteControllerKey.create(RemoteControllerKey.START_PAIRING), new YNListener0() {
                    @Override
                    public void onSuccess() {
                        flg = true;
                        setAddListener();
                    }

                    @Override
                    public void onException(Throwable e) {
                        ToastUtil.show(ContextUtil.getString(R.string.not_support));
                    }
                });
            } else if (status == CANCEL_STATUS) {
                timer.cancel();
                alertDialog.dismiss();
            }
        });
        builder.setView(view);
        alertDialog = builder.create();
        ImmerseUtil.showDialog(alertDialog);
        alertDialog.setOnDismissListener(dialog -> {
            status = OK_STATUS;
            KeyManager.getInstance().removeListener(pairingStateListener);
            time = 60;
            if (timer != null) {
                timer.cancel();
            }
            if (flg) {
                KeyManager.getInstance().performAction(RemoteControllerKey.create(RemoteControllerKey.STOP_PAIRING), new YNListener0() {
                    @Override
                    public void onSuccess() {
                        flg = false;
                    }

                    @Override
                    public void onException(Throwable e) {
                    }
                });
            }
        });*/

    }

    private void setAddListener() {
        /*pairingStateListener = new KeyListener<PairingState>() {
            @Override
            protected void onValueChanged(@Nullable PairingState old, @Nullable PairingState now) {
                if (now != null) {
                    switch (now.value()) {
                        case 0:
                            setTextShow();
                            break;
                        case 1:
                            break;
                        case 2:
                            setTextShow();
                            break;
                    }
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(RemoteControllerKey.create(RemoteControllerKey.PAIRING_STATE), pairingStateListener);*/
    }

    private void setTextShow() {
        ContextUtil.getHandler().post(() -> {
            flg = false;
            if (timer != null) {
                timer.cancel();
            }
            tvDialogText1.setText(ContextUtil.getString(R.string.rocker_pairing_dialog_6));
            tvDialogText2.setText(" ");
            tvDialogText3.setText(ContextUtil.getString(R.string.dialog_ok));
        });
    }

    @Override
    public void removeListener() {
        if (timer != null) {
            timer.cancel();
        }
        //KeyManager.getInstance().removeListener(pairingStateListener);
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading) {
            if (connect) {
                rlExitRemoteMode.setVisibility(View.VISIBLE);
                mFunctionMode.getFristMode();
            } else {
                if (timer != null) {
                    timer.cancel();
                }
                //KeyManager.getInstance().removeListener(pairingStateListener);
                rlExitRemoteMode.setVisibility(View.GONE);
            }
        }
    }
}
