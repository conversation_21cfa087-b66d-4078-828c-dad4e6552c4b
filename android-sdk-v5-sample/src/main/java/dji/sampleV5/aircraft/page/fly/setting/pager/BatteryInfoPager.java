package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuBatteryPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.BatteryPagerInfoItem;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.BatteryPagerInfoItemSeekbar;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.BatterySmartHome;


public class BatteryInfoPager extends BasePager {

    private BatterySmartHome batterySmartHome;
    private BatteryPagerInfoItem batteryPagerInfoItem;
    private BatteryPagerInfoItemSeekbar batteryPagerInfoItemSeekbar;

    public BatteryInfoPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {

        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.battery_set_pager));
        MenuBatteryPagerBinding binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_battery_pager, null, false);
        binding.setActivityMenuPresenter(activityMenuPresenter);
        //电池电压等信息,每秒获取.方法放在menuActivity中每秒调用
        batteryPagerInfoItem = new BatteryPagerInfoItem(binding, fragment);
        batteryPagerInfoItem.getBatteryInfo1();
        //设置电池电量警报
        batteryPagerInfoItemSeekbar = new BatteryPagerInfoItemSeekbar(binding);

        //智能返航
        batterySmartHome = new BatterySmartHome(binding);
        //添加到帧布局
        flContainer.addView(binding.getRoot());
    }


    @Override
    public void removeListener() {
        if (isLoading) {
            batteryPagerInfoItem.removeLisener();
            batterySmartHome.remove();
            batteryPagerInfoItemSeekbar.removeLisener();
        }
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading) {
            if (connect) {
                batteryPagerInfoItemSeekbar.setBatteryThreshold();
                batterySmartHome.getSmartHome();
            }
        }
    }

}
