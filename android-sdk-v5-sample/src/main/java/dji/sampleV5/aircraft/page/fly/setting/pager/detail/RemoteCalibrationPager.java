package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;


public class RemoteCalibrationPager extends BasePager {

    public RemoteCalibrationPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading=true;
        activityMenuPresenter.setIsPrevious(true);
        activityMenuPresenter.setPreviousName("RemoteCalibrationPager");
        tvTitle.setText(ContextUtil.getString(R.string.rc_calibration));
        /*MenuRemoteCalibrationBinding Binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_remote_calibration, null, false);

        //添加到帧布局
        flContainer.addView(Binding.getRoot());*/
    }

    @Override
    public void removeListener() {

    }
}
