package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.page.fly.setting.adapter.PagerModeAdapter;

//遥控功能模式设置
public class RemoteSetFunctionMode {
    private final ImageView mivUiRc;
    private final DefaultLayoutActivity mActivity;
    private  TextView metNumber;
    private  RelativeLayout mrlExditRemoteMode;
    private  int[] mremote;
    private ActivityMenuPresenter mremoteFunctionSetBinding;
    private  ListView mlistView;
    private int mValue;
    private AlertDialog mDialog;
    private int mposition;
    private PagerModeAdapter mPagerModeAdapter;


    public RemoteSetFunctionMode(ImageView ivUiRc, RelativeLayout rlExditRemoteMode, TextView etNumber, ActivityMenuPresenter remoteFunctionSetBinding, int[] remote,
                                 ListView mListView, DefaultLayoutActivity mActivity) {
        this.mActivity=mActivity;
        mlistView=mListView;
        mremoteFunctionSetBinding=remoteFunctionSetBinding;
        mremote=remote;
        mrlExditRemoteMode=rlExditRemoteMode;
        metNumber=etNumber;
        mivUiRc=ivUiRc;
    }

    //获得当前模式
    public void getFristMode() {
      /*  if (DJIHelper.getInstance().getRemoteController()==null){
            return;
        }
        KeyManager.getInstance().getValue(RemoteControllerKey.create(RemoteControllerKey.AIRCRAFT_MAPPING_STYLE), new YNListener1<AircraftMappingStyle>() {
            @Override
            public void onSuccess(AircraftMappingStyle MappingStyle) {
                mValue=MappingStyle.value();
                setListIntoMode(mValue-1);
                ContextUtil.getHandler().post(() -> {
                    switch (mValue) {
                        case 1:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.japan));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_japan);
                            break;
                        case 2:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.usa));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_usa);
                            break;
                        case 3:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.china));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_china);
                            break;
                    }
                });
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    public void setFunctionMode(final int position) {
       /* ContextUtil.getHandler().post(() -> {
            switch (position) {
                case 0:
                    getDialog(AircraftMappingStyle.STYLE_1, position);
                    break;
                case 1:
                    getDialog(AircraftMappingStyle.STYLE_2, position);
                    break;
                case 2:
                    getDialog(AircraftMappingStyle.STYLE_3, position);
                    break;

            }
        });*/
    }

   /* private void getDialog(final AircraftMappingStyle ControlStyle, final int position) {

        AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity(),AlertDialog.THEME_HOLO_DARK);

        builder.setMessage(ContextUtil.getString(R.string.remote_set_function_mode));
        builder.setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
            setRCControlMode(ControlStyle, position);
            mDialog.dismiss();
        });
        builder.setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
            KeyManager.getInstance().getValue(RemoteControllerKey.create(RemoteControllerKey.AIRCRAFT_MAPPING_STYLE), new YNListener1<AircraftMappingStyle>() {
                @Override
                public void onSuccess(final AircraftMappingStyle value) {
                    ContextUtil.getHandler().post(() -> {
                        mPagerModeAdapter.getPosition(value.value() - 1);
                        mPagerModeAdapter.notifyDataSetChanged();
                    });
                }

                @Override
                public void onException(Throwable e) {

                }
            });
            mDialog.dismiss();
        });
        mDialog = builder.create();
        mDialog.setCanceledOnTouchOutside(false);
        ImmerseUtil.showDialog(mDialog);
    }
*/


//设置listview里面的遥控手模式
    public void setListIntoMode(final int value) {
        mposition=value;
        mPagerModeAdapter = new PagerModeAdapter(mremote,mposition);
        mlistView.setAdapter(mPagerModeAdapter);
        mlistView.setVerticalScrollBarEnabled(false);
        mlistView.setOnItemClickListener((parent, view, position, id) -> {
            //设置摇杆模式
            setFunctionMode(position);
                mPagerModeAdapter.getPosition(position);
                mPagerModeAdapter.notifyDataSetChanged();
            //((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
        });
        mrlExditRemoteMode.setOnClickListener(v -> {
            View anchor = metNumber;
            //mActivity.showPopup(anchor,mlistView, mremote.length);
        });
    }

    //设置遥控手模式
   /* private void setRCControlMode(final AircraftMappingStyle ControlStyle, final int position) {
        KeyManager.getInstance().setValue(RemoteControllerKey.create(RemoteControllerKey.AIRCRAFT_MAPPING_STYLE), ControlStyle, new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> {
                    switch (ControlStyle.value()) {
                        case 1:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.japan));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_japan);
                            break;
                        case 2:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.usa));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_usa);
                            break;
                        case 3:
                            mremoteFunctionSetBinding.setRemoteShow(ContextUtil.getString(R.string.china));
                            mivUiRc.setImageResource(R.drawable.setting_ui_rc_china);
                            break;
                    }
                });

                mposition = position;


            }

            @Override
            public void onException(Throwable e) {
            }
        });
    }*/


}
