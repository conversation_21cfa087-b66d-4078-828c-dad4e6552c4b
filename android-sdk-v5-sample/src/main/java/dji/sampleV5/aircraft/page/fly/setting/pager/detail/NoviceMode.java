package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.content.Context;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.ToggleButton;

import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;


public class NoviceMode implements CompoundButton.OnCheckedChangeListener {
    private MenuAicraftSetPagerBinding aicraftSetPagerBinding;
    private int MaxHighlimit;
    private int mDiatancelimit;
    private AlertDialog mDialog1;
    private final ToggleButton swithASNoviceMode;
    //private FlightController flightController;
    private AlertDialog mAlertDialog;
    private final EditText tvMaxHighLimit;
    //private KeyListener<Integer> max_flight_heightListener;
    //private KeyListener<Integer> max_flight_radiusListener;
    private final EditText tvLimitDiatance;
    private boolean isNoviceMode;

    private final ToggleButton switchASetLimitDiatance;
    private final RelativeLayout rlASetLimitDiatance;
    private final ActivityMenuPresenter activityMenuPresenter;
    private boolean RADIUS_ENABLED;
    private boolean isflying=false;


    public NoviceMode(MenuAicraftSetPagerBinding aicraftSetPagerBinding) {
        this.aicraftSetPagerBinding=aicraftSetPagerBinding;
        activityMenuPresenter = aicraftSetPagerBinding.getActivityMenuPresenter();
        swithASNoviceMode = aicraftSetPagerBinding.menuASetNewBoyMode.swithASNoviceMode;
        tvMaxHighLimit = aicraftSetPagerBinding.menuASetMaxHigh.tvASetMaxHighLimitNovice;
        tvLimitDiatance = aicraftSetPagerBinding.menuASetDistanceLimit.evASetLimitDiatance;

        switchASetLimitDiatance = aicraftSetPagerBinding.menuASetDistanceLimit.switchASetLimitDiatance;
        rlASetLimitDiatance = aicraftSetPagerBinding.menuASetDistanceLimit.rlASetLimitDiatance;
        swithASNoviceMode.setOnCheckedChangeListener(this);
        switchASetLimitDiatance.setOnCheckedChangeListener((buttonView, isChecked) -> setRADIUS_ENABLED(isChecked));
        //监听输入框的值
        getEditorLimit();
        //获得最大高度限制的当前值
        getMaxHighlimit1();
        //获得限远当前值
        getMaxRadius();
        //获得是否打开限远开关当前状态
        getMaxRadiusEnabed();
    }

    private void getMaxRadiusEnabed() {
        /*max_flight_radius_enabledListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now != null) {
                    RADIUS_ENABLED = now;
                    ContextUtil.getHandler().post(() -> {
                        switchASetLimitDiatance.setChecked(now);
                        if (now) {
                            rlASetLimitDiatance.setVisibility(View.VISIBLE);
                        } else {
                            rlASetLimitDiatance.setVisibility(View.GONE);
                        }
                    });

                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_RADIUS_ENABLED), max_flight_radius_enabledListener);*/
    }

    /**
     * 开启新手模式
     */
    private void StartNovice(boolean isChecked) {
        if (isChecked){
            rlASetLimitDiatance.setVisibility(View.GONE);
            activityMenuPresenter.setNoviceMode(true);
        }else {
            if (RADIUS_ENABLED){
                rlASetLimitDiatance.setVisibility(View.VISIBLE);
            }else {
                rlASetLimitDiatance.setVisibility(View.GONE);
            }
            activityMenuPresenter.setNoviceMode(false);
        }
    }
    private void getMaxRadius() {
       /* max_flight_radiusListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> tvLimitDiatance.setText(now+""));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_RADIUS),max_flight_radiusListener);*/
    }

    private void getMaxHighlimit1() {
       /* max_flight_heightListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> tvMaxHighLimit.setText(now + ""));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_HEIGHT),max_flight_heightListener);*/
    }

    public void getEditorLimit() {
        /*//修改获得输入框最大限高,设置
        tvMaxHighLimit.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {//点击软键盘完成控件时触发的行为

                //关闭光标并且关闭软键盘
//                    mevASetMaxHighLimitNovice.setCursorVisible(false);
                InputMethodManager im = (InputMethodManager) ContextUtil.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(ContextUtil.getCurrentActivity().getCurrentFocus().getApplicationWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
                //设置输入的值
                getMaxHighlimit();
                setMaxFlightHeight(MaxHighlimit);
                int uiOptions = View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
                ContextUtil.getCurrentActivity().getWindow().getDecorView().setSystemUiVisibility(uiOptions);
            }
            return true;//消费掉该行为
        });

        //修改获得输入框最大限远,设置
        tvLimitDiatance.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {//点击软键盘完成控件时触发的行为
                //关闭光标并且关闭软键盘
//                    mevASetLimitDistance.setCursorVisible(false);
                InputMethodManager im = (InputMethodManager) ContextUtil.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(ContextUtil.getCurrentActivity().getCurrentFocus().getApplicationWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
                getEditextDistancelimit();
                setMaxFlightRadius(mDiatancelimit);
                int uiOptions = View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
                ContextUtil.getCurrentActivity().getWindow().getDecorView().setSystemUiVisibility(uiOptions);
            }
            return true;//消费掉该行为
        });*/
    }


    /**
     * 关闭新手模式
     */
    private void StopNovicedialog() {
        /*AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity(),AlertDialog.THEME_HOLO_DARK);
        builder.setMessage(ContextUtil.getString(R.string.close_novice_tips));
        builder.setPositiveButton(ContextUtil.getString(R.string.dialog_ok), (dialog, which) -> {
            getMaxHighlimit();
            getEditextDistancelimit();
            setMaxFlightHeight(MaxHighlimit);
            setMaxFlightRadius(mDiatancelimit);
            mDialog1.dismiss();
        });
        builder.setNegativeButton(ContextUtil.getString(R.string.dialog_cancel), (dialog, which) -> {
            swithASNoviceMode.setChecked(true);
            mDialog1.dismiss();
        });
        mDialog1 = builder.create();
        mDialog1.setCanceledOnTouchOutside(false);
        ImmerseUtil.showDialog(mDialog1);*/
    }

    //新手模式提示
    private void startNoviceDialog() {
       /* AircraftFragmentManager manager = ((AircraftActivity)ContextUtil.getCurrentActivity()).getAircraftFragmentManager();
        manager.removeFragment(AircraftFragmentManager.SETTING);
        AircraftPresenter presenter = ((AircraftActivity) ContextUtil.getCurrentActivity()).getAircraftPresenter();
        ((AircraftActivity)ContextUtil.getCurrentActivity()).showNoviceDialog();*/
    }

    /**
     * 设置距离限制
     *
     */
    public void setMaxFlightRadius(final int FlightRadius) {
        /*KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_RADIUS),  FlightRadius, new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> {
                    ToastUtil.show(ContextUtil.getString(R.string.set_success));
                    aicraftSetPagerBinding.menuASetDistanceLimit.evASetLimitDiatance.setText(FlightRadius + "");
                });
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    /**
     * 设置最大限高
     */
    public void setMaxFlightHeight(final int max) {
        /*KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_HEIGHT), max, new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> ToastUtil.show(ContextUtil.getString(R.string.set_success)));
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }
    /**
     * 获得输入的数值最大限高
     */
    private void getMaxHighlimit() {
        final String s =  aicraftSetPagerBinding.menuASetMaxHigh.tvASetMaxHighLimitNovice.getText().toString().trim();
        if (s != null) {
            MaxHighlimit = Integer.parseInt(s);
            if (500 < MaxHighlimit) {
                MaxHighlimit = 500;
            } else if (MaxHighlimit < 20) {
                MaxHighlimit = 20;
            }
        }
    }

    /**
     * 获得输入的数值距离限制
     */
    private void getEditextDistancelimit() {

        final String s = aicraftSetPagerBinding.menuASetDistanceLimit.evASetLimitDiatance.getText().toString().trim();
        if (s != null) {
            mDiatancelimit = Integer.parseInt(s);
            if (5000 < mDiatancelimit) {
                mDiatancelimit = 5000;
            } else if (mDiatancelimit < 15) {
                mDiatancelimit = 15;
            }
        }

    }


    //是否限远
    private void setRADIUS_ENABLED(final boolean isChecked) {
        /*KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.MAX_FLIGHT_RADIUS_ENABLED), isChecked, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {
                ContextUtil.getHandler().post(() -> ToastUtil.show(ContextUtil.getString(R.string.not_support_GPS)));
            }
        });*/
    }

    public void connected(Boolean connect) {
        /*swithASNoviceMode.setEnabled(connect);
        switchASetLimitDiatance.setEnabled(connect);
        tvMaxHighLimit.setEnabled(connect);
        tvLimitDiatance.setEnabled(connect);
        if (connect){
            flightController = DJIHelper.getInstance().getFlightController();
            if (flightController != null) {
                getNoviceMode();
            }
        }*/
    }


    private void getNoviceMode() {
        /*flightController.getNoviceModeEnabled(new CommonCallbacks.CompletionCallbackWith<Boolean>() {
            @Override
            public void onSuccess(final Boolean aBoolean) {
                ContextUtil.getHandler().post(() -> {
                    StartNovice(aBoolean);
                    isNoviceMode=aBoolean;
                    swithASNoviceMode.setChecked(aBoolean);
                });
            }

            @Override
            public void onFailure(DJIError djiError) {

            }
        });*/
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, final boolean isChecked) {
       /* KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_FLYING), new YNListener1<Boolean>() {
            @Override
            public void onSuccess(Boolean value) {
                if (isNoviceMode){
                    isNoviceMode=false;
                }else {
                    if (value){
                        if (isChecked){
                            ContextUtil.getHandler().post(() -> {
                                isflying=true;
                                dialogfiying();
                                swithASNoviceMode.setChecked(false);
                            });
                        }else {
                            if (!isflying){
                                isflying=false;
                                setNoviceMode1(isChecked);
                            }
                        }
                    }else {
                        setNoviceMode1(isChecked);
                    }
                }
                }
            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    public void dialogfiying() {
        /*AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity());
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.status_compass_dialog, null);
        Button cancel_dialog = view.findViewById(R.id.button_cancel_dialog);
        cancel_dialog.setVisibility(View.GONE);
        Button button_ok = view.findViewById(R.id.button_ok_dialog);
        button_ok.setOnClickListener(v -> mAlertDialog.dismiss());
        TextView tv_show_center_message = view.findViewById(R.id.tv_show_center_message);
        tv_show_center_message.setText(ContextUtil.getString(R.string.fpv_setting_beginner_mode_note));
        builder.setView(view);
        mAlertDialog = builder.create();
        ImmerseUtil.showDialog(mAlertDialog);*/
    }

    private void setNoviceMode1(final boolean isChecked) {
       /* if (flightController!=null){
            flightController.setNoviceModeEnabled(isChecked, djiError -> ContextUtil.getHandler().post(() -> {
                if (djiError == null) {
                    if (isChecked) {
                        startNoviceDialog();
                    } else {
                        StartNovice(isChecked);
                        StopNovicedialog();
                    }
                } else {
                    swithASNoviceMode.setChecked(isChecked);
                }
            }));
        }*/
    }

    public void remove() {
       /* KeyManager.getInstance().removeListener(max_flight_heightListener);
        KeyManager.getInstance().removeListener(max_flight_radiusListener);
        KeyManager.getInstance().removeListener(max_flight_radius_enabledListener);*/
    }
}
