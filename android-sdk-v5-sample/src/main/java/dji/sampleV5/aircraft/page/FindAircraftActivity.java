package dji.sampleV5.aircraft.page;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.TextureMapView;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityFindAircraftBinding;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MapIndex;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.lbs.MissionMapPainter;
import dji.sampleV5.aircraft.lbs.amap.AMapTranslation;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.lbs.bean.MarkerInfo;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.view.MapTypeMenu;
import me.jessyan.autosize.internal.CancelAdapt;

public class FindAircraftActivity extends AppCompatActivity implements  CancelAdapt {
    private ActivityFindAircraftBinding binding;
    private MapController mMapController;
    private MissionMapPainter mMissionMapPainter;
    private TextureMapView mapView;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_find_aircraft);
        ImmerseUtil.fullScreen(this);
        mMapController = MapServiceFactory.produceMapController();
        mapView = mMapController.getMapView(this);
        mMapController.onCreate(savedInstanceState);
        mMissionMapPainter = new MissionMapPainter(this, mMapController);

        binding.rootView.addView(mapView);
        binding.top.bringToFront();
        binding.imageLevel.bringToFront();
        binding.rlLatlng.bringToFront();

        binding.imageLevel.setOnClickListener(v -> {
            new MapTypeMenu(this, mapView.getMap()).show(v);
        });
        binding.ivBack.setOnClickListener(v -> {
            finish();
        });

        AppLatLng appLatLng = SpUtil.getAircraftLastLocation();
        if (appLatLng == null) {
            ToastUtil.show("没有获取到无人机数据");
        } else {
            binding.rlLatlng.setVisibility(View.VISIBLE);
            binding.latlng.setText("84坐标系 "+appLatLng.getLat()+"/"+appLatLng.getLng());

            LocateInfo locateInfo = GCJ02_WGS84.wgs84_To_Gcj02(appLatLng.getLat(), appLatLng.getLng());
            AppLatLng location = new AppLatLng(locateInfo.getLatitude(), locateInfo.getLongitude());
            Bitmap bMap = BitmapFactory.decodeResource(getResources(), R.drawable.aircraft);
            MarkerInfo info = new MarkerInfo()
                    .setPosition(location)
                    .setIcon(bMap)
                    .setZIndex(MapIndex.ROUTE_VERTEX_INDEX);
            mMapController.addMarker(info);
            mapView.getMap().moveCamera(CameraUpdateFactory.newLatLngZoom(AMapTranslation.toSDK(location), 16));
        }
    }
}
