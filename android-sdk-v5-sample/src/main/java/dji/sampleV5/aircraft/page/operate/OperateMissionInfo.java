package dji.sampleV5.aircraft.page.operate;

import java.util.List;

public class OperateMissionInfo {
    private String ASName;
    private String updateTime;
    private String missionName;
    private String missionId;
    private String missionType;
    private int enableGroundFly;
    private FlightParam flightParams;

    public void setEnableGroundFly(int enableGroundFly) {
        this.enableGroundFly = enableGroundFly;
    }

    public int getEnableGroundFly() {
        return enableGroundFly;
    }

    public String getMissionType() {
        return missionType;
    }

    public void setMissionType(String missionType) {
        this.missionType = missionType;
    }

    public String getASName() {
        return ASName;
    }

    public void setASName(String ASName) {
        this.ASName = ASName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getMissionName() {
        return missionName;
    }

    public void setMissionName(String missionName) {
        this.missionName = missionName;
    }

    public String getMissionId() {
        return missionId;
    }

    public void setMissionId(String missionId) {
        this.missionId = missionId;
    }

    public FlightParam getFlightParams() {
        return flightParams;
    }

    public void setFlightParams(FlightParam flightParams) {
        this.flightParams = flightParams;
    }

    public static class FlightParam{
        private List<FlightPath> childrenList;
        private List<Detail> flightPath;

        public void setFlightPath(List<Detail> flightPath) {
            this.flightPath = flightPath;
        }

        public List<Detail> getFlightPath() {
            return flightPath;
        }

        public void setChildrenList(List<FlightPath> childrenList) {
            this.childrenList = childrenList;
        }

        public List<FlightPath> getChildrenList() {
            return childrenList;
        }
    }

    public static class FlightPath{
        private List<Detail> flightPath;

        public void setFlightPath(List<Detail> flightPath) {
            this.flightPath = flightPath;
        }

        public List<Detail> getFlightPath() {
            return flightPath;
        }
    }

    public static class Detail{
        private double latitude;
        private double longitude;

        public double getLatitude() {
            return latitude;
        }

        public void setLatitude(double latitude) {
            this.latitude = latitude;
        }

        public double getLongitude() {
            return longitude;
        }

        public void setLongitude(double longitude) {
            this.longitude = longitude;
        }
    }
}
