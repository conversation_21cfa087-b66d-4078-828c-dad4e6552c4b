package dji.sampleV5.aircraft.page.picture.media;

import java.util.ArrayList;

import dji.v5.manager.datacenter.media.MediaFile;


//处里事件
public class MediaObserver implements MediaFileObserveInterface {
    private final ArrayList<MediaUpdate> objects;

    MediaObserver() {
        objects = new ArrayList<>();
    }

    @Override
    public void registerObserver(MediaUpdate observer) {
        objects.add(observer);
    }

    @Override
    public void removeObserver(MediaUpdate observer) {
        objects.remove(observer);
    }

    @Override
    public void notifyObserver(String e) {
        for (int i = 0; i < objects.size(); i++) {
            objects.get(i).update(e);
        }
    }

    void setNotifyObserver(String data) {
        notifyObserver(data);
    }
    //是否选择删除

    public void setSelect(boolean select) {
        for (int i = 0; i < objects.size(); i++) {
            objects.get(i).selectMedia(select);
        }
    }

    public void setDownloadMedia(int currentItem){
        objects.get(currentItem).downloadMedia(currentItem);
    }

    void setDeleteMedia(int currentItem) {
        objects.get(currentItem).deleteMedia(currentItem);
    }

    void setDeleteMediaUpdate(ArrayList<MediaFile> listMedia, int currentItem) {
        for (int i = 0; i < objects.size(); i++) {
            if (i != currentItem) {
                objects.get(i).deleteMediaUpdate(listMedia);
            }
        }
    }
}
