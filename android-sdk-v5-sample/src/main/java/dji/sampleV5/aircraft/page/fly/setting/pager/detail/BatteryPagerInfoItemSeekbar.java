package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.databinding.MenuBatteryPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;


public class BatteryPagerInfoItemSeekbar implements View.OnClickListener {
    private SeekBar seekBarLowBattery;
    private final TextView textLowBattery;
    private final SeekBar seekBarSerious;
    private final TextView textSerious;
    private int lastLowBattery;

    public BatteryPagerInfoItemSeekbar(MenuBatteryPagerBinding binding) {
        //低电压
        seekBarLowBattery = binding.menuBatteryPagerItem3.seekbarSeverity;
        textLowBattery = binding.menuBatteryPagerItem3.tvSeverity;
        RelativeLayout lowBatteryMinus = binding.menuBatteryPagerItem3.lowBatteryMinus;
        RelativeLayout lowBatteryPlus = binding.menuBatteryPagerItem3.lowBatteryPlus;
        lowBatteryMinus.setOnClickListener(this);
        lowBatteryPlus.setOnClickListener(this);
        //严重低电压
        seekBarSerious = binding.menuBatteryPagerItem4.seekbarSeverity1;
        textSerious = binding.menuBatteryPagerItem4.tvSeverity1;
        RelativeLayout seriousBatteryMius = binding.menuBatteryPagerItem4.seriousBatteryMinus;
        RelativeLayout seriousBatteryPlus = binding.menuBatteryPagerItem4.seriousBatteryPlus;
        seriousBatteryMius.setOnClickListener(this);
        seriousBatteryPlus.setOnClickListener(this);

        setSeekBar();
    }

    public void setBatteryThreshold() {
        //获取电量低电量警告
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyLowBatteryWarningThreshold), new CommonCallbacks.CompletionCallbackWithParam<Integer>() {
            @Override
            public void onSuccess(Integer value) {
                ContextUtil.getHandler().post(() -> {
                    seekBarLowBattery.setProgress(value - 15);
                    lastLowBattery = value;
                    seekBarSerious.setMax(value - 15);
                    textLowBattery.setText(value + "%");
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
       /* KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.LOW_BATTERY_WARNING_THRESHOLD), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                ContextUtil.getHandler().post(() -> {
                    seekBarLowBattery.setProgress(value - 15);
                    lastLowBattery = value;
                    seekBarSerious.setMax(value - 15);
                    textLowBattery.setText(value + "%");
                });
            }

            @Override
            public void onException(Throwable e) {

            }
        });
        //获取电量严重低电量警告
        KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.SERIOUS_LOW_BATTERY_WARNING_THRESHOLD), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                ContextUtil.getHandler().post(() -> {
                    textSerious.setText(value + "%");
                    seekBarSerious.setProgress(value - 10);
                });
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/


    }

    private void setSeekBar() {
        //严重低电压
        seekBarSerious.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, final int Progress, boolean b) {
                setSeriousBattery(Progress + 10);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });

        //低电压
        seekBarLowBattery.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, final int progress, boolean b) {
                textLowBattery.setText(progress + 15 + "%");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                int progress = seekBar.getProgress();
                setLowBatterySeekbarValue(progress);
            }
        });
    }

    //严重低电压
    private void setSeriousBattery(final int i) {
        //The minimum value is 10. The maximum value is value from getLowBatteryWarningThreshold minus 5.
        /*KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.SERIOUS_LOW_BATTERY_WARNING_THRESHOLD), i, new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> textSerious.setText(i + "%"));
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    //低电压
    private void setLowBattery(final int progress) {
        KeyManager.getInstance().setValue(KeyTools.createKey(FlightControllerKey.KeyLowBatteryWarningThreshold), progress, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                lastLowBattery = progress;
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
        //The percentage must be in the range of [15, 50]
        /*KeyManager.getInstance().setValue(FlightControllerKey.create(FlightControllerKey.LOW_BATTERY_WARNING_THRESHOLD), Progress, new YNListener0() {
            @Override
            public void onSuccess() {
                lastLowBattery = Progress;
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    public void removeLisener() {

    }

    private void setLowBatterySeekbarValue(int progress) {
        if (progress >= 0 && progress <= seekBarLowBattery.getMax()) {
            seekBarLowBattery.setProgress(progress);
            textLowBattery.setText(progress + 15 + "%");
            if (progress < 5) {
                setLowBattery(progress + 15);
                seekBarSerious.setMax(0);
            } else if (progress < seekBarSerious.getProgress()) {
                ToastUtil.show(R.string.low_battery_setup_failed);
                textLowBattery.setText(lastLowBattery + "%");
                seekBarLowBattery.setProgress(lastLowBattery - 15);
            } else {
                setLowBattery(progress + 15);
                seekBarSerious.setMax(progress);
            }
        }
    }

    private void setSeriousBatterySeekbarValue(int progress) {
        if (progress >= 0 && progress <= seekBarSerious.getMax()) {
            seekBarSerious.setProgress(progress);
            setSeriousBattery(progress + 10);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.low_battery_minus:
                setLowBatterySeekbarValue(seekBarLowBattery.getProgress() - 1);
                break;
            case R.id.low_battery_plus:
                setLowBatterySeekbarValue(seekBarLowBattery.getProgress() + 1);
                break;
            case R.id.serious_battery_minus:
                setSeriousBatterySeekbarValue(seekBarSerious.getProgress() - 1);
                break;
            case R.id.serious_battery_plus:
                setSeriousBatterySeekbarValue(seekBarSerious.getProgress() + 1);
                break;
        }
    }
}
