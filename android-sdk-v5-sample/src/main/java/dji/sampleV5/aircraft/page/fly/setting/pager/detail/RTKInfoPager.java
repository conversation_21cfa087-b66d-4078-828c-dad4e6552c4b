package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;


import com.alibaba.fastjson.JSONArray;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import dji.rtk.CoordinateSystem;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.HomeActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.data.preference.CustomRTKSetting;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.MenuRtkSettingPagerBinding;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.view.adapter.EasyAdapter;
import dji.sampleV5.aircraft.view.adapter.EasyHolder;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.flightcontroller.FailsafeAction;
import dji.sdk.keyvalue.value.rtkbasestation.RTKCustomNetworkSetting;
import dji.sdk.keyvalue.value.rtkbasestation.RTKReferenceStationSource;
import dji.sdk.keyvalue.value.rtkbasestation.RTKServiceState;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.rtk.RTKCenter;
import dji.v5.manager.aircraft.rtk.RTKLocationInfo;
import dji.v5.manager.aircraft.rtk.RTKLocationInfoListener;
import dji.v5.manager.aircraft.rtk.network.INetworkServiceInfoListener;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class RTKInfoPager extends BasePager implements View.OnClickListener {
    private final ArrayList<String> listRtkMode = new ArrayList<>();
    private int currentPosition;
    private EasyAdapter easyAdapter;
    private ListView mListView;
    private TextView TVRTKMode;
    private MenuRtkSettingPagerBinding binding;
    private double tuoQiuHeight = -9999;
    private double lat;
    private double lon;

    public RTKInfoPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        tvTitle.setText(ContextUtil.getString(R.string.rtk_setting));
        activityMenuPresenter.setIsPrevious(false);
        activityMenuPresenter.setPreviousName("RTKInfoPager");
        if (mListView != null) {
            return;
        }
        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_rtk_setting_pager, null, false);
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        TVRTKMode = binding.tvRtkMode;
        TVRTKMode.setOnClickListener(this);
        List list = Arrays.asList("CMCC网络RTK", "自定义网络RTK","千寻网络RTK");
        listRtkMode.addAll(list);

        RTKCenter.getInstance().getRTKReferenceStationSource(new CommonCallbacks.CompletionCallbackWithParam<RTKReferenceStationSource>() {
            @Override
            public void onSuccess(RTKReferenceStationSource rtkReferenceStationSource) {
                Log.e("TAG", ": " + rtkReferenceStationSource);
                switch (rtkReferenceStationSource) {
                    case NTRIP_NETWORK_SERVICE:
                        setListIntoMode(0);
                        binding.cmccRl.setVisibility(View.VISIBLE);
                        binding.customRtkRl.setVisibility(View.GONE);
                        binding.qxRl.setVisibility(View.GONE);
                        break;
                    case CUSTOM_NETWORK_SERVICE:
                        setListIntoMode(1);
                        binding.cmccRl.setVisibility(View.GONE);
                        binding.qxRl.setVisibility(View.GONE);
                        binding.customRtkRl.setVisibility(View.VISIBLE);
                        setCustomRTKText();
                        break;
                    case QX_NETWORK_SERVICE:
                        setListIntoMode(2);
                        binding.qxRl.setVisibility(View.VISIBLE);
                        binding.cmccRl.setVisibility(View.GONE);
                        binding.customRtkRl.setVisibility(View.GONE);
                        setCustomRTKText();
                        break;
                    default:
                        setListIntoMode(0);
                        binding.cmccRl.setVisibility(View.VISIBLE);
                        binding.customRtkRl.setVisibility(View.GONE);
                        binding.qxRl.setVisibility(View.GONE);
                        break;
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });

        flContainer.addView(binding.getRoot());
        RTKCenter.getInstance().getCMCCRTKManager().addNetworkRTKServiceInfoListener(new INetworkServiceInfoListener() {
            @Override
            public void onServiceStateUpdate(RTKServiceState state) {
                //ToastUtil.show("RTK状态：" + state.name());
                binding.cmccStatus.setText(state.name());
            }

            @Override
            public void onErrorCodeUpdate(IDJIError code) {

            }
        });

        RTKCenter.getInstance().getCustomRTKManager().addNetworkRTKServiceInfoListener(new INetworkServiceInfoListener() {
            @Override
            public void onServiceStateUpdate(RTKServiceState state) {
                //ToastUtil.show("RTK状态：" + state.name());
                binding.customStatus.setText(state.name());
            }

            @Override
            public void onErrorCodeUpdate(IDJIError code) {

            }
        });

        RTKCenter.getInstance().getQXRTKManager().addNetworkRTKServiceInfoListener(new INetworkServiceInfoListener() {
            @Override
            public void onServiceStateUpdate(RTKServiceState state) {
                //ToastUtil.show("RTK状态：" + state.name());
                binding.qxStatus.setText(state.name());
            }

            @Override
            public void onErrorCodeUpdate(IDJIError code) {

            }
        });

        //获取椭球高
        RTKCenter.getInstance().addRTKLocationInfoListener(new RTKLocationInfoListener() {
            @Override
            public void onUpdate(RTKLocationInfo newValue) {
                if(newValue != null && newValue.getRtkLocation() != null && newValue.getRtkLocation().getMobileStationLocation() != null){
                    tuoQiuHeight = newValue.getRtkLocation().getMobileStationLocation().getAltitude();
                    /*lon = newValue.getRtkLocation().getMobileStationLocation().longitude;
                    lat = newValue.getRtkLocation().getMobileStationLocation().latitude;*/
                    binding.tuoqiuHeight.setText(tuoQiuHeight+"米");
                    //ToastUtil.show("椭球高:"+newValue.getRtkLocation().getMobileStationLocation().altitude);
                }else {
                    tuoQiuHeight = -9999;
                    binding.tuoqiuHeight.setText("未知");
                }

            }
        });

        binding.syncRtkData.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                LocationCoordinate2D locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
                if (locationCoordinate2D == null) {
                    ToastUtil.show("没有获取到无人机坐标！");
                    return;
                }

                lat = locationCoordinate2D.getLatitude();
                lon = locationCoordinate2D.getLongitude();
                if(tuoQiuHeight == -9999){
                    ToastUtil.show("RTK数据异常，请确认RTK正常连接后再重试");
                }else {
                    AlertDialog rtkDialog = new AlertDialog.Builder(ContextUtil.getCurrentActivity())
                            .setMessage("当前无人机椭球高为"+tuoQiuHeight+"米，是否上报")
                            .setPositiveButton("确认", (dialog, which) -> {
                                syncRtkData();
                            })
                            .setNegativeButton("取消", (dialog, which) -> {
                            })
                            .create();
                    rtkDialog.setCanceledOnTouchOutside(false);
                    rtkDialog.show();
                }
            }
        });

        binding.startCmcc.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                RTKCenter.getInstance().getCMCCRTKManager().startNetworkRTKService(CoordinateSystem.WGS84, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        ToastUtil.show("开启CMCC成功");
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("开启CMCC失败：" + error.description());
                    }
                });
            }
        });

        binding.startQx.setOnClickListener(v -> {
            RTKCenter.getInstance().getQXRTKManager().startNetworkRTKService(CoordinateSystem.WGS84, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("开启千寻RTK成功");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("开启千寻RTK失败：" + error.description());
                }
            });
        });

        binding.setCustom.setOnClickListener(v -> {
            String host = binding.editHost.getText().toString();
            String port = binding.editPort.getText().toString();
            String user = binding.editUser.getText().toString();
            String pwd = binding.editPwd.getText().toString();
            String mountpoint = binding.editMountpoint.getText().toString();
            if(TextUtils.isEmpty(host) || TextUtils.isEmpty(port) || TextUtils.isEmpty(user) || TextUtils.isEmpty(pwd) || TextUtils.isEmpty(mountpoint)){
                ToastUtil.show("请完善参数");
                return;
            }
            CustomRTKSetting customRTKSetting = new CustomRTKSetting();
            customRTKSetting.setHost(host);
            customRTKSetting.setPort(port);
            customRTKSetting.setUser(user);
            customRTKSetting.setPwd(pwd);
            customRTKSetting.setMountpoint(mountpoint);
            SpUtil.setCustomRTKSetting(customRTKSetting);

            RTKCustomNetworkSetting rtkCustomNetworkSetting = new RTKCustomNetworkSetting();
            rtkCustomNetworkSetting.setServerAddress(host);
            rtkCustomNetworkSetting.setPort(Integer.valueOf(port));
            rtkCustomNetworkSetting.setUserName(user);
            rtkCustomNetworkSetting.setPassword(pwd);
            rtkCustomNetworkSetting.setMountPoint(mountpoint);
            RTKCenter.getInstance().getCustomRTKManager().setCustomNetworkRTKSettings(rtkCustomNetworkSetting);
            RTKCenter.getInstance().getCustomRTKManager().startNetworkRTKService(CoordinateSystem.WGS84, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("设置成功");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("设置失败:"+error.description());
                }
            });
        });

    }

    @Override
    public void removeListener() {
        //ToastUtil.show("移除CMCC监听");
        RTKCenter.getInstance().getCMCCRTKManager().clearAllNetworkRTKServiceInfoListener();
        RTKCenter.getInstance().getCustomRTKManager().clearAllNetworkRTKServiceInfoListener();
        RTKCenter.getInstance().getQXRTKManager().clearAllNetworkRTKServiceInfoListener();
        RTKCenter.getInstance().clearAllRTKLocationInfoListener();
    }

    private void syncRtkData(){
        /*JSONObject data = new JSONObject();
        try {
            JSONArray array = new JSONArray();
            array.add(lon);
            array.add(lat);
            data.put("supplementLonlat", array);
            data.put("supplementHeight",tuoQiuHeight);
            String sn = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber));
            data.put("sn", sn);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        Log.e("TAG", "syncRtkData: " + String.valueOf(data));
        RequestBody requestBody = RequestBody.create(JSON, String.valueOf(data));
        String url = NetConfig.LGURl3 + "thirdParty/supplementLocationInfo";
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("同步椭球高失败:" + e.getLocalizedMessage());
                    }
                });
                Log.e("TAG", "syncRtkData onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("syncRtkData", "onResponse: " + result);
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(result);
                            if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                                ToastUtil.show("同步椭球高成功");
                            } else {
                                ToastUtil.show("同步椭球高失败，请联系管理员");
                            }
                        } catch (com.alibaba.fastjson.JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });*/
    }


    private void setListIntoMode(int integer) {
        currentPosition = integer;
        TVRTKMode.setText(listRtkMode.get(integer));

        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listRtkMode) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {
                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position == currentPosition) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        } else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            TVRTKMode.setText(listRtkMode.get(position));
            ((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
            if(currentPosition == position){
                return;
            }
            currentPosition = position;

            RTKReferenceStationSource rtkReferenceStationSource;
            if (position == 0) {
                binding.cmccRl.setVisibility(View.VISIBLE);
                binding.customRtkRl.setVisibility(View.GONE);
                binding.qxRl.setVisibility(View.GONE);
                rtkReferenceStationSource = RTKReferenceStationSource.NTRIP_NETWORK_SERVICE;
            } else if (position == 1){
                binding.cmccRl.setVisibility(View.GONE);
                binding.qxRl.setVisibility(View.GONE);
                binding.customRtkRl.setVisibility(View.VISIBLE);
                rtkReferenceStationSource = RTKReferenceStationSource.CUSTOM_NETWORK_SERVICE;

                setCustomRTKText();
            }else {
                binding.qxRl.setVisibility(View.VISIBLE);
                binding.cmccRl.setVisibility(View.GONE);
                binding.customRtkRl.setVisibility(View.GONE);
                rtkReferenceStationSource = RTKReferenceStationSource.QX_NETWORK_SERVICE;
            }
            RTKCenter.getInstance().setRTKReferenceStationSource(rtkReferenceStationSource, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("RTK切换成功，需要重启无人机");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("RTK切换失败：" + error.description());
                }
            });
        });
    }

    private void setCustomRTKText() {
        CustomRTKSetting customRTKSetting = SpUtil.getCustomRTKSetting();
        if(customRTKSetting == null){
            return;
        }
        binding.editHost.setText(customRTKSetting.getHost());
        binding.editPort.setText(customRTKSetting.getPort());
        binding.editUser.setText(customRTKSetting.getUser());
        binding.editPwd.setText(customRTKSetting.getPwd());
        binding.editMountpoint.setText(customRTKSetting.getMountpoint());
    }

    @Override
    public void onClick(View v) {
        View anchor = TVRTKMode;
        ((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).showPopup(anchor, mListView, listRtkMode.size());
    }
}
