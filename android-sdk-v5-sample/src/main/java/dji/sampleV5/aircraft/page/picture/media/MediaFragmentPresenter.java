package dji.sampleV5.aircraft.page.picture.media;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import dji.sampleV5.aircraft.BR;


public class MediaFragmentPresenter extends BaseObservable {

    @Bindable
    public Boolean tabAll=true;
    public void setTabAll(Boolean tabAll) {
        this.tabAll = tabAll;
        notifyPropertyChanged(BR.tabAll);
    }

    @Bindable
    public Boolean isEmpty;
    public void setEmpty(Boolean isEmpty) {
        this.isEmpty = isEmpty;
        notifyPropertyChanged(BR.isEmpty);
    }

    @Bindable
    public Boolean tabImage;
    public void setTabImage(Boolean tabImage) {
        this.tabImage = tabImage;
        notifyPropertyChanged(BR.tabImage);
    }

    @Bindable
    public Boolean tabVideo;
    public void setTabVideo(Boolean tabVideo) {
        this.tabVideo = tabVideo;
        notifyPropertyChanged(BR.tabVideo);
    }

    @Bindable
    public Boolean selectDelete=false;
    public void setSelectDelete(Boolean selectDelete) {
        this.selectDelete = selectDelete;
        notifyPropertyChanged(BR.selectDelete);
    }

}
