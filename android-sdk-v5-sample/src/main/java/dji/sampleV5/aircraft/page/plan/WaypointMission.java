package dji.sampleV5.aircraft.page.plan;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.v5.manager.datacenter.media.MediaFile;

public class WaypointMission extends BaseMission {

    private HeadingMode headingMode;
    private FinishedAction finishedAction;
    private List<Waypoint> waypointList = new ArrayList<>();

    public WaypointMission() {
    }

    private String globalWaypointTurnMode;

    private int myMissionType = 0;

    private String heightType;

    private int uavSTAltitude;
    private int uavRHAltitude;

    private boolean isOfflineMission = false;

    private int missionCheck = 0;

    public void setMissionCheck(int missionCheck) {
        this.missionCheck = missionCheck;
    }

    public int getMissionCheck() {
        return missionCheck;
    }

    public void setOfflineMission(boolean offlineMission) {
        isOfflineMission = offlineMission;
    }

    public boolean isOfflineMission() {
        return isOfflineMission;
    }

    public int getUavSTAltitude() {
        return uavSTAltitude;
    }

    public void setUavSTAltitude(int uavSTAltitude) {
        this.uavSTAltitude = uavSTAltitude;
    }

    public int getUavRHAltitude() {
        return uavRHAltitude;
    }

    public void setUavRHAltitude(int uavRHAltitude) {
        this.uavRHAltitude = uavRHAltitude;
    }

    public String getHeightType() {
        return heightType;
    }

    public void setHeightType(String heightType) {
        this.heightType = heightType;
    }

    public void setMyMissionType(int myMissionType) {
        this.myMissionType = myMissionType;
    }

    public int getMyMissionType() {
        return myMissionType;
    }

    public String getGlobalWaypointTurnMode() {
        return globalWaypointTurnMode;
    }

    public void setGlobalWaypointTurnMode(String globalWaypointTurnMode) {
        this.globalWaypointTurnMode = globalWaypointTurnMode;
    }

    public HeadingMode getHeadingMode() {
        return headingMode;
    }

    public void setHeadingMode(HeadingMode headingMode) {
        this.headingMode = headingMode;
    }

    public FinishedAction getFinishedAction() {
        return finishedAction;
    }

    public void setFinishedAction(FinishedAction finishedAction) {
        this.finishedAction = finishedAction;
    }

    public List<Waypoint> getWaypointList() {
        return waypointList;
    }

    public void setWaypointList(List<Waypoint> waypointList) {
        this.waypointList.clear();
        this.waypointList.addAll(waypointList);
    }

    public void addWaypoint(Waypoint wp) {
        this.waypointList.add(wp);
    }

    public void addAll(List<Waypoint> waypointList) {
        this.waypointList.addAll(waypointList);
    }

    @Override
    public String toString() {
        return super.toString() + "\nWaypointMission{" +
                "headingMode=" + headingMode +
                ", finishedAction=" + finishedAction +
                ", waypointList=" + waypointList +
                '}';
    }

    public enum HeadingMode {
        Auto,
        UsingWaypointHeading
    }


    public static class Waypoint {
        private String meterType;
        private String deviceName;
        private String pointName;
        private String photoName;
        private MediaFile mediaFile;            // 类型：photo，video
        private short heading;                  // 相机朝向
        private short gimbalPitch;              // 云台俯仰角

        private float altitude;                 // 高度
        private TurnMode turnMode;              // 旋转类型
        private float speed;
        private String waypointTurnMode;

        private AppLatLng latLng = new AppLatLng();
        private List<Action> waypointActions = new ArrayList<>();

        public Waypoint() {
        }

        public String getWaypointTurnMode() {
            return waypointTurnMode;
        }

        public void setWaypointTurnMode(String waypointTurnMode) {
            this.waypointTurnMode = waypointTurnMode;
        }

        public float getSpeed() {
            return speed;
        }

        public void setSpeed(float speed) {
            this.speed = speed;
        }

        public AppLatLng getLatLng() {
            return latLng;
        }

        public void setLatLng(AppLatLng latLng) {
            this.latLng.setLat(latLng.getLat());
            this.latLng.setLng(latLng.getLng());
        }

        public float getAltitude() {
            return altitude;
        }

        public void setAltitude(float altitude) {
            this.altitude = altitude;
        }

        public short getHeading() {
            return heading;
        }

        public void setHeading(short heading) {
            this.heading = heading;
        }

        public TurnMode getTurnMode() {
            return turnMode;
        }

        public void setTurnMode(TurnMode turnMode) {
            this.turnMode = turnMode;
        }

        public short getGimbalPitch() {
            return gimbalPitch;
        }

        public void setGimbalPitch(short gimbalPitch) {
            this.gimbalPitch = gimbalPitch;
        }

        public List<Action> getWaypointActions() {
            return waypointActions;
        }

        public void setWaypointActions(List<Action> waypointActions) {
            this.waypointActions.clear();
            this.waypointActions.addAll(waypointActions);
        }

        public void addWaypointAction(Action action) {
            this.waypointActions.add(action);
        }

        public void addAll(List<Action> waypointActions) {
            this.waypointActions.addAll(waypointActions);
        }

        @Override
        public String toString() {
            return "DIYWaypoint{" +
                    "latLng=" + latLng +
                    ", altitude=" + altitude +
                    ", heading=" + heading +
                    ", turnMode=" + turnMode +
                    ", gimbalPitch=" + gimbalPitch +
                    ", waypointActions=" + waypointActions +
                    '}';
        }

        public String getMeterType() {
            return meterType;
        }

        public void setMeterType(String meterType) {
            this.meterType = meterType;
        }

        public String getDeviceName() {
            return deviceName;
        }

        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }

        public String getPointName() {
            return pointName;
        }

        public void setPointName(String pointName) {
            this.pointName = pointName;
        }

        public String getPhotoName() {
            return photoName;
        }

        public void setPhotoName(String photoName) {
            this.photoName = photoName;
        }

        public MediaFile getMediaFile() {
            return mediaFile;
        }

        public void setMediaFile(MediaFile mediaFile) {
            this.mediaFile = mediaFile;
        }

        /**
         *
         */
        public enum ActionType {
            /**
             * 悬停
             */
            Stay,
            /**
             * 开始拍照
             */
            StartTakePhoto,
            /**
             * 开始录像
             */
            StartRecord,
            /**
             * 结束录像
             */
            StopRecord,
            /**
             * 旋转, 改变横滚角?
             */
            RotateAircraft,
            /**
             * 调整云台俯仰
             */
            GimbalPitch,
            /**
             * 调整航向角
             */
            GimbalYaw,
            /**
             * 变焦
             */
            CameraFocus,
            /**
             * 缩放
             */
            CameraZoom,
        }

        /**
         * 动作 = 动作类型 + 参数
         */
        public static class Action {
            private int actionParam;
            private ActionType actionType;

            public Action() {
            }

            public Action(ActionType actionType, int actionParam) {
                this.actionType = actionType;
                this.actionParam = actionParam;
            }

            public int getActionParam() {
                return actionParam;
            }

            public void setActionParam(int actionParam) {
                this.actionParam = actionParam;
            }

            public ActionType getActionType() {
                return actionType;
            }

            public void setActionType(ActionType actionType) {
                this.actionType = actionType;
            }

            @Override
            public String toString() {
                return "DIYWaypointAction{" +
                        "mActionParam=" + actionParam +
                        ", mActionType=" + actionType +
                        '}';
            }
        }
    }
}
