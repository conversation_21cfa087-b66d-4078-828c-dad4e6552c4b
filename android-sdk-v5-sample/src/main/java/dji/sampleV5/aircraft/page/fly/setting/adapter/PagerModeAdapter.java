package dji.sampleV5.aircraft.page.fly.setting.adapter;

import android.graphics.Color;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;

public class PagerModeAdapter extends BaseAdapter {

    private int[] remoteArray;
    private int currentPosition;

    public PagerModeAdapter(int[] remote, int position) {
        remoteArray = remote;
        currentPosition = position;
    }

    public void getPosition(int position) {
        currentPosition = position;
    }

    @Override
    public int getCount() {
        return remoteArray.length;
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            viewHolder = new ViewHolder();
            convertView = View.inflate(ContextUtil.getApplicationContext(), R.layout.one_center_item, null);
            viewHolder.tv_text = convertView.findViewById(R.id.tv_center_item);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }
        int item = remoteArray[position];
        if (currentPosition == position) {
            viewHolder.tv_text.setTextColor(ContextUtil.getApplicationContext().getResources().getColor(R.color.colorBright));
        } else {
            viewHolder.tv_text.setTextColor(Color.WHITE);
        }
        viewHolder.tv_text.setText(item);
        return convertView;
    }

    static class ViewHolder {
        TextView tv_text;
    }
}
