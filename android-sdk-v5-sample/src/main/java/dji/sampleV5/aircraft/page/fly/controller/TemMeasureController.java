package dji.sampleV5.aircraft.page.fly.controller;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.HandlerThread;
import android.text.TextUtils;
import android.util.Log;
import android.view.PixelCopy;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Locale;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.event.Events;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.FormatUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.DJIKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.ThermalAreaMetersureTemperature;
import dji.sdk.keyvalue.value.camera.ThermalTemperatureMeasureMode;
import dji.sdk.keyvalue.value.common.Attitude;
import dji.sdk.keyvalue.value.common.CameraLensType;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.DoubleRect;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class TemMeasureController {
    private static final String TAG = TemMeasureController.class.getName();
    private DefaultLayoutActivity activity;
    private ActivityDefaultLayoutBinding binding;
    private MissionController missionController;
    private DJIKey temKey = KeyTools.createCameraKey(CameraKey.KeyThermalRegionMetersureTemperature, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL);
    private int lastPosition = -1;
    private double lastMaxTem = 0;
    private LocationCoordinate2D lastLocationCoordinate2D;
    private long lastWarningTime = 0;
    private ThermalAreaMetersureTemperature maxPoint;

    public TemMeasureController(DefaultLayoutActivity activity, MissionController missionController) {
        this.activity = activity;
        this.binding = activity.getBinding();
        this.missionController = missionController;
    }

    public void startMeasureTem(boolean isUploadPicture) {
        if (TextUtils.equals(binding.tvMode.getText(), "红外")) {
            activity.changeDisplayMode(2);
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalTemperatureMeasureMode, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL), ThermalTemperatureMeasureMode.REGION, new CommonCallbacks.CompletionCallback() {
                        @Override
                        public void onSuccess() {
                            XLogUtil.INSTANCE.e(TAG, "设置红外区域测温成功: ");
                            //DoubleRect rectF = new DoubleRect((double)(oval.left - paddingStart) / fpvWidth, (double)(oval.top - paddingTop) / fpvHeight, (double)(oval.right - paddingStart) / fpvWidth, (double)(oval.bottom - paddingTop) / fpvHeight);
                            DoubleRect doubleRect = new DoubleRect(0.1, 0.1, 0.8, 0.8);
                            KeyManager.getInstance().setValue(KeyTools.createCameraKey(CameraKey.KeyThermalRegionMetersureArea, ComponentIndexType.LEFT_OR_MAIN, CameraLensType.CAMERA_LENS_THERMAL), doubleRect, new CommonCallbacks.CompletionCallback() {
                                @Override
                                public void onSuccess() {
                                    XLogUtil.INSTANCE.e(TAG, "设置红外区域成功: ");
                                }

                                @Override
                                public void onFailure(@NonNull IDJIError error) {
                                    XLogUtil.INSTANCE.e(TAG, "设置红外区域失败: " + error.description());
                                }
                            });
                            KeyManager.getInstance().listen(temKey, this, new CommonCallbacks.KeyListener<ThermalAreaMetersureTemperature>() {
                                @Override
                                public void onValueChange(@Nullable ThermalAreaMetersureTemperature oldValue, @Nullable ThermalAreaMetersureTemperature newValue) {
                                    if (newValue != null) {
                                        DJIAircraftApplication.getInstance().setThermalAreaMetersureTemperature(newValue);
                                        if(!isUploadPicture){ //如果是正常的测温，则不需要上传识别到的图片
                                            return;
                                        }
                                        long timeInterval = System.currentTimeMillis() - lastWarningTime;
                                        if (timeInterval > 1500) {
                                            //0.7609000205993652 y:0.0215000007301569
                                            if(newValue.getMaxTemperaturePoint().getX() < 0.1 || newValue.getMaxTemperaturePoint().getX() > 0.8){
                                                return;
                                            }
                                            if(newValue.getMaxTemperaturePoint().getY() < 0.1 || newValue.getMaxTemperaturePoint().getY() > 0.8){
                                                return;
                                            }
                                            Log.e(TAG, "x: " + newValue.getMaxTemperaturePoint().getX() + " y:" + newValue.getMaxTemperaturePoint().getY());
                                            lastWarningTime = System.currentTimeMillis();
                                            maxPoint = newValue;
                                            Surface surface = activity.primaryFpvWidget.getSurfaceView().getHolder().getSurface();
                                            capture(surface, activity.primaryFpvWidget.getWidth(), activity.primaryFpvWidget.getHeight());
                                        }
                                        //Log.e(TAG, ": " + newValue.getMaxAreaTemperature() + " " + newValue.getMinAreaTemperature());
                                        double maxTem = newValue.getMaxAreaTemperature();  //现场反馈测出来的高温高出3倍，做一下处理
                                        int currentPosition = missionController.getCurrentWaypointIndex();
                                        if (currentPosition == -1) {
                                            return;
                                        }
                                        if (newValue.getMaxTemperaturePoint().getX() < 0.1 || newValue.getMaxTemperaturePoint().getX() > 0.8) {
                                            return;
                                        }
                                        if (newValue.getMaxTemperaturePoint().getY() < 0.1 || newValue.getMaxTemperaturePoint().getY() > 0.8) {
                                            return;
                                        }
                                        WaypointMission waypointMission = missionController.getmChosenMission();
                                        WaypointMission.Waypoint waypoint = waypointMission.getWaypointList().get(currentPosition);
                                        LocationCoordinate2D locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
                                        float distance = 0.5f; //这个是距离上一次预警的距离
                                        float waypointDistance = 0.5f; //这个是距离航点的距离
                                        double yawGap = 5;
                                        if (lastLocationCoordinate2D != null && locationCoordinate2D != null) {
                                            distance = LatLngUtil.calculateLineDistance(lastLocationCoordinate2D.getLatitude(), lastLocationCoordinate2D.getLongitude(),
                                                    locationCoordinate2D.getLatitude(), locationCoordinate2D.getLongitude());
                                        }
                                        if (locationCoordinate2D != null) {
                                            waypointDistance = LatLngUtil.calculateLineDistance(waypoint.getLatLng().getLat(), waypoint.getLatLng().getLng(),
                                                    locationCoordinate2D.getLatitude(), locationCoordinate2D.getLongitude());
                                        }
                                        double aircraftAltRel = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAltitude));
                                        double heightDistance = Math.abs(aircraftAltRel - waypoint.getAltitude());
                                        Attitude aircraftAttitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftAttitude));
                                        if (aircraftAttitude != null) {
                                            double yaw = aircraftAttitude.getYaw();
                                            yawGap = Math.abs(yaw - waypoint.getHeading());
                                        }
                                        long timeInterval1 = System.currentTimeMillis() - lastWarningTime;
                                        XLogUtil.INSTANCE.e(TAG, " pointIndex" + currentPosition + " waypointDistance:" + waypointDistance +
                                                " heightDistance:" + heightDistance + " yawGap:" + yawGap);
                                        if (waypointDistance < 0.25 && heightDistance < 0.25 && yawGap < 2  && timeInterval1 > 1100 && maxTem > SpUtil.getWarningTem()) {
                                            XLogUtil.INSTANCE.e(TAG, "x: " + newValue.getMaxTemperaturePoint().getX() + " y:" + newValue.getMaxTemperaturePoint().getY());
                                            lastPosition = currentPosition;  //需要判断一下识别到的航点位置跟实际航点位置
                                            lastMaxTem = maxTem;
                                            maxPoint = newValue;
                                            lastLocationCoordinate2D = locationCoordinate2D;
                                            lastWarningTime = System.currentTimeMillis();
                                            Surface surface = activity.primaryFpvWidget.getSurfaceView().getHolder().getSurface();
                                            capture(surface, activity.primaryFpvWidget.getWidth(), activity.primaryFpvWidget.getHeight());
                                        }
                                    }
                                }
                            });

                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {
                            if (error != null) {
                                XLogUtil.INSTANCE.e("设置红外区域测温失败", "onFailure: " + error.description());
                            }

                        }
                    });
            /*thermalAreaMeterView.startMeasureTem();
            thermalAreaMeterView.setVisibility(View.VISIBLE);*/
                }
            }, 3000);
        }
    }

    private void capture(Surface surface, int width, int height) {
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.N) return;
        PixelCopy.OnPixelCopyFinishedListener listener = new PixelCopyFinishedListener(bitmap);
        HandlerThread ht = new HandlerThread("ht");
        ht.start();
        Handler handler = new Handler(ht.getLooper());
        PixelCopy.request(surface, bitmap, listener, handler);
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    class PixelCopyFinishedListener implements PixelCopy.OnPixelCopyFinishedListener {
        Bitmap bitmap = null;

        public PixelCopyFinishedListener(Bitmap bitmap) {
            this.bitmap = bitmap;
        }

        @Override
        public void onPixelCopyFinished(int i) {
            if (PixelCopy.SUCCESS != i) {
                return;
            } else {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        int widthTem = 75;
                        if (activity.currentIndex == Events.IndexEvent.INDEX_MAP) { // 根据当前索引状态确定裁剪宽度
                            widthTem = 310;
                        }
                        // 定义裁剪区域并创建新位图
                        Rect rect = new Rect(widthTem, 0, bitmap.getWidth() - widthTem, bitmap.getHeight());
                        Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, rect.left, rect.top, rect.width(), rect.height());
                        Canvas cnvs = new Canvas(croppedBitmap);
                        Paint paint = new Paint();
                        paint.setColor(Color.BLUE); // 设置绘制颜色
                        paint.setStyle(Paint.Style.STROKE); // 设置绘制样式为描边
                        if (activity.currentIndex == Events.IndexEvent.INDEX_MAP) {
                            paint.setStrokeWidth(4); // 地图状态下描边宽度为4
                        } else {
                            paint.setStrokeWidth(1); // 其他状态描边宽度为1
                        }

                        // 计算并绘制最大温度显示框
                        Log.e(TAG, "cnvs.getWidth: " + cnvs.getWidth() + " croppedBitmap.width" + croppedBitmap.getWidth());
                        int left = (int) (cnvs.getWidth() * maxPoint.getMaxTemperaturePoint().getX()) - widthTem / 3;
                        int top = (int) (cnvs.getHeight() * maxPoint.getMaxTemperaturePoint().getY()) - widthTem / 3;
                        int right = (int) (cnvs.getWidth() * maxPoint.getMaxTemperaturePoint().getX()) + widthTem / 3;
                        int bottom = (int) (cnvs.getHeight() * maxPoint.getMaxTemperaturePoint().getY()) + widthTem / 3;
                        cnvs.drawRect(left, top, right, bottom, paint);

                        // 设置绘制文字样式，并在画布上绘制最大温度值
                        paint.setStyle(Paint.Style.FILL);
                        if (activity.currentIndex == Events.IndexEvent.INDEX_MAP) {
                            paint.setTextSize(60); // 地图状态下字体大小为60
                        } else {
                            paint.setTextSize(15); // 其他状态字体大小为15
                            paint.setStrokeWidth(0.2f); // 设置文字描边宽度
                        }

                        // 在画布上绘制温度值
                        cnvs.drawText(FormatUtil.getDoubleByFormat(lastMaxTem, 1) + "℃",
                                maxPoint.getMaxTemperaturePoint().getX().floatValue() * cnvs.getWidth() - widthTem / 4,
                                maxPoint.getMaxTemperaturePoint().getY().floatValue() * cnvs.getHeight(), paint);
                        // 调用函数上传处理后的位图
                        uploadTemPicture(croppedBitmap);
                        /*Bitmap croppedBitmap = Bitmap.createBitmap(bitmap, rect.left, rect.top, rect.width(), rect.height());
                        binding.test.setImageBitmap(croppedBitmap);*/
                    }
                });
            }
        }
    }

    public void stopMeasureTem() {
        DJIAircraftApplication.getInstance().setThermalAreaMetersureTemperature(null);
        KeyManager.getInstance().cancelListen(temKey);
        //thermalAreaMeterView.stopMeasureTem();
    }

    private void uploadTemPicture(Bitmap bitmap) {
        String path = saveBitmap(bitmap, String.valueOf(System.currentTimeMillis()));
        File file = new File(path);
        OkHttpClient httpClient = new OkHttpClient();
        MediaType mediaType = MediaType.parse("image/*");
        RequestBody requestBody = RequestBody.create(mediaType, file);//把文件与类型放入请求体

        MultipartBody multipartBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), requestBody)//文件名
                .build();
        String url = ApiConfig.INSTANCE.getAI_URL() + "AI/WX/App/" + missionController.getmChosenMission().getMissionBatch() + "/PictureUpload";
        url = url.replace("#", "%23");
        Log.e(TAG, "onBitmapReady: " + url);
        Request request = new Request.Builder()
                .url(url)
                .post(multipartBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("上传失败" + e.getLocalizedMessage());
                    }
                });
                Log.e(TAG, "onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                final String resdata = response.body().string();
                Log.e(TAG, "上传成功: " + resdata);
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            JSONObject json = new JSONObject(resdata);
                            if (json.optInt("code") == 500) {
                                ToastUtil.show("上传测温图片失败");
                            } else {
                                //ToastUtil.show("上传测温图片成功");
                                String url = json.optString("data");
                                noticeAI(url);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }

    private void noticeAI(String picUrl) {
        JSONObject data = new JSONObject();
        try {
            lastLocationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
            if (lastLocationCoordinate2D != null) {
                Double aircraftLat = lastLocationCoordinate2D.getLatitude();
                Double aircraftLng = lastLocationCoordinate2D.getLongitude();
                data.put("longitudeWGS", aircraftLng);
                data.put("latitudeWGS", aircraftLat);
            }
            data.put("missionBatch", missionController.getmChosenMission().getMissionBatch());
            data.put("eventPicUrl", picUrl);
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
            data.put("eventTime", sf.format(System.currentTimeMillis()));
            JSONArray eventInfoArray = new JSONArray();
            JSONObject eventInfo = new JSONObject();
            eventInfo.put("cls", missionController.getmChosenMission().getWaypointList().get(lastPosition).getPointName()
                    + " 温度" + FormatUtil.getDoubleByFormat(lastMaxTem, 1) + "℃");
            eventInfoArray.put(eventInfo);
            data.put("eventInfo", eventInfoArray);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        Log.e(TAG, "noticeAI: " + String.valueOf(data));
        RequestBody requestBody = RequestBody.create(JSON, String.valueOf(data));
        String url = ApiConfig.INSTANCE.getAI_URL() + "AI/WX/App/EventInsert";
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ToastUtil.show("预警上报失败:" + e.getLocalizedMessage());
                    }
                });
                Log.e(TAG, "noticeAI onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                Log.e("FtpConnectionUpload", "noticeAI onResponse: " + call.toString() + "  response:" + response.toString());
                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //ToastUtil.show("预警上报成功");
                    }
                });
            }
        });
    }

    private String saveBitmap(Bitmap bitmap, String filename) {
        String savePath = Environment.getExternalStorageDirectory().getPath() + "/DCIM/SKYSYS/" + filename + ".jpg";
        File f = new File(savePath);
        if (f.exists()) f.delete();

        File fileParent = f.getParentFile();
        if (!fileParent.exists()) {
            fileParent.mkdirs();
        }

        try {
            if (f.createNewFile()) {
                FileOutputStream out = new FileOutputStream(f);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
                out.flush();
                out.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bitmap != null) {
                bitmap.recycle();
            }
        }
        return savePath;
    }
}
