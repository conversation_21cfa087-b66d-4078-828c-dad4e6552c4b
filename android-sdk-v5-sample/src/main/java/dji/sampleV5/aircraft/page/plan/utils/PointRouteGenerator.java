package dji.sampleV5.aircraft.page.plan.utils;



import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;

public class PointRouteGenerator {

    public static void generator(WaypointMission mission) throws Exception {
        List<WaypointMission.Waypoint> list = mission.getWaypointList();
        short num = (short) list.size();
        int length = 0;
        int time;
        mission.setWaypointNum(num);
        if (list.size() < 2) {
            throw new Exception();
        }

        for (int i = 0; i < num - 1; i++) {
            AppLatLng start = list.get(i).getLatLng();
            AppLatLng end = list.get(i + 1).getLatLng();
            double distance = LatLngUtil.calculateLineDistance(start.getLat(), start.getLng(), end.getLat(), end.getLng());
            length += distance;
        }
        time = (int) (length / mission.getFlySpeed());
        mission.setRouteLength(length);
        mission.setPreFlightTime(time);
    }
}
