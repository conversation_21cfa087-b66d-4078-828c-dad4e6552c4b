package dji.sampleV5.aircraft.page.fly.controller;

import android.app.AlertDialog;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.ftp.FtpConfig;
import dji.sampleV5.aircraft.ftp.FtpConnectionUpload;
import dji.sampleV5.aircraft.ftp.OnThreadResultListener;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.picture.media.MediaBean;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.value.camera.DateTime;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaFileListState;
import dji.v5.manager.datacenter.media.MediaFileListStateListener;
import dji.v5.manager.datacenter.media.MediaManager;
import dji.v5.manager.datacenter.media.PullMediaFileListParam;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


public class MissionPhotoUploader {

    private static final int SHOW_PROGRESS_DIALOG = 2;
    private static final int HIDE_PROGRESS_DIALOG = 3;
    private static final int NOT_PICTURE = 4;
    private static final int HAVE_PICTURE = 5;
    //private SettingsDefinitions.StorageLocation mediaType = SettingsDefinitions.StorageLocation.SDCARD;
    //private WaypointTable table;
    private ActivityDefaultLayoutBinding binding;
    private static MissionPhotoUploader instance;
    //private List<WaypointMission.Waypoint> wayPointList;
    private ArrayList<MediaFile> downloadMediaList = new ArrayList<>();
    private ArrayList<String> newFileNameList = new ArrayList<>();
    private List<MediaFile> mediaFiles;
    private String missionId;
    private boolean isUploading;
    private String ftpPath;
    private AlertDialog netDialog;
    private FtpConnectionUpload ftpConnectionUpload;
    public void setUploading(boolean uploading) {
        isUploading = uploading;
    }

    private Runnable downloadRunnable = new Runnable() {
        @Override
        public void run() {
            binding.downloadLL.setVisibility(View.VISIBLE);
            binding.downloadLL.bringToFront();
            MediaDownloadController.getInstance().setOnProgressListener(new MediaDownloadController.OnProgressUpdateListener() {
                @Override
                public void onProgressUpdate(final long total, final long current, final String fileName, final int currentPosition) {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            binding.downloadText.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (currentPosition + 1) + "个 " + fileName);
                            binding.downloadProgressBar.setProgress((int) (current * 100 / total));
                            binding.downPercent.setText((int) (current * 100 / total) + "%");
                        }
                    });
                }

                @Override
                public void onSuccess(String fileName) {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            binding.downloadText.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 " + fileName);
                            binding.downloadProgressBar.setProgress(100);
                            binding.downPercent.setText("100%");
                            Log.e("TAG", "总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载完第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 " + fileName);
                            /*if (MediaDownloadController.getInstance().getCurrentPosition() < MediaDownloadController.getInstance().getTotalCount()) {
                                ToastUtil.show("总个数" + MediaDownloadController.getInstance().getTotalCount() + " 当前第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 ");
                            }

                            if (MediaDownloadController.getInstance().getTotalCount() == (MediaDownloadController.getInstance().getCurrentPosition() + 1)) {
                                Log.e("TAG", "run: 全部下载完成,开始上传");
                                binding.downloadText.setText("全部下载完成,开始上传");
                                binding.btnWarning.setText("正在上传文件，请稍等");
                                ToastUtil.show("全部下载完成");
                                upload();
                            }*/
                        }
                    });

                }

                @Override
                public void onFinish() {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            Log.e("TAG", "run: 全部下载完成,开始上传");
                            binding.downloadText.setText("全部下载完成,开始上传");
                            binding.btnWarning.setText("正在上传文件，请稍等");
                            ToastUtil.show("全部下载完成");
                            upload();
                        }
                    });

                }
            });
            MediaDownloadController.getInstance().startDownload();
        }
    };

    private MissionPhotoUploader() {
    }

    public static MissionPhotoUploader getInstance() {
        if (instance == null) {
            instance = new MissionPhotoUploader();
        }
        return instance;
    }


    //可能会多次进入，要加是否有上传任务判断条件
    public void onMissionComplete(String missionId, ActivityDefaultLayoutBinding binding) {
        if (!isUploading) {
            ToastUtil.show("开始准备数据，请稍等");
            this.binding = binding;
            this.missionId = missionId;
            initNetDialog();
            String date = stampToDate(System.currentTimeMillis());
            UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();
            if (uavInfoSN == null) {
                ToastUtil.show("没有获取到站点数据，请联系后台！");
                return;
            }
            ftpPath = FtpConfig.ftpPath + uavInfoSN.getData().getSiteInfo().getSiteID() + "/" + date + "/" + uavInfoSN.getData().getUAVID() + "/" + missionId + "/";
            isUploading = true;
            //table = new WaypointTable(ContextUtil.getApplicationContext());
            //wayPointList = table.getAll();
            initData();
        } else {
            ToastUtil.show("已有上传任务，此次不上传！");
        }
    }

    private void initData() {
        MediaManager.getInstance().enable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                MediaManager.getInstance().addMediaFileListStateListener(mediaFileListState -> {
                    if (mediaFileListState == MediaFileListState.UP_TO_DATE) {
                        ToastUtil.show("加载图库...");
                        mediaFiles = MediaManager.getInstance().getMediaFileListData().getData();

                        if (downloadMediaList != null) {
                            downloadMediaList.clear();
                            newFileNameList.clear();
                        }

                        if (mediaFiles.size() <= 0) {
                            finish();
                            ToastUtil.show("图库为空");
                            return;
                        }

                        MissionDetail missionDetail = SpUtil.getMissionData(missionId);
                        for (int i = mediaFiles.size() - 1; i >= 0; i--) {
                            final MediaFile media = mediaFiles.get(i);
                            DateTime dateTime = media.getDate();
                            Calendar calendar = Calendar.getInstance();
                            calendar.set(dateTime.getYear(), dateTime.getMonth() - 1, dateTime.getDay(), dateTime.getHour(), dateTime.getMinute(), dateTime.getSecond());
                            long time = calendar.getTime().getTime();
                            if (time > missionDetail.getStartTime() && time < missionDetail.getEndTime()) {
                                downloadMediaList.add(media);
                                newFileNameList.add(media.getFileName());
                            }
                        }

                        if (downloadMediaList.size() > 0) {
                            ToastUtil.show("匹配到几张图片" + downloadMediaList.size());
                            MediaDownloadController.getInstance().setCheckedMediaFileList(downloadMediaList, newFileNameList);
                            ContextUtil.getHandler().post(downloadRunnable);
                        } else {
                            finish();
                            ToastUtil.show("没有匹配到数据！");
                        }
                    }
                });

                MediaManager.getInstance().pullMediaFileListFromCamera(new PullMediaFileListParam.Builder().build(), new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {

                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("pullMediaFileListFromCamera:" + error.description());
                    }
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("enable error:" + error.description());
            }
        });
    }

    private void finish() {
        isUploading = false;
        MediaManager.getInstance().removeAllMediaFileListStateListener();
        MediaManager.getInstance().disable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("disable error:" + error.description());
            }
        });
    }

    private void upload() {
        ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        ftpConnectionUpload = new FtpConnectionUpload(missionId, ftpPath, downloadMediaList, 0, new OnThreadResultListener() {
                            @Override
                            public void onFinish(int index) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        Log.e("FtpConnectionUpload", "onFinish: " + index + "总文件个数" + downloadMediaList.size() + "    当前上传第" + (index + 1) + "个 ");
                                        binding.downloadText.setText("总文件个数" + downloadMediaList.size() + "    当前上传第" + (index + 1) + "个 ");
                                        binding.downloadProgressBar.setProgress((int) ((index + 1) * 100 / downloadMediaList.size()));
                                        binding.downPercent.setText((int) (index * 100 / downloadMediaList.size()) + "%");

                                        if (index == downloadMediaList.size() - 1) {
                                            Log.e("FtpConnectionUpload", "结束");
                                            SpUtil.setMissionBatch("");
                                            SpUtil.remove(missionId);
                                            ToastUtil.show("开始从图库中删除任务图片");
                                            MediaManager.getInstance().removeAllMediaFileListStateListener();
                                            MediaManager.getInstance().deleteMediaFiles(downloadMediaList, new CommonCallbacks.CompletionCallback() {
                                                @Override
                                                public void onSuccess() {
                                                    ToastUtil.show("删除成功");
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });

                                                    finish();
                                                }

                                                @Override
                                                public void onFailure(@NonNull IDJIError djiError) {
                                                    if (djiError != null) {
                                                        ToastUtil.show("djiError:" + djiError.description());
                                                    }
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                }
                                            });
                                        }
                                    }
                                });

                            }

                            @Override
                            public void onError(int index, String error, File localFile) {
                                ContextUtil.getHandler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.downloadText.setText("上传失败：" + index + ":" + error);
                                        ToastUtil.show("上传失败：" + index + ":" + error);
                                        if (!netDialog.isShowing()) {
                                            netDialog.show();
                                        }
                                        finish();
                                    }
                                });
                                Log.e("FtpConnectionUpload", "onError  index: " + index + "  error:" + error);
                            }
                        });
                        ftpConnectionUpload.start();
                    }
                }).start();
            }
        });
    }

    private void initNetDialog() {
        if (netDialog == null) {
            netDialog = new AlertDialog.Builder(ContextUtil.getCurrentActivity())
                    .setMessage("网络异常，请检查网络,点击确定可以尝试继续上传")
                    .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                        if (ftpConnectionUpload != null) {
                            new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    ftpConnectionUpload.reConnect();
                                    ftpConnectionUpload.start();
                                }
                            }).start();
                        }
                    })
                    .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                        binding.downloadLL.setVisibility(View.INVISIBLE);
                        finish();
                    })
                    .create();
            netDialog.setCanceledOnTouchOutside(false);
        }

    }

    public String stampToDate(long timeMillis) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(timeMillis);
        return simpleDateFormat.format(date);
    }

}
