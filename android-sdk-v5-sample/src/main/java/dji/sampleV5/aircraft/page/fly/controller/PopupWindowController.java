package dji.sampleV5.aircraft.page.fly.controller;


import java.lang.ref.WeakReference;

import dji.sampleV5.aircraft.view.RemindPopupWindow;

/**
 * 飞行界面提示窗管理类
 */

public class PopupWindowController {

    private static final PopupWindowController instance = new PopupWindowController();

    private WeakReference<RemindPopupWindow> popupWindowFromLeft;
    private WeakReference<RemindPopupWindow> popupWindowFromCenter;

    public static PopupWindowController getInstance() {
        return instance;
    }

    public RemindPopupWindow getPopupWindowFromLeft() {
        if (this.popupWindowFromLeft != null) {
            return this.popupWindowFromLeft.get();
        }
        return null;
    }

    public RemindPopupWindow getPopWindowFromCenter() {
        if (this.popupWindowFromCenter != null) {
            return this.popupWindowFromCenter.get();
        }
        return null;
    }

    public void setPopupWindowFromLeft(RemindPopupWindow remindPopupWindow) {
        this.popupWindowFromLeft = new WeakReference<>(remindPopupWindow);
    }

    public void setPopupWindowFromCenter(RemindPopupWindow popupWindowFromCenter) {
        this.popupWindowFromCenter = new WeakReference<>(popupWindowFromCenter);
    }
}
