package dji.sampleV5.aircraft.page.picture.media;

import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.page.picture.PictureFragmentManager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.view.adapter.ViewHolder;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.media.MediaFile;

import java.util.ArrayList;
import java.util.List;


public class MediaTabImageFragment extends MediaBaseFragment implements MediaUpdate {

    private final List<MediaBean> mediaList;
    private final PictureActivity activity;
    private final MediaObserver instence;
    private RecyclerView rv_media_fragment;
    private CommonAdapter<MediaBean> commonAdapter;
    private ArrayList<MediaBean> list = new ArrayList();
    private ArrayList<MediaFile> mediaFilelist = new ArrayList<>();
    //是否选择删除图片
    private boolean isSelect = false;
    //private int mposition=-1;
    //private boolean isSamePosition=true;
    private ArrayList<String> selectedList = new ArrayList<>();

    public MediaTabImageFragment(PictureActivity activity, List<MediaBean> mediaList, MediaObserver instence) {
        super();
        this.activity = activity;
        this.mediaList = mediaList;
        this.instence = instence;
    }


    public void initData() {
        isLoading = false;
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.item_recyclerview, null);
        rv_media_fragment = view.findViewById(R.id.rv_item);

        initRecyclerView();
        flContentMedia.addView(rv_media_fragment);
    }

    private void initRecyclerView() {
        for (int i = 0; i < mediaList.size(); i++) {
            MediaBean mediaBean = mediaList.get(i);
            int mediaType = mediaBean.getMediaType();
            if (mediaType == 0 || mediaType == 5) {
                list.add(mediaBean);
            }
        }
        GridLayoutManager manager = new GridLayoutManager(ContextUtil.getApplicationContext(), 5);
        rv_media_fragment.setLayoutManager(manager);
        commonAdapter = new CommonAdapter<MediaBean>(ContextUtil.getApplicationContext(), R.layout.item_image, list) {
            @Override
            public void convert(final ViewHolder holder, final MediaBean mediaFile, final int position) {
                //如果点击了选择
                if (isSelect) {
                    holder.setTextHide(R.id.tv_select_media, selectedList.contains(mediaFile.getFileName()));
                    /*if (mposition!=-1){
                        holder.setTextHide(R.id.tv_select_media,isSamePosition);
                    }*/
                } else {
                    holder.setTextHide(R.id.tv_select_media, false);
                }
                final int mediaType = mediaFile.getMediaType();
                final MediaFile media = mediaFile.getMedia();
                final int durationInSeconds = (int) mediaFile.getDurationInSeconds();
                final String fileName = media.getFileName();
                holder.setText(R.id.tv_item_label, mediaFile.getFileName());
                holder.setImageTag(R.id.iv_img, fileName);
                Bitmap thumbnail1 = media.getThumbNail();
                if (thumbnail1 == null) {
                    ContextUtil.getHandler().post(() -> holder.setImageBackgroundResource(R.id.iv_img, R.drawable.ic_not_image));
                    media.pullThumbnailFromCamera(new CommonCallbacks.CompletionCallbackWithParam<Bitmap>() {
                        @Override
                        public void onSuccess(Bitmap bitmap) {
                            String imageTag = holder.getImageTag(R.id.iv_img);
                            String name = media.getFileName();
                            if (name.equals(imageTag)) {
                                holder.setImageTag(R.id.iv_img, name);
                                final Bitmap thumbnail1 = media.getThumbNail();
                                setView(thumbnail1, mediaType, holder, durationInSeconds);
                            }
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {

                        }
                    });
                } else {
                    setView(thumbnail1, mediaType, holder, durationInSeconds);
                }

            }
        };
        commonAdapter.setOnItemClickListener((view, position) -> {
            //点击选择不删除图片
            if (isSelect) {
                //获得当前选中的media
                MediaFile media = list.get(position).getMedia();
                String filename = media.getFileName();
                if (selectedList.contains(filename)) {
                    selectedList.remove(filename);
                    mediaFilelist.remove(media);
                } else {
                    selectedList.add(filename);
                    mediaFilelist.add(media);
                }

                /*//在次点击则选中
                if (mposition==position){
                    //是否是相同的position
                    isSamePosition = !isSamePosition;
                }else {
                    isSamePosition=true;
                    if (mediaFilelist.size()==0){
                        isSamePosition=true;
                    }else {
                        int size = mediaFilelist.size();
                        for (int i = 0; i <size ; i++) {
                            if (mediaFilelist.get(i)==media){
                                isSamePosition=false;
                            }
                        }
                    }
                }
                if (isSamePosition){
                    mediaFilelist.add(media);
                }else {
                    mediaFilelist.remove(media);
                }
                mposition=position;*/
                commonAdapter.notifyItemChanged(position);
            } else {
                Bundle bundle = new Bundle();
                bundle.putInt(ContextUtil.getString(R.string.position), position);
                bundle.putParcelableArrayList(ContextUtil.getString(R.string.all), list);
                activity.getPictureFragmentManager().addFragment(PictureFragmentManager.MEDIA_FRAGMENT_SHOW, bundle);
            }
        });
        rv_media_fragment.setAdapter(commonAdapter);
        rv_media_fragment.setItemAnimator(null);
    }

    private void setView(Bitmap thumbnail, final int mediaType, final ViewHolder holder, final int durationInSeconds) {
        final BitmapDrawable bitmapDrawable = new BitmapDrawable(activity.getResources(), thumbnail);
        ContextUtil.getHandler().post(() -> {
            if (mediaType != 0 && mediaType != 5) {
                holder.setImageResource(R.id.iv_video_play, R.drawable.ic_video_play);
                holder.setText(R.id.tv_item_label, Util.formatSecond2Hour(durationInSeconds));
            } else {
                holder.setImageVisible(R.id.iv_video_play, false);
            }
            holder.setImageResource(R.id.iv_img, bitmapDrawable);
        });
    }

    @Override
    public void update(String filename) {
        for (int i = 0; i < list.size(); i++) {
            MediaBean mediaBean = list.get(i);
            if (mediaBean.getFileName().equals(filename)) {
                list.remove(i);
                commonAdapter.notifyItemRemoved(i);
                if (i != mediaList.size()) {
                    commonAdapter.notifyItemRangeChanged(i, mediaList.size() - i);
                }
            }
        }
    }

    @Override
    public void selectMedia(boolean select) {
        isSelect = select;
        if (!isSelect) {
            if (commonAdapter != null) {
                //mposition=-1;
                mediaFilelist.clear();
                commonAdapter.notifyItemRangeChanged(0, mediaList.size());
            }
        }
    }

    @Override
    public void deleteMedia(final int currentItem) {
        if (mediaManager != null) {
            mediaManager.deleteMediaFiles(mediaFilelist, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ContextUtil.getHandler().post(() -> {
                        if (instence != null) {
                            instence.setDeleteMediaUpdate(mediaFilelist, currentItem);
                        }
                        //mposition=-1;
                        ArrayList<MediaBean> newMediaList = new ArrayList<>();
                        boolean temp = false;
                        for (int i = 0; i < list.size(); i++) {
                            temp = false;
                            for (int j = 0; j < mediaFilelist.size(); j++) {
                                if (mediaFilelist.get(j) == list.get(i).getMedia()) {
                                    temp = true;
                                    break;
                                }
                            }
                            if(!temp){
                                newMediaList.add(list.get(i));
                            }
                        }
                        list.clear();
                        list.addAll(newMediaList);

                        commonAdapter.notifyDataSetChanged();
                        mediaFilelist.clear();
                        ToastUtil.show("删除成功！");
                    });
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show(""+error.description());
                }
            });
        }
    }

    @Override
    public void downloadMedia(int currentItem) {
        downloadLL.setVisibility(View.VISIBLE);
        downloadTV.setText(" 总文件个数" + mediaFilelist.size());
        MediaDownloadController.getInstance().setCheckedMediaFileList(mediaFilelist);
        MediaDownloadController.getInstance().setOnProgressListener(new MediaDownloadController.OnProgressUpdateListener() {
            @Override
            public void onProgressUpdate(long total, long current, String fileName, int currentPosition) {
                ContextUtil.getHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        downloadTV.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (currentPosition + 1) + "个 " + fileName);
                        downloadPB.setProgress((int) (current * 100 / total));
                        downPercent.setText((int) (current * 100 / total) + "%");
                    }
                });
            }

            @Override
            public void onSuccess(String fileName) {
                ContextUtil.getHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        /*if (MediaDownloadController.getInstance().getCurrentPosition() < MediaDownloadController.getInstance().getTotalCount()) {
                            downloadTV.setText("总个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 ");
                        }*/
                        if (MediaDownloadController.getInstance().getTotalCount() == (MediaDownloadController.getInstance().getCurrentPosition() + 1)) {
                            downloadLL.setVisibility(View.INVISIBLE);
                            ToastUtil.show("全部下载完成！");
                            /*MediaFragment mediaFragment = (MediaFragment) activity.getAircraftFragmentManager().getFragment(AircraftFragmentManager.MEDIA_FRAGMENT);
                            mediaFragment.uploadVideo(mediaFilelist);*/
                        }
                        downloadPB.setProgress(100);
                        downPercent.setText("100%");

                    }
                });
            }
        });
        MediaDownloadController.getInstance().startDownload();
        ToastUtil.show("开始下载-文件个数" + mediaFilelist.size());
    }

    @Override
    public void deleteMediaUpdate(ArrayList<MediaFile> listMedia) {
        for (int i = 0; i < listMedia.size(); i++) {
            String fileName = listMedia.get(i).getFileName();
            update(fileName);
        }
    }
}
