package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*

package dji.sampleV5.aircraft.page.fly.setting.pager.detail;


import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080I_50FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080I_60FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080P_24FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080P_25FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080P_30FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080P_50FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_1080P_60FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_720P_24FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_720P_25FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_720P_30FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_720P_50FPS;
import static dji.common.remotecontroller.SecondaryVideoFormat.RESOLUTION_720P_60FPS;

import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.databinding.MenuHdPagerBinding;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;

import java.util.ArrayList;
import java.util.Collections;

import dji.common.error.DJIError;
import dji.common.remotecontroller.SecondaryVideoFormat;
import dji.common.util.CommonCallbacks;
import dji.internal.util.Util;
import dji.sdk.remotecontroller.RemoteController;


public class HDVideoOutputFormat implements View.OnClickListener {
    //private final AirLink airLink;
    private RemoteController remoteController;
    private final RelativeLayout rlHdVideoFormatPop;
    private final TextView tvHdVideoFormat;
    private final ListView mListView;
    private final AircraftActivity mActivity;
    private String[] array;
    private final ArrayList<String> list;
    private EasyAdapter easyAdapter;
    private int currentvalue;
    private final SecondaryVideoFormat[] formatarr;


    public HDVideoOutputFormat(MenuHdPagerBinding binding, AircraftActivity mActivity) {
        this.mActivity=mActivity;
        formatarr = new SecondaryVideoFormat[]{RESOLUTION_720P_60FPS, RESOLUTION_720P_50FPS, RESOLUTION_1080I_60FPS, RESOLUTION_1080I_50FPS, RESOLUTION_1080P_60FPS,
                RESOLUTION_1080P_50FPS, RESOLUTION_1080P_30FPS, RESOLUTION_1080P_24FPS, RESOLUTION_1080P_25FPS, RESOLUTION_720P_30FPS, RESOLUTION_720P_25FPS, RESOLUTION_720P_24FPS};
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        rlHdVideoFormatPop = binding.menuHdVideoFormat.rlHdVideoFormatPop;
        tvHdVideoFormat = binding.menuHdVideoFormat.tvHdVideoFormat;
        //airLink = DJIHelper.getInstance().getAirLink();
        remoteController = DJIHelper.getInstance().getRemoteController();
        rlHdVideoFormatPop.setOnClickListener(this);
        list = new ArrayList<>();
        array = Util.getResouce().getStringArray(R.array.lb_video_format);
    }

    public void initData() {
        list.clear();
        Collections.addAll(list, array);
        if (remoteController == null) {
            return;
        }
        remoteController.getSecondaryVideoOutputFormatForHDMI(new CommonCallbacks.CompletionCallbackWith<SecondaryVideoFormat>() {
            @Override
            public void onSuccess(SecondaryVideoFormat secondaryVideoFormat) {
                if (secondaryVideoFormat != null) {
                    currentvalue = secondaryVideoFormat.value();
                    final String s = array[currentvalue];
                    ContextUtil.getHandler().post(() -> {
                        if (easyAdapter==null){
                            setlistViewData(currentvalue);
                        }else {
                            easyAdapter.notifyDataSetChanged();
                        }
                        tvHdVideoFormat.setText(s);
                    });

                }
            }

            @Override
            public void onFailure(DJIError djiError) {

            }
        });

        */
/*if (airLink.getLightbridgeLink() != null) {
            DJIHelper.getInstance().getRemoteController().getSecondaryVideoOutputFormatForPort(HDMI, new CommonCallbacks.CompletionCallbackWith<SecondaryVideoFormat>() {
                @Override
                public void onSuccess(SecondaryVideoFormat now) {
                    if (now != null) {
                        currentvalue = now.value();
                        final String s = array[currentvalue];
                        ContextUtil.getHandler().post(new Runnable() {
                            @Override
                            public void run() {
                                if (easyAdapter==null){
                                    setlistViewData(currentvalue);
                                }else {
                                    easyAdapter.notifyDataSetChanged();
                                }
                                tvHdVideoFormat.setText(s);
                            }
                        });

                    }
                }

                @Override
                public void onFailure(DJIError djiError) {

                }
            });

        }*//*

    }

    private void setlistViewData(int value) {
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), list) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {

                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position == currentvalue) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        } else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };
        mListView.setAdapter(easyAdapter);
        mListView.setSelection(value);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> setSecondaryVideoOutputFormat(formatarr[position],position));
    }

    private void setSecondaryVideoOutputFormat(final SecondaryVideoFormat VideoFormat, final int position) {
        if (remoteController != null) {
            remoteController.setSecondaryVideoOutputFormatForHDMI(VideoFormat, djiError -> {
                currentvalue= position;
                ContextUtil.getHandler().post(() -> easyAdapter.notifyDataSetChanged());
            });
            */
/*airLink.getLightbridgeLink().setSecondaryVideoOutputFormat(VideoFormat, HDMI, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onResult(DJIError djiError) {
                    currentvalue= position;
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            easyAdapter.notifyDataSetChanged();
                        }
                    });
                }
            });*//*

        }

    }

    public void remove() {

    }

    @Override
    public void onClick(View v) {
        mActivity.showPopup1(tvHdVideoFormat, mListView, list.size());
    }

    public void isConnect(Boolean connect) {
        if (connect){
            rlHdVideoFormatPop.setVisibility(View.VISIBLE);
        }else {
            rlHdVideoFormatPop.setVisibility(View.GONE);
        }
    }
}
*/
