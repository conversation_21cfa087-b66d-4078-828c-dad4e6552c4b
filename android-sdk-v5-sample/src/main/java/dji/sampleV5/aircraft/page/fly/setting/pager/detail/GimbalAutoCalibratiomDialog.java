package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import dji.sampleV5.aircraft.R;

import java.util.List;



public class GimbalAutoCalibratiomDialog {

    private TextView tv_calibration_hint;
    private AlertDialog mAlertDialog;
    private  Gimbal gimbal;

    //校准对话框
    public void calibrationDialog(final AircraftActivity aircraftActivity, int gimbalIndex) {
        if (gimbal==null){
            List<Gimbal> gimbalList = DJIHelper.getInstance().getGimbals();
            if (gimbalList != null) {
                this.gimbal = gimbalList.get(gimbalIndex);
            }
        }
        KeyManager.getInstance().performAction(GimbalKey.create(GimbalKey.START_CALIBRATION, gimbalIndex), new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> setCalibrationProgressDialog(aircraftActivity));

            }

            @Override
            public void onException(Throwable e) {

            }
        });

    }

    public void setCalibrationProgressDialog(AircraftActivity aircraftActivity) {
        AlertDialog.Builder builder1 = new AlertDialog.Builder(aircraftActivity);
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.gimbal_calibration_dialog_hint, null);
        Button bt_calibration_ok = view.findViewById(R.id.bt_Calibration_OK);
        Button bt_calibration_cancel = view.findViewById(R.id.bt_Calibration_cancel);
        ImageView iv_calibration_exit = view.findViewById(R.id.iv_calibration_exit);
        tv_calibration_hint = view.findViewById(R.id.tv_calibration_hint);
        bt_calibration_ok.setVisibility(View.GONE);
        bt_calibration_cancel.setVisibility(View.GONE);
        if (gimbal!=null){
            gimbal.setStateCallback(gimbalState -> ContextUtil.getHandler().post(() -> {
                boolean calibrationSuccessful = gimbalState.isCalibrationSuccessful();
                int calibrationProgress = gimbalState.getCalibrationProgress();
                Log.e("onUpdate", calibrationSuccessful + "==" + calibrationProgress);
                String format = String.format(ContextUtil.getString(R.string.gimbal_calibrate_tips1), calibrationProgress);
                tv_calibration_hint.setText(format);
                if (calibrationProgress == 100) {
                    if (calibrationSuccessful) {
                        tv_calibration_hint.setText(ContextUtil.getString(R.string.gimbal_calibration_ok));
                        ContextUtil.getHandler().postDelayed(() -> mAlertDialog.dismiss(), 1000);
                    } else {
                        tv_calibration_hint.setText(ContextUtil.getString(R.string.gimbal_calibrate_failed));
                        ContextUtil.getHandler().postDelayed(() -> mAlertDialog.dismiss(), 1000);
                    }
                    gimbal.setStateCallback(null);
                }

            }));
        }

        builder1.setView(view);
        //退出
        iv_calibration_exit.setOnClickListener(v -> mAlertDialog.dismiss());

        mAlertDialog = builder1.create();
        mAlertDialog.setCanceledOnTouchOutside(false);
        ImmerseUtil.showDialog(mAlertDialog);
    }
}
*/
