package dji.sampleV5.aircraft.page.fly.controller;

import android.os.Environment;
import android.util.Log;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaFileDownloadListener;

public class MediaDownloadController {
    private static MediaDownloadController INSTANCE = new MediaDownloadController();
    private ArrayList<MediaFile> checkedMediaFileList = new ArrayList<>();
    private ArrayList<String> newFileNameList = new ArrayList<>();
    private OnProgressUpdateListener onProgressUpdateListener;
    private int currentPosition;

    private MediaDownloadController() {
    }

    public static MediaDownloadController getInstance() {
        return INSTANCE;
    }

    public int getTotalCount() {
        return checkedMediaFileList.size();
    }

    public int getCurrentPosition() {
        return currentPosition;
    }

    public void setCheckedMediaFileList(ArrayList<MediaFile> checkedMediaFileList, ArrayList<String> newFileNameList) {
        this.checkedMediaFileList.clear();
        this.newFileNameList.clear();
        currentPosition = 0;
        this.checkedMediaFileList.addAll(checkedMediaFileList);
        this.newFileNameList.addAll(newFileNameList);
    }

    public void setOnProgressListener(OnProgressUpdateListener onProgressUpdateListener) {
        this.onProgressUpdateListener = onProgressUpdateListener;
    }

    private static String getPath() {
        String path = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Skysys/download";
        File file = new File(path);
        if (file.mkdirs()) {
            return path;
        }
        return path;
    }

    FileOutputStream outputStream = null;

    public void startDownload() {
        if (currentPosition < checkedMediaFileList.size()) {
            MediaFile currentMedia = checkedMediaFileList.get(currentPosition);
            //final String fileName = newFileNameList.get(currentPosition);
            final String fileName = checkedMediaFileList.get(currentPosition).getFileName();
            String path = getPath();
            String downloadName = fileName.substring(0, fileName.lastIndexOf("."));
            File file = new File(path + "/" + fileName);
            if (file.exists()) {
                ContextUtil.getHandler().postDelayed(() -> {
                    onProgressUpdateListener.onSuccess(fileName);
                }, 1000);
                ContextUtil.getHandler().postDelayed(() -> {
                    nextDownLoad();
                }, 2000);
            } else {
                try {
                    outputStream = new FileOutputStream(file);
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
                if (outputStream == null) {
                    Log.e("TAG", "startDownload: outputStream null");
                    return;
                }
                BufferedOutputStream bos = new BufferedOutputStream(outputStream);
                long beginTime = System.currentTimeMillis();

                currentMedia.pullOriginalMediaFileFromCamera(0, new MediaFileDownloadListener() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onProgress(long total, long current) {
                        onProgressUpdateListener.onProgressUpdate(total, current, fileName, currentPosition);
                    }

                    @Override
                    public void onRealtimeDataUpdate(byte[] data, long position) {
                        try {
                            bos.write(data, 0, data.length);
                            bos.flush();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onFinish() {
                        try {
                            outputStream.close();
                            bos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                        if (currentMedia.getFileType().value() == 3) {
                            //boolean isMavicPro = PreferenceUtil.getVideoIsMavicPro(currentMedia.getFileName());
                            //videoTable.save(currentMedia.getFileName(), currentMedia.getTimeCreated(), (long) (currentMedia.getDurationInSeconds() * 1000), isMavicPro);
                        }
                        //FileUtils.updatePhotoMedia(new File(filePath), ContextUtil.getCurrentActivity());
                        onProgressUpdateListener.onSuccess(fileName);
                        nextDownLoad();
                    }

                    @Override
                    public void onFailure(IDJIError error) {
                        nextDownLoad();
                        ContextUtil.getHandler().post(() -> ToastUtil.show("error = " + error.description()));
                    }
                });
            }
        } else {
            onProgressUpdateListener.onFinish();
        }
    }

    private void nextDownLoad() {
        ContextUtil.getHandler().postDelayed(new Runnable() {
            @Override
            public void run() {
                currentPosition++;
                startDownload();
            }
        }, 1000);
    }

    public interface OnProgressUpdateListener {
        void onProgressUpdate(long total, long current, String fileName, int currentPosition);

        void onSuccess(String fileName);

        void onFinish();
    }
}
