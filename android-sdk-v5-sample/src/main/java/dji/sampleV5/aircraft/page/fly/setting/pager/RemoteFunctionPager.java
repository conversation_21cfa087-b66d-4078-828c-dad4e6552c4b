package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuRemoteSetPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;


public class RemoteFunctionPager extends BasePager {

    public RemoteFunctionPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {
        tvTitle.setText(ContextUtil.getString(R.string.remote_set_pager));
        isLoading = true;
        MenuRemoteSetPagerBinding RemoteSetPagerBinding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_remote_set_pager, null, false);
        RemoteSetPagerBinding.setActivityMenuPresenter(activityMenuPresenter);

        flContainer.addView(RemoteSetPagerBinding.getRoot());
    }

    @Override
    public void removeListener() {
    }
}
