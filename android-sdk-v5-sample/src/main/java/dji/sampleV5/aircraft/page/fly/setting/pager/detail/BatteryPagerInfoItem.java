package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuBatteryPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.view.adapter.CommonAdapter;

import java.util.ArrayList;
import java.util.List;



public class BatteryPagerInfoItem {
    private final AircraftSettingFragment activity;
    private MenuBatteryPagerBinding binding;
    private Integer numberOfDischarge;
  /*  private KeyListener<Integer> flyTimeListener;
    private KeyListener<Integer[]> cellVoltagesListener;
    private KeyListener<Integer> numberOfDischargeListener;
    private KeyListener<Float> temperatureKeyListener;
    private KeyListener<Integer> chargeListener;

    private KeyListener<Integer> voltagekeyListener;*/

    private int numberOfConnectedBatteries;
    private final ActivityMenuPresenter activityMenuPresenter;

    //private Battery battery;
    private final RecyclerView rvBatteryShow;
    private List<BatteryInfo> listInfo =new ArrayList<>();
    private List<BatteryInfo> listInfo1 =new ArrayList<>();
    private CommonAdapter<BatteryInfo> commonAdapter;
    private boolean ISAggregationState=true;
    private CommonAdapter<BatteryInfo> commonAdapter1;
    //private KeyListener<Integer> full_charge_capacity;

    public BatteryPagerInfoItem(MenuBatteryPagerBinding binding, AircraftSettingFragment activity) {
        this.binding = binding;
        this.activity = activity;
        rvBatteryShow = binding.menuBatteryPagerItem1.rvBatteryShow;

        activityMenuPresenter = binding.getActivityMenuPresenter();

    }

    public void getBatteryInfo1() {
       /* battery = DJIHelper.getInstance().getBattery();
        if (battery == null) {
            return;
        }
        //电池组状态


        Battery.setAggregationStateCallback(aggregationState -> {
            if (aggregationState == null) {
                return;
            }
            numberOfConnectedBatteries = aggregationState.getNumberOfConnectedBatteries();
            final BatteryOverview[] batteryOverviews = aggregationState.getBatteryOverviews();
            if (ISAggregationState){
                ISAggregationState=false;
                removeLisener1();
                for (BatteryOverview batteryOverview : batteryOverviews) {
                    float Percent1 = batteryOverview.getChargeRemainingInPercent() / 100f;
                    String s = batteryOverview.getChargeRemainingInPercent() + "%";
                    BatteryInfo batteryInfo = new BatteryInfo();
                    batteryInfo.setCellVoltage(s);
                    batteryInfo.setPercent(Percent1);
                    listInfo1.add(batteryInfo);
                }
                //创建RecyclerView
                initRecyclerView1();
            }else {
                for (int i = 0; i <batteryOverviews.length ; i++) {
                    float Percent1 = batteryOverviews[i].getChargeRemainingInPercent() / 100f;
                    String s = batteryOverviews[i].getChargeRemainingInPercent() + "%";
                    BatteryInfo batteryInfo = listInfo1.get(i);
                    batteryInfo.setCellVoltage(s);
                    batteryInfo.setPercent(Percent1);
                    final int finalI = i;
                    ContextUtil.getHandler().post(() -> commonAdapter1.notifyItemChanged(finalI));
                }
            }

            //获得电池组的详细
            getBatteryInfoAggregationState(aggregationState);

        });

        if (numberOfConnectedBatteries <= 1) {
            activityMenuPresenter.setIsClickableOfBattery(false);
            //获取电池的状态
            getBatteryinfo();
            //获得六个小电池组的电压数据
            getCellVoltages();
        }

        //获得飞行状态,飞行时间
        getFlyTime();*/

    }

   /* private void getBatteryInfoAggregationState(AggregationState aggregationState) {
        //电池容量
        final int fullChargeCapacity = aggregationState.getFullChargeCapacity();
        //电压
        final int voltage = aggregationState.getVoltage();
        //当前电量
        final int chargeRemaining = aggregationState.getChargeRemaining();
        //温度
        final int highestTemperature = aggregationState.getHighestTemperature();

        ContextUtil.getHandler().post(() -> {

            activityMenuPresenter.setBatteryPressure(Util.getDot((voltage / 1000f), 2) + ContextUtil.getString(R.string.v));
            activityMenuPresenter.setCurrentEnergy(chargeRemaining + ContextUtil.getString(R.string.mah));
            activityMenuPresenter.setFullEnergy(fullChargeCapacity + ContextUtil.getString(R.string.mah));
            activityMenuPresenter.setBatteryTemperature(Util.getDot(highestTemperature, 1) + ContextUtil.getString(R.string.temperature_unit));
        });
    }

    //获得飞行状态,飞行时间
    private void getFlyTime() {
        flyTimeListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable Integer now) {
                if (now != null) {
                    final String hhmmss = Util.formatRecordTime(now/10);

                    ContextUtil.getHandler().post(() -> activityMenuPresenter.setFlyTime(hhmmss));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.create(FlightControllerKey.FLY_TIME_IN_SECONDS), flyTimeListener);
    }

    private void getCellVoltages() {
        KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.CELL_VOLTAGES), new YNListener1<Integer[]>() {
            @Override
            public void onSuccess(Integer[] value) {
                for (Integer aValue : value) {

                    float percent = aValue / 4250f;
                    String CellVoltage = Util.getDot((aValue / 1000f), 2) + ContextUtil.getString(R.string.v);
                    BatteryInfo batteryInfo = new BatteryInfo();
                    batteryInfo.setCellVoltage(CellVoltage);
                    batteryInfo.setPercent(percent);
                    listInfo.add(batteryInfo);
                }
                //创建RecyclerView
                initRecyclerView(listInfo);
                update();
            }
            @Override
            public void onException(Throwable e) {
            }
        });

    }*/

    private void update() {
/*
        cellVoltagesListener = new KeyListener<Integer[]>() {
            @Override
            protected void onValueChanged(@Nullable Integer[] old, @Nullable final Integer[] value) {
                for (int i = 0; i <value.length ; i++) {

                    float percent = value[i] / 4250f;
                    String CellVoltage = Util.getDot((value[i] / 1000f), 2)+ContextUtil.getString(R.string.v);
                    BatteryInfo batteryInfo = listInfo.get(i);

                    batteryInfo.setPercent(percent);
                    batteryInfo.setCellVoltage(CellVoltage);
                    final int finalI = i;
                    ContextUtil.getHandler().post(() -> commonAdapter.notifyItemChanged(finalI));
                }
            }
        };

        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.CELL_VOLTAGES), cellVoltagesListener);*/
    }

    private void initRecyclerView(List<BatteryInfo> listInfo) {

        LinearLayoutManager mLayoutManager = new LinearLayoutManager(ContextUtil.getApplicationContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvBatteryShow.setLayoutManager(mLayoutManager);
        /*commonAdapter = new CommonAdapter<BatteryInfo>(ContextUtil.getApplicationContext(), R.layout.item_battery_show, listInfo) {
            @Override
            public void convert(final ViewHolder holder, final BatteryInfo batteryInfo, int position) {
                ContextUtil.getHandler().post(() -> {
                    holder.setPercent(R.id.battery_energy, batteryInfo.getPercent());
                    holder.setText(R.id.battery_text, batteryInfo.getCellVoltage());
                });
            }
        };*/
        ContextUtil.getHandler().post(() -> {
            rvBatteryShow.setAdapter(commonAdapter);
            rvBatteryShow.setItemAnimator(null);
        });

    }

    private void initRecyclerView1() {

        LinearLayoutManager mLayoutManager = new LinearLayoutManager(ContextUtil.getApplicationContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvBatteryShow.setLayoutManager(mLayoutManager);
        /*commonAdapter1 = new CommonAdapter<BatteryInfo>(ContextUtil.getApplicationContext(), R.layout.item_battery_show, listInfo1) {
            @Override
            public void convert(final ViewHolder holder, final BatteryInfo batteryInfo, int position) {
                ContextUtil.getHandler().post(() -> {
                    holder.setPercent(R.id.battery_energy, batteryInfo.getPercent());
                    holder.setText(R.id.battery_text, batteryInfo.getCellVoltage());
                });
            }
        };*/
        ContextUtil.getHandler().post(() -> {
            rvBatteryShow.setAdapter(commonAdapter1);
            rvBatteryShow.setItemAnimator(null);
        });
        commonAdapter1.setOnItemClickListener((view, position) -> {

//            battery.setComponentIndex(position);
//            activity.getViewPager().setCurrentItem(12, false);
        });

    }

    //获取电池的状态
    private void getBatteryinfo() {
        //电池循环次数
        getNumberDischarge();
        //电池容量
        getFullCharge();
        //温度
        getTemperature();
        //剩余电量
        getCharge();
        //电压
        getVoltage();
    }

    private void getCharge() {
       /* chargeListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> binding.getActivityMenuPresenter().setCurrentEnergy(now + ContextUtil.getString(R.string.mah)));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.CHARGE_REMAINING), chargeListener);*/
    }

    private void getTemperature() {
       /* temperatureKeyListener = new KeyListener<Float>() {
            @Override
            protected void onValueChanged(@Nullable Float old, @Nullable final Float now) {
                ContextUtil.getHandler().post(() -> activityMenuPresenter.setBatteryTemperature(Util.getDot(now, 1) + ContextUtil.getString(R.string.temperature_unit)));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.TEMPERATURE), temperatureKeyListener);*/
    }

    private void getNumberDischarge() {
        /*numberOfDischargeListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable Integer now) {
                if (now != null) {
                    numberOfDischarge = now;
                    ContextUtil.getHandler().post(() -> activityMenuPresenter.setCyclikTime(numberOfDischarge + ""));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.NUMBER_OF_DISCHARGES), numberOfDischargeListener);*/
    }

    private void getFullCharge() {
        /*full_charge_capacity = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now != null) {
                    ContextUtil.getHandler().post(() -> activityMenuPresenter.setFullEnergy(now + ContextUtil.getString(R.string.mah)));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.FULL_CHARGE_CAPACITY),full_charge_capacity );*/
    }

    //电压
    private void getVoltage() {
       /* voltagekeyListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> activityMenuPresenter.setBatteryPressure(Util.getDot((now / 1000f), 2) + ContextUtil.getString(R.string.v)));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.VOLTAGE), voltagekeyListener);*/
    }


    public void removeLisener() {

      /*  Battery.setAggregationStateCallback(null);
        KeyManager.getInstance().removeListener(flyTimeListener);
        KeyManager.getInstance().removeListener(cellVoltagesListener);
        KeyManager.getInstance().removeListener(numberOfDischargeListener);
        KeyManager.getInstance().removeListener(temperatureKeyListener);
        KeyManager.getInstance().removeListener(chargeListener);
        KeyManager.getInstance().removeListener(voltagekeyListener);
        KeyManager.getInstance().removeListener(full_charge_capacity);*/
    }

    public void removeLisener1() {
       /* KeyManager.getInstance().removeListener(flyTimeListener);
        KeyManager.getInstance().removeListener(cellVoltagesListener);
        KeyManager.getInstance().removeListener(numberOfDischargeListener);
        KeyManager.getInstance().removeListener(temperatureKeyListener);
        KeyManager.getInstance().removeListener(chargeListener);
        KeyManager.getInstance().removeListener(voltagekeyListener);
        KeyManager.getInstance().removeListener(full_charge_capacity);*/

    }


}
