package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.widget.SeekBar;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.data.preference.SpUtil;
import com.skysys.fly.databinding.MenuHdPagerBinding;

import dji.common.airlink.LightbridgeDataRate;
import dji.common.error.DJIError;
import dji.common.util.CommonCallbacks;
import dji.sdk.airlink.AirLink;

*/
/**
 * 说明:手动选择信号道
 *//*


public class HDSignalChannelManual {

    private  SeekBar mseekBarHdManual;
    private MenuHdPagerBinding mbinding;
    private AirLink airLink;

    public HDSignalChannelManual(MenuHdPagerBinding binding) {
        mseekBarHdManual = binding.menuHdManual.SeekBarHdManual;
        mbinding=binding;
    }

    public void setHDSignalChannelManual() {
        airLink = DJIHelper.getInstance().getAirLink();
        if (airLink == null) {
            return;
        }
        //设置图传码率
        setLBAirLinkDataRate();
        //选择当前码率
        selectCurrentDataRAte();
        //设置默认码率
        if(!SpUtil.getIsSetDefaultHDSignalChannel()){
            setDataRAte(LightbridgeDataRate.BANDWIDTH_4_MBPS);
            SpUtil.setDefaultHDSignalChannel();
        }
    }

    //设置图传码率
    public void setLBAirLinkDataRate() {
        if (airLink != null && airLink.getLightbridgeLink() != null) {
            airLink.getLightbridgeLink().getDataRate(new CommonCallbacks.CompletionCallbackWith<LightbridgeDataRate>() {
                @Override
                public void onSuccess(LightbridgeDataRate lightbridgeDataRate) {
                    int value = lightbridgeDataRate.value();
                    mseekBarHdManual.setProgress(value);
                    //显示当前的码率
                    setTextShow(value);

                }

                @Override
                public void onFailure(DJIError djiError) {
                }
            });
        }
    }
    //选择当前码率
    private void selectCurrentDataRAte() {
        mseekBarHdManual.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser){
                    switch (progress) {
                        case 0:
                            setDataRAte(LightbridgeDataRate.BANDWIDTH_4_MBPS);
                            break;
                        case 1:
                            setDataRAte(LightbridgeDataRate.BANDWIDTH_6_MBPS);
                            break;
                        case 2:
                            setDataRAte(LightbridgeDataRate.BANDWIDTH_8_MBPS);
                            break;
                        case 3:
                            setDataRAte(LightbridgeDataRate.BANDWIDTH_10_MBPS);
                            break;
                    }
                }

            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    //显示当前的码率
    private void setTextShow(final int value) {
        ContextUtil.getHandler().post(() -> {
            mseekBarHdManual.setProgress(value-1);
            switch (value) {
                case 1:
                    mbinding.getActivityMenuPresenter().setHDSeekbarTextView(ContextUtil.getString(R.string.mbps4)+"(3km)");
                    break;
                case 2:
                    mbinding.getActivityMenuPresenter().setHDSeekbarTextView(ContextUtil.getString(R.string.mbps6)+"(2km)");
                    break;
                case 3:
                    mbinding.getActivityMenuPresenter().setHDSeekbarTextView(ContextUtil.getString(R.string.mbps8)+"(1.5km)");
                    break;
                case 4:
                    mbinding.getActivityMenuPresenter().setHDSeekbarTextView(ContextUtil.getString(R.string.mbps10)+"(0.7km)");
                    break;
            }
        });
    }
//设置当前的码率
    public void setDataRAte(final LightbridgeDataRate bandwidth) {
        if (airLink.getLightbridgeLink() == null) {
            return;
        }
        airLink.getLightbridgeLink().setDataRate(bandwidth, djiError -> {
            if (djiError==null){
                int value1 = bandwidth.value();
                setTextShow(value1);
            }
        });
    }


}
*/
