package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import static dji.common.remotecontroller.SecondaryVideoDisplayMode.SOURCE_1_MAIN;
import static dji.common.remotecontroller.SecondaryVideoDisplayMode.SOURCE_1_ONLY;
import static dji.common.remotecontroller.SecondaryVideoDisplayMode.SOURCE_2_MAIN;
import static dji.common.remotecontroller.SecondaryVideoDisplayMode.SOURCE_2_ONLY;

import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.listener.YNListener0;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;

import java.util.ArrayList;

import dji.common.remotecontroller.PIPPosition;
import dji.common.remotecontroller.SecondaryVideoDisplayMode;
import dji.sdk.remotecontroller.RemoteController;


public class HDVideoOutputMode implements View.OnClickListener {

    private static final int LISTDATA = 0;
    private static final int LISTDATA1 = 1;
    private final RelativeLayout rlHdVideoModePop;
    private final TextView tvHdVideoMode;
    private final AircraftActivity mActivity;
    private ListView mListView1;
    //private AirLink airLink;
    private RemoteController remoteController;
    private ListView mListView;
    private ArrayList<String> listdata;
    private ArrayList<SecondaryVideoDisplayMode> modeList;
    private int currentPosition;
    private final TextView tvHdPictureInPicture;
    private final RelativeLayout rlHdPictureInPicturePop;
    private ArrayList<String> listdata1;
    private int currentPosition1;
    private ArrayList<PIPPosition> pipPositions;
    private KeyListener<PIPPosition> listener1;
    private KeyListener<SecondaryVideoDisplayMode> listener2;

    public HDVideoOutputMode(com.skysys.fly.databinding.MenuHdPagerBinding binding, AircraftActivity mActivity) {
        this.mActivity = mActivity;
        //airLink = DJIHelper.getInstance().getAirLink();
        remoteController = DJIHelper.getInstance().getRemoteController();
        rlHdVideoModePop = binding.menuHdVideoMode.rlHdVideoModePop;
        tvHdVideoMode = binding.menuHdVideoMode.tvHdVideoMode;
        //画中画
        tvHdPictureInPicture = binding.menuHdPictureInPicture.tvHdPictureInPicture;
        rlHdPictureInPicturePop = binding.menuHdPictureInPicture.rlHdPictureInPicturePop;
        rlHdPictureInPicturePop.setOnClickListener(this);
        rlHdVideoModePop.setOnClickListener(this);
        listdata = new ArrayList<>();
        listdata1 = new ArrayList<>();
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        mListView1 = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        if (remoteController == null) {
            return;
        }
        //获得数据
        setModedata();
        setPictureData();
    }

    private void setPictureData() {
        listdata1.clear();
        listdata1.add(ContextUtil.getString(R.string.left_top));
        listdata1.add(ContextUtil.getString(R.string.right_top));
        listdata1.add(ContextUtil.getString(R.string.left_down));
        listdata1.add(ContextUtil.getString(R.string.right_down));
        pipPositions = new ArrayList<>();
        pipPositions.add(PIPPosition.TOP_LEFT);
        pipPositions.add(PIPPosition.TOP_RIGHT);
        pipPositions.add(PIPPosition.BOTTOM_LEFT);
        pipPositions.add(PIPPosition.BOTTOM_RIGHT);
        listener1 = new KeyListener<PIPPosition>() {
            @Override
            protected void onValueChanged(@Nullable PIPPosition old, @Nullable PIPPosition now) {
                if (now != null) {
                    currentPosition1 = now.value();
                    ContextUtil.getHandler().post(() -> tvHdPictureInPicture.setText(listdata1.get(currentPosition1)));
                    setListViewe(listdata1, LISTDATA1, mListView1);
                }
            }
        };

        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.PIP_POSITION), listener1);

    }

    private void setModedata() {
        listdata.clear();
        listdata.add(ContextUtil.getString(R.string.video_source1));
        listdata.add(ContextUtil.getString(R.string.picture_in_picture));
        listdata.add("2only");
        listdata.add("1main");
        modeList = new ArrayList<>();
        modeList.add(SOURCE_1_ONLY);
        modeList.add(SOURCE_2_MAIN);
        modeList.add(SOURCE_2_ONLY);
        modeList.add(SOURCE_1_MAIN);
        listener2 = new KeyListener<SecondaryVideoDisplayMode>() {
            @Override
            protected void onValueChanged(@Nullable SecondaryVideoDisplayMode old, @Nullable final SecondaryVideoDisplayMode VideoDisplayMode) {
                if (VideoDisplayMode != null) {
                    ContextUtil.getHandler().post(() -> {
                        switch (VideoDisplayMode.value()) {
                            case 0:
                                currentPosition = 0;
                                tvHdVideoMode.setText(listdata.get(0));
                                break;
                            case 1:
                                tvHdVideoMode.setText(listdata.get(2));
                                currentPosition = 2;
                                break;
                            case 2:
                                tvHdVideoMode.setText(listdata.get(3));
                                currentPosition = 3;
                                break;
                            case 3:
                                tvHdVideoMode.setText(listdata.get(1));
                                currentPosition = 1;
                                break;
                        }
                    });

                    setListViewe(listdata, LISTDATA, mListView);
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_DISPLAY_MODE), listener2);

    }

    private void setListViewe(ArrayList<String> listdata, final int i, final ListView mListView) {

        final EasyAdapter easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listdata) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {

                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (i == LISTDATA) {
                            if (position == currentPosition) {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                            } else {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                            }
                        } else {
                            if (position == currentPosition1) {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                            } else {
                                tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                            }
                        }

                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };
        ContextUtil.getHandler().post(() -> mListView.setAdapter(easyAdapter));
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            if (i == LISTDATA) {
                currentPosition = position;
                SecondaryVideoDisplayMode Mode = modeList.get(position);
                setdata(Mode);
            } else {
                currentPosition1 = position;
                PIPPosition lightbridgePIPPosition = pipPositions.get(position);
                setPIPData(lightbridgePIPPosition);
            }
            easyAdapter.notifyDataSetChanged();
        });
    }

    private void setPIPData(PIPPosition lightbridgePIPPosition) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.PIP_POSITION), lightbridgePIPPosition, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }

    private void setdata(final SecondaryVideoDisplayMode mode) {
        if (remoteController != null) {
            remoteController.setSecondaryVideoDisplay(mode, djiError -> {

            });
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.rl_hd_video_mode_pop:
                mActivity.showPopup1(tvHdVideoMode, mListView, listdata.size());
                break;
            case R.id.rl_hd_picture_in_picture_pop:
                mActivity.showPopup1(tvHdPictureInPicture, mListView1, listdata1.size());
                break;
        }

    }

    public void remove() {
        KeyManager.getInstance().removeListener(listener1);
        KeyManager.getInstance().removeListener(listener2);
    }

    public void isConnect(Boolean connect) {
        if (connect) {
            rlHdVideoModePop.setVisibility(View.VISIBLE);
            rlHdPictureInPicturePop.setVisibility(View.VISIBLE);
        } else {
            rlHdVideoModePop.setVisibility(View.GONE);
            rlHdPictureInPicturePop.setVisibility(View.GONE);
        }
    }
}
*/
