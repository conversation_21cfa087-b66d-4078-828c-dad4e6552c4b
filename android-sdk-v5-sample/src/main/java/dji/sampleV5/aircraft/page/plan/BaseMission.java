package dji.sampleV5.aircraft.page.plan;

public class BaseMission {
    private String missionID;
    private String missionBatch;
    private String name;                                // 任务名称
    private String address;                             // 任务地址
    private int routeLength;                            // 航线总长度，单位：米
    private int waypointNum;                            // 航点个数
    private int preFlightTime;                          // 预计飞行时间，单位：秒
    private float flySpeed;                             // 飞行速度
    private float altitude;                             // 高度
    private MissionType missionType;                    // 任务类型
    private boolean exitMissionOnRCSignalLost;          // 无人机断开信号是否退出任务

    public BaseMission() {
    }

    public void setMissionID(String missionID) {
        this.missionID = missionID;
    }

    public String getMissionID() {
        return missionID;
    }

    public String getMissionBatch() {
        return missionBatch;
    }

    public void setMissionBatch(String missionBatch) {
        this.missionBatch = missionBatch;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public MissionType getMissionType() {
        return missionType;
    }

    public void setMissionType(MissionType missionType) {
        this.missionType = missionType;
    }

    public int getPreFlightTime() {
        return preFlightTime;
    }

    public void setPreFlightTime(int preFlightTime) {
        this.preFlightTime = preFlightTime;
    }

    public int getRouteLength() {
        return routeLength;
    }

    public void setRouteLength(int routeLength) {
        this.routeLength = routeLength;
    }

    public int getWaypointNum() {
        return waypointNum;
    }

    public void setWaypointNum(int waypointNum) {
        this.waypointNum = waypointNum;
    }

    public float getFlySpeed() {
        return flySpeed;
    }

    public void setFlySpeed(float flySpeed) {
        this.flySpeed = flySpeed;
    }

    public float getAltitude() {
        return altitude;
    }

    public void setAltitude(float altitude) {
        this.altitude = altitude;
    }

    public boolean isExitMissionOnRCSignalLost() {
        return exitMissionOnRCSignalLost;
    }

    public void setExitMissionOnRCSignalLost(boolean exitMissionOnRCSignalLost) {
        this.exitMissionOnRCSignalLost = exitMissionOnRCSignalLost;
    }

    @Override
    public String toString() {
        return "BaseMission{" +
                ", name='" + name + '\'' +
                ", missionType=" + missionType +
                ", preFlightTime=" + preFlightTime +
                ", routeLength=" + routeLength +
                ", waypointNum=" + waypointNum +
                ", flySpeed=" + flySpeed +
                ", altitude=" + altitude +
                ", exitMissionOnRCSignalLost=" + exitMissionOnRCSignalLost +
                '}';
    }

    public enum MissionType {
        POINT,
        POLYGON,
        CIRCLE,
        OBLIQUE
    }

    /**
     * 任务完成后的动作
     */
    public enum FinishedAction {
        GoHome,
        NoAction,
        AutoLand
    }

    /**
     * 云台转向模式
     */
    public enum TurnMode {
        Clockwise,
        CounterClockwise
    }


}
