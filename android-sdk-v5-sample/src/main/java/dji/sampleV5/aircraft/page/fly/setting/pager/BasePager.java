package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuBasePagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;


public abstract class BasePager {

    protected TextView tvTitle;
    protected FrameLayout flContainer;
    protected MenuBasePagerBinding binding;
    protected AircraftSettingFragment fragment;
    protected ActivityMenuPresenter activityMenuPresenter;

    public final View rootView;
    public boolean isLoading = false;

    public BasePager(AircraftSettingFragment fragment) {
        this.fragment = fragment;
        rootView = initView();
    }

    private View initView() {
        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_base_pager, null, false);
        activityMenuPresenter = new ActivityMenuPresenter(fragment);
        binding.setActivityMenuPresenter(activityMenuPresenter);

        flContainer = binding.flContainer;
        tvTitle = binding.menuTitleBar.tvTitleMenu;
        ImageView btn_menu = binding.menuTitleBar.ivMenuExit;
        btn_menu.setOnClickListener(v -> {
            AircraftSettingFragment aircraftMenuActivity = fragment;
            aircraftMenuActivity.finish();
        });

        return binding.getRoot();
    }

    public abstract void initData();

    public abstract void removeListener();

    public void onPageSelected() {}

    public void isConnect(boolean connect) {}
}
