package dji.sampleV5.aircraft.page.picture.media;

import android.app.Dialog;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;


import com.google.vr.sdk.widgets.pano.VrPanoramaEventListener;
import com.google.vr.sdk.widgets.pano.VrPanoramaView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.page.picture.PictureFragmentManager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.video.channel.VideoChannelType;
import dji.v5.common.video.decoder.DecoderOutputMode;
import dji.v5.common.video.decoder.DecoderState;
import dji.v5.common.video.decoder.VideoDecoder;
import dji.v5.common.video.interfaces.IVideoFrame;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaManager;
import dji.v5.manager.datacenter.media.VideoPlayStateListener;
import dji.v5.manager.datacenter.media.VideoPlayStatus;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;

import java.util.ArrayList;
import java.util.List;


public class MediaShowFragment extends Fragment implements View.OnClickListener, SeekBar.OnSeekBarChangeListener, SurfaceHolder.Callback {

    private int mediaPosition;
    private boolean isPlaying;
    private boolean isTrackingTouch;

    private View view;
    private Dialog dialog;
    private MediaObserver observer;
    private MediaManager mediaManager;
    private PictureActivity activity;
    private ArrayList<MediaBean> mediaBeanList;

    private SeekBar seekBarMedia;
    private VideoPlayStateListener videoPlaybackStateListener;
    private MediaFile currentMediaFile;
    private VideoDecoder videoDecoder = null;
    private SurfaceView surfaceView;
    //private ZPanoramaTextureView mPanoramaTextureView;
    private VrPanoramaView vrPanoramaView;
    private VrPanoramaView.Options paNormalOptions;


    public static MediaShowFragment newInstance(Bundle bundle) {
        MediaShowFragment fragment = new MediaShowFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        activity = (PictureActivity) getActivity();
        view = inflater.inflate(R.layout.fragment_media_show, container, false);

        mediaManager = MediaManager.getInstance();

        if (getArguments() != null) {
            mediaBeanList = getArguments().getParcelableArrayList(getString(R.string.all));
            mediaPosition = getArguments().getInt(getString(R.string.position));
        }

        MediaFragment mediaFragment = (MediaFragment) activity.getPictureFragmentManager().getFragment(PictureFragmentManager.MEDIA_FRAGMENT);
        observer = mediaFragment.getData();

        surfaceView = view.findViewById(R.id.surfaceView);
        surfaceView.getHolder().addCallback(this);
        view.findViewById(R.id.iv_back).setOnClickListener(this);
        view.findViewById(R.id.iv_delete).setOnClickListener(this);
        view.findViewById(R.id.iv_left).setOnClickListener(this);
        view.findViewById(R.id.iv_right).setOnClickListener(this);

        //((FPVWidget) view.findViewById(R.id.FPVWidget)).setSourceCameraNameVisibility(false);

        view.findViewById(R.id.iv_item_play).setOnClickListener(this);
        view.findViewById(R.id.iv_pause_resume).setOnClickListener(this);

        //mPanoramaTextureView = view.findViewById(R.id.panorama);
        vrPanoramaView = view.findViewById(R.id.vrPanoramaView);
        paNormalOptions = new VrPanoramaView.Options();
        paNormalOptions.inputType = VrPanoramaView.Options.TYPE_MONO;
//        paNormalView.setFullscreenButtonEnabled (false); //隐藏全屏模式按钮
        vrPanoramaView.setInfoButtonEnabled(false); //设置隐藏最左边信息的按钮
        vrPanoramaView.setStereoModeButtonEnabled(false); //设置隐藏立体模型的按钮
        vrPanoramaView.setEventListener(new VrPanoramaEventListener(){
            @Override
            public void onLoadError(String errorMessage) {
                super.onLoadError(errorMessage);
                Log.e("TAG", "全景图load失败:"+errorMessage);
            }

            @Override
            public void onLoadSuccess() {
                super.onLoadSuccess();
                Log.e("TAG", "全景图load成功");
            }
        });

        initImageView(mediaPosition);
        return view;
    }

    private void initImageView(final int position) {

        view.findViewById(R.id.progress_bar).setVisibility(View.VISIBLE);
        view.findViewById(R.id.iv_item_image).setVisibility(View.VISIBLE);
        view.findViewById(R.id.iv_item_play).setVisibility(View.GONE);

       /* view.findViewById(R.id.iv_left).setVisibility(View.VISIBLE);
        view.findViewById(R.id.iv_right).setVisibility(View.VISIBLE);

        if (mediaPosition <= 0) {
            view.findViewById(R.id.iv_left).setVisibility(View.INVISIBLE);
        } else if (mediaPosition >= mediaBeanList.size() - 1) {
            view.findViewById(R.id.iv_right).setVisibility(View.INVISIBLE);
        }*/

        currentMediaFile = mediaBeanList.get(position).getMedia();

        String fileName = mediaBeanList.get(position).getFileName();
        ((TextView) view.findViewById(R.id.tv_title)).setText(fileName);

        /*Bitmap preview = mediaFile.getPreview();
        if (preview == null) {
            Log.e("TAG", "initImageView: (preview == null");
            mediaFile.fetchPreview(djiError -> {
                if (djiError == null) {
                    setView(mediaFile);
                }
            });
        } else {
            setView(mediaFile);
        }*/
        setView(currentMediaFile);
    }

    private void setView(final MediaFile mediaFile) {
        ContextUtil.getHandler().post(() -> {
            //((ImageView) view.findViewById(R.id.iv_item_image)).setImageBitmap(mediaFile.getPreview());
            //final BitmapDrawable bitmapDrawable = new BitmapDrawable(activity.getResources(), mediaFile.getThumbnail());
            if(mediaFile.getFileType().value() == 4){
                mediaFile.pullPreviewFromCamera(new CommonCallbacks.CompletionCallbackWithParam<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
                            @Override
                            public void run() {
                                vrPanoramaView.setVisibility(View.VISIBLE);
                                vrPanoramaView.loadImageFromBitmap(bitmap,paNormalOptions);
                                
                                //vrPanoramaView.loadImageFromBitmap(BitmapFactory.decodeResource(getResources(), R.drawable.quanjing),paNormalOptions);
                            }
                        });
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {

                    }
                });

            } else if (mediaFile.getFileType().value() == 0 /*|| mediaFile.getMediaType().value() == MediaFile.MediaType.TIFF.value()*/) {
                mediaFile.pullPreviewFromCamera(new CommonCallbacks.CompletionCallbackWithParam<Bitmap>() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        AndroidSchedulers.mainThread().scheduleDirect(new Runnable() {
                            @Override
                            public void run() {
                                ((ImageView) view.findViewById(R.id.iv_item_image)).setImageBitmap(bitmap);
                            }
                        });
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {

                    }
                });
                view.findViewById(R.id.iv_item_play).setVisibility(View.GONE);
                view.findViewById(R.id.ll_media_play).setVisibility(View.GONE);
            } else {
                ((ImageView) view.findViewById(R.id.iv_item_image)).setImageBitmap(mediaFile.getThumbNail());/*setImageDrawable(bitmapDrawable);*/
                initVideoView(mediaFile);
            }
        });
    }

    private void initVideoView(final MediaFile mediaFile) {

        isPlaying = false;

        view.findViewById(R.id.iv_item_play).setVisibility(View.VISIBLE);
        view.findViewById(R.id.ll_media_play).setVisibility(View.VISIBLE);
        ((ImageView) view.findViewById(R.id.iv_pause_resume)).setImageResource(R.drawable.ic_play_arrow_black_24dp);

        float durationInSeconds = mediaFile.getDuration()/1000;
        Log.e("TAG", "initVideoView: "+durationInSeconds);
        ((TextView) view.findViewById(R.id.tv_media_play_time)).setText(Util.formatSecond2Hour((long) durationInSeconds));

        addMediaVideoPlayState();

        seekBarMedia = view.findViewById(R.id.seek_bar_media);
        seekBarMedia.setMax((int) durationInSeconds);
        seekBarMedia.setOnSeekBarChangeListener(this);
        seekBarMedia.setEnabled(false);
    }

    private void addMediaVideoPlayState() {
        videoPlaybackStateListener = new VideoPlayStateListener() {
            @Override
            public void onUpdate(VideoPlayStatus videoPlayStatus) {
                if (videoPlayStatus != null) {
                    //final int cachedPosition = videoPlayStatus.getCachedPosition();
                    final double playingPosition = videoPlayStatus.getPlayingPosition();
                    if (currentMediaFile != null) {
                        long durationInSeconds = currentMediaFile.getDuration();

                        ContextUtil.getHandler().post(() -> {
                            if (!isTrackingTouch) {
                                seekBarMedia.setProgress((int) playingPosition);
                            }

                            seekBarMedia.setSecondaryProgress((int) (playingPosition / durationInSeconds));
                            if (playingPosition == durationInSeconds) {
                                isPlaying = false;
                                ((ImageView) view.findViewById(R.id.iv_pause_resume)).setImageResource(R.drawable.ic_play_arrow_black_24dp);
                                view.findViewById(R.id.iv_item_play).setVisibility(View.VISIBLE);
                            }
                        });
                    }
                }
            }
        };

        mediaManager.addVideoPlayStateListener(videoPlaybackStateListener);
    }

    private void startPlay() {

        view.findViewById(R.id.iv_item_image).setVisibility(View.INVISIBLE);

        mediaManager.playVideo(mediaBeanList.get(mediaPosition).getMedia(), new CommonCallbacks.CompletionCallbackWithParam<IVideoFrame>() {
            @Override
            public void onSuccess(IVideoFrame iVideoFrame) {
                if(iVideoFrame == null){
                    return;
                }
                videoDecoder.queueInFrame(iVideoFrame);
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        view.findViewById(R.id.progress_bar).setVisibility(View.GONE);
                        view.findViewById(R.id.iv_item_play).setVisibility(View.GONE);
                        ((ImageView) view.findViewById(R.id.iv_pause_resume)).setImageResource(R.drawable.ic_pause_black_24dp);
                        isPlaying = true;
                    }
                });

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("播放失败:" + error.description());
            }
        });
    }

    private void stopPlay() {
        if(isPlaying){
            mediaManager.stopVideo(new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    mediaManager.removeVideoPlayStateListener(videoPlaybackStateListener);
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    //ToastUtil.show(""+error.description());
                }
            });
        }
    }

    /*@Override
    protected void onConnected() {
        super.onConnected();
        if (!isAirCraftConnected) {
            activity.getPictureFragmentManager().removeFragment(PictureFragmentManager.MEDIA_FRAGMENT_SHOW);
        }
    }*/

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_delete:
                AlertDialog.Builder builder = new AlertDialog.Builder(activity);
                builder.setMessage(ContextUtil.getString(R.string.delete_ok));
                builder.setPositiveButton(ContextUtil.getString(R.string.dialog_ok), (dialog, which) -> {
                    List<MediaFile> deleteFiles = new ArrayList<>();
                    deleteFiles.add(mediaBeanList.get(mediaPosition).getMedia());
                    mediaManager.deleteMediaFiles(deleteFiles, new CommonCallbacks.CompletionCallback() {
                        @Override
                        public void onSuccess() {
                            ContextUtil.getHandler().post(() -> {
                                if (observer != null) {
                                    observer.setNotifyObserver(mediaBeanList.get(mediaPosition).getFileName());
                                }

                                if (mediaPosition >= mediaBeanList.size() - 1) {
                                    mediaPosition = mediaBeanList.size() - 1;
                                }

                                if (mediaPosition == -1) {
                                    MediaShowFragment.this.dialog.dismiss();
                                    activity.getPictureFragmentManager().removeFragment(PictureFragmentManager.MEDIA_FRAGMENT_SHOW);
                                    return;
                                }

                                if (isPlaying) {
                                    stopPlay();
                                }

                                initImageView(mediaPosition);
                                MediaShowFragment.this.dialog.dismiss();
                            });
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {
                            ToastUtil.show("The camera is busy:" + error.description());
                        }
                    });
                });

                builder.setNegativeButton(ContextUtil.getString(R.string.dialog_cancel), (dialog, which) -> MediaShowFragment.this.dialog.dismiss());
                dialog = builder.create();
                ImmerseUtil.showDialog(dialog);
                break;
            case R.id.iv_back:
                stopPlay();
                isPlaying = false;
                activity.getPictureFragmentManager().removeFragment(PictureFragmentManager.MEDIA_FRAGMENT_SHOW);
                break;
            case R.id.iv_item_play:
                startPlay();
                break;
            case R.id.iv_pause_resume:
                if (isPlaying) {
                    mediaManager.pauseVideo(new CommonCallbacks.CompletionCallback() {
                        @Override
                        public void onSuccess() {
                            isPlaying = false;
                            ((ImageView) view.findViewById(R.id.iv_pause_resume)).setImageResource(R.drawable.ic_play_arrow_black_24dp);
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {
                            ToastUtil.show("暂停失败:" + error.description());
                        }
                    });
                } else {
                    if (view.findViewById(R.id.iv_item_play).getVisibility() == View.VISIBLE) {
                        startPlay();
                    } else {
                        mediaManager.resumeVideo(new CommonCallbacks.CompletionCallback() {
                            @Override
                            public void onSuccess() {
                                isPlaying = true;
                                ((ImageView) view.findViewById(R.id.iv_pause_resume)).setImageResource(R.drawable.ic_pause_black_24dp);
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                startPlay();
                            }
                        });
                    }
                }
                break;
            case R.id.iv_left:
                if (mediaPosition > 0) {
                    mediaPosition--;
                }
                initImageView(mediaPosition);
                stopPlay();
                break;
            case R.id.iv_right:
                if (mediaPosition < mediaBeanList.size() - 1) {
                    mediaPosition++;
                }
                initImageView(mediaPosition);
                stopPlay();
                break;
        }
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        final String time = Util.formatSecond2Hour(progress);
        ContextUtil.getHandler().post(() -> ((TextView) view.findViewById(R.id.tv_media_play_time)).setText(time));
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        isTrackingTouch = true;
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        int progress = seekBar.getProgress();
        //mediaManager.seekVideo();
        /*mediaManager.moveToPosition(progress, djiError -> {
            if (djiError == null) {
                isTrackingTouch = false;
            }
        });*/
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        if (videoDecoder == null) {
            videoDecoder = createVideoDecoder();
        } else if (videoDecoder.getDecoderStatus() == DecoderState.PAUSED) {
            videoDecoder.onResume();
        }
        videoDecoder.setMediaFile(currentMediaFile);
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        if (videoDecoder == null) {
            videoDecoder = createVideoDecoder();

        } else if (videoDecoder.getDecoderStatus() == DecoderState.PAUSED) {
            videoDecoder.onResume();
        }
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        videoDecoder.onPause();
    }

    private VideoDecoder createVideoDecoder(){
        return new VideoDecoder(getActivity(), VideoChannelType.EXTENDED_STREAM_CHANNEL, DecoderOutputMode.SURFACE_MODE,surfaceView.getHolder(),surfaceView.getWidth(),surfaceView.getHeight());
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (videoDecoder != null) {
            videoDecoder.destroy();
            videoDecoder = null;
        }
        MediaManager.getInstance().removeAllVideoPlayStateListener();
        vrPanoramaView.shutdown();
    }

    @Override
    public void onResume() {
        super.onResume();
        vrPanoramaView.resumeRendering();;
    }

    @Override
    public void onPause() {
        super.onPause();
        vrPanoramaView.pauseRendering();
    }
}
