package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.GimbalKey;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.listener.YNListener1;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.util.Util;
import com.skysys.fly.util.phone.ImmerseUtil;

import dji.sdk.gimbal.Gimbal;


public class GimbalRollSetDialog {
    private AircraftActivity maircraftActivity;
    private TextView mShow_gimbal_roll;
    private AlertDialog mDialog;
    private int constant;
    private Gimbal gimbal;

    public GimbalRollSetDialog(AircraftActivity aircraftActivity) {
        maircraftActivity = aircraftActivity;
    }

    public void setGimbalRollSetDialog(final int gimbalIndex) {
        gimbal = DJIHelper.getInstance().getGimbal();
        setShowGimbal(gimbalIndex);
        AlertDialog.Builder builder = new AlertDialog.Builder(maircraftActivity);
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.gimbal_roll_set_dialog, null);
        ImageView mDialog_exit = view.findViewById(R.id.iv_gimbal_dialog_exit);
        ImageView mAdd_gimbal = view.findViewById(R.id.iv_add_gimbal);
        ImageView mSubtract_gimbal = view.findViewById(R.id.iv_subtract_gimbal);
        mShow_gimbal_roll = view.findViewById(R.id.tv_show_gimbal_roll);

        mDialog_exit.setOnClickListener(v -> mDialog.dismiss());
        mAdd_gimbal.setOnClickListener(v -> {
            if (constant < 200) {
                constant++;
                setGimbal();
            }
        });
        mSubtract_gimbal.setOnClickListener(v -> {
            if (constant > -200) {
                constant--;

                setGimbal();
            }
        });
        builder.setView(view);
        mDialog = builder.create();
        ImmerseUtil.showDialog(mDialog);
    }

    private void setShowGimbal(int gimbalIndex) {
        boolean flg = true;
        KeyManager.getInstance().getValue(GimbalKey.create(GimbalKey.ROLL_FINE_TUNE_IN_DEGREES, gimbalIndex), new YNListener1<Float>() {
            @Override
            public void onSuccess(final Float value) {
                ContextUtil.getHandler().post(() -> {
                    constant = (int) (value * 2);
                    mShow_gimbal_roll.setText(value / 10f + "");

                });
            }

            @Override
            public void onException(Throwable e) {

            }
        });
//        final float rollFineTuneInDegrees = GimbalHelper.getInstance().getGimbalState().getRollFineTuneInDegrees();
//        ContextUtil.getHandler().post(new Runnable() {
//            @Override
//            public void run() {
//
//                    Log.e("calibrationProgress111", rollFineTuneInDegrees + "");
//                    constant= (int) (rollFineTuneInDegrees*2);
//                    mShow_gimbal_roll.setText( rollFineTuneInDegrees/10f+ "");
//
//            }
//        });
    }

    private void setGimbal() {
        if (gimbal != null) {
            gimbal.fineTuneRollInDegrees(constant / 100f, djiError -> ContextUtil.getHandler().post(() -> mShow_gimbal_roll.setText(Util.getDot(constant / 100f * 5, 1))));
//        KeyManager.getInstance().performAction(GimbalKey.create(GimbalKey.ROLL_FINE_TUNE_IN_DEGREES), new YNListener0() {
//            @Override
//            public void onSuccess() {
//                mShow_gimbal_roll.setText(Util.getDot(constant / 100f*5,1));
//            }
//
//            @Override
//            public void onException(Throwable e) {
//                Log.e("ww", e.toString() + "calibrationProgress111");
//            }
//        },constant / 100f);

//        GimbalHelper.setRollFineTuneInDegrees(constant / 100f, new YNListener0() {
//            @Override
//            public void onSuccess() {
//                ContextUtil.getHandler().post(new Runnable() {
//                    @Override
//                    public void run() {
//                            mShow_gimbal_roll.setText(Util.getDot(constant / 100f*5,1));
//                    }
//                });
//            }
//
//            @Override
//            public void onException(Throwable e) {
//
//            }
//        });
        }
    }
}
*/
