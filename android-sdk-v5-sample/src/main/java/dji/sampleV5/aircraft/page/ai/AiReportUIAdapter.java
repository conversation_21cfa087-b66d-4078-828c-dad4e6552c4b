package dji.sampleV5.aircraft.page.ai;

import android.app.Activity;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.net.PagingResult;
import dji.sampleV5.aircraft.net.ResultNotice;
import dji.sampleV5.aircraft.page.ai.vo.ReportInfoVO;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.utils.common.LogUtils;

/**
 * 循环使用的 ui 对象
 */
class AiReportViewHolder extends RecyclerView.ViewHolder {
    private final String TAG = LogUtils.getTag(this);

    public AiReportViewHolder(@NonNull View itemView, View.OnClickListener listener) {
        super(itemView);
        itemView.setOnClickListener(listener);
    }
}

/**
 * 数据对象
 */
class ReportItemVO {
    private final String TAG = LogUtils.getTag(this);

    public String taskName;
    public String pdfUrl;

    /**
     * 当模型层数据变化时, 列表 item 会重新绑定模型. 调用此方法渲染
     *
     * @param holder ui 对象
     */
    void updateUI(AiReportViewHolder holder) {
        TextView taskName = holder.itemView.findViewById(R.id.taskName);
        TextView reportUrl = holder.itemView.findViewById(R.id.reportUrl);
        Log.i(TAG, String.format("updateUI %s %s", this.taskName, this.pdfUrl));

        taskName.setText(this.taskName);
        reportUrl.setText(this.pdfUrl);
    }

    protected static ReportItemVO createFrom(ReportInfoVO item) {
        ReportItemVO vo = new ReportItemVO();
        vo.taskName = item.taskName;
        if (item.subTaskList != null && item.subTaskList.size() > 0) {
            vo.pdfUrl = item.subTaskList.get(0).pdfUrl;
        } else {
            vo.pdfUrl = "";
        }
        return vo;
    }
}

/**
 * ai 报告列表数据提供接口, 调用远程接口
 */
public class AiReportUIAdapter extends RecyclerView.Adapter<AiReportViewHolder> implements ResultNotice<ReportInfoVO> {
    private final String TAG = LogUtils.getTag(this);

    /**
     * 一个 repost 被点击时调用
     */
    public interface OnItemListener {
        /**
         *
         */
        void onClick(View v, ReportItemVO item);
    }

    /**
     * 模型层
     */
    private List<ReportItemVO> items;
    private final Activity activity;

    /**
     * 通知外层某个报告被点击了
     */
    private final OnItemListener listener;

    /**
     * 拉取远程数据
     */
    private final PagingResult<QueryParam, ReportInfoVO> query;

    /**
     * @param activity 用来获得 context
     * @param query    查询远程接口
     * @param listener 监听列表点击事件
     */
    public AiReportUIAdapter(Activity activity, PagingResult<QueryParam, ReportInfoVO> query, OnItemListener listener) {
        this.listener = listener;
        this.items = new ArrayList<>();

        this.activity = activity;
        this.query = query;
    }

    void onItemClick(View v) {
        ReportItemVO item = (ReportItemVO) v.getTag();
        this.listener.onClick(v, item);
    }

    @NonNull
    @Override
    public AiReportViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.aireport_item, parent, false);
        return new AiReportViewHolder(view, AiReportUIAdapter.this::onItemClick);
    }

    @Override
    public void onBindViewHolder(@NonNull AiReportViewHolder holder, int position) {

        ReportItemVO item = this.items.get(position);
        Log.e(TAG, String.format("AiReportViewHolder position:%d, item:%s", position, item));

        // model & ui 绑定
        holder.itemView.setTag(item);

        // 更新 ui
        item.updateUI(holder);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    /**
     * 清空分页条件后重新拉取数据
     */
    public void loadData() {
        this.query.resetPage().next(this);
    }

    /**
     * 保存数据 & 刷新列表
     *
     * @param offset 第一条记录的位置
     * @param total  总条数
     * @param list   当前页记录
     */
    @Override
    public void onResult(int offset, int total, List<ReportInfoVO> list) {

        Log.i(TAG, String.format("onResult offset:%d total:%d size:%d", offset, total, list.size()));
        List<ReportItemVO> target = new ArrayList<>();
        if (offset > 0) {
            target.addAll(this.items);
        }

        int dirty = Math.min(target.size(), offset);
        for (ReportInfoVO vo : list) {
            ReportItemVO item = ReportItemVO.createFrom(vo);
            if (offset >= target.size()) {
                target.add(item);
            } else {
                target.set(offset, item);
            }
            offset++;
        }

        // 通知主线程加载数据
        activity.runOnUiThread(() -> {
            Log.i(TAG, String.format("notifyItemInserted dirty:%d size:%d", dirty, target.size()));
            this.items = target;
            notifyItemInserted(dirty);
        });
    }

    /**
     * 显示出错提示
     *
     * @param message 错误消息
     */
    @Override
    public void onError(String message) {
        ToastUtil.show(message);
    }
}
