package dji.sampleV5.aircraft.page.fly.controller;


import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.EXECUTION_PAUSED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_FINISH;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_PAUSE;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_RESUME;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_START;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_STOP;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.MISSION_UPLOAD;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.READY_TO_EXECUTE;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.UNKNOWN;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_FAILED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_FINISHED;
import static dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper.VALUE_SUCCEED;

import android.app.AlertDialog;
import android.app.Dialog;
import android.graphics.Bitmap;
import android.media.Image;
import android.text.TextUtils;
import android.view.SurfaceView;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.drawable.DrawableCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;

import com.hjq.toast.Toaster;

import org.json.JSONException;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import dji.sampleV5.aircraft.common.drone.mission.BaseMissionHelper;
import dji.sampleV5.aircraft.common.drone.mission.WaypointMissionHelper;
import dji.sampleV5.aircraft.common.json.JsonUtil;
import dji.sampleV5.aircraft.data.mission.LaserUploadBean;
import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.data.mission.MissionStatus;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MissionMapPainter;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.util.ImageUtils;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.mvvm.util.temp.ThermalMeasureHelper;
import dji.sampleV5.aircraft.net.bean.Flightpath;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.Mission;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.plan.BaseMission;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.CompressUtil;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sdk.keyvalue.key.BatteryKey;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraMode;
import dji.sdk.keyvalue.value.camera.GeneratedMediaFileInfo;
import dji.sdk.keyvalue.value.camera.LaserMeasureInformation;
import dji.sdk.keyvalue.value.camera.LaserMeasureState;
import dji.sdk.keyvalue.value.camera.PhotoPanoramaMode;
import dji.sdk.keyvalue.value.common.EmptyMsg;
import dji.sdk.keyvalue.value.flightcontroller.GoHomeState;
import dji.sdk.keyvalue.value.gimbal.GimbalAngleRotation;
import dji.sdk.keyvalue.value.gimbal.GimbalMode;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.waypoint3.model.WaypointMissionExecuteState;
import kotlin.Pair;
import kotlin.Unit;


public class MissionController implements BaseMissionHelper.IOperationResultListener, View.OnClickListener {
    private final String TAG = this.getClass().getName();
    private static final int ENABLE_NONE = 0x0000;
    private static final int ENABLE_START_BUTTON = 0x0001;
    private static final int ENABLE_PAUSE_BUTTON = 0x0002;
    private static final int ENABLE_STOP_BUTTON = 0x0004;

    private int lastShowToastID;
    private long lastUploadTime;
    private long lastShowToastTime;

    private boolean isFlying;
    private boolean lastFly;
    private boolean isClickStartMission;

    private DefaultLayoutActivity activity;
    private ActivityDefaultLayoutBinding binding;

    private MissionDetail missionDetail = new MissionDetail();
    private MissionStatus missionStatus = new MissionStatus();
    private WaypointMission mChosenMission;
    private MissionMapPainter mMissionMapPainter;
    private BaseMissionHelper missionHelper;

    private boolean autoFly;
    private boolean isH20;
    private boolean is2D;//是否是光伏2d扫图
    private ArrayList<LaserUploadBean> laserUploadBeanArrayList = new ArrayList<>();
    private MissionJson missionJson;
    private int currentWaypointIndex = -1;
    private float targetDistance = -1;
    private CommonCallbacks.KeyListener isFlyingListener;
    private CommonCallbacks.KeyListener generatedMediaFileListener;
    private CommonCallbacks.KeyListener laserListener;
    private boolean isResumeMission = false;
    private boolean isCustomMission = false;
    private boolean isMissionFinish = false;
    private RtmpController rtmpController;
    private MissionDetail resumeMissionDetail;
    private int missionType;
    private final ProductType productType;
    private boolean enableHighTemp = false;
    private int highTemp = 50;
    private ThermalMeasureHelper thermalMeasureHelper;
    public int getCurrentWaypointIndex() {
        return currentWaypointIndex;
    }

    public WaypointMission getmChosenMission() {
        return mChosenMission;
    }

    public MissionController(DefaultLayoutActivity activity, RtmpController rtmpController) {
        this.activity = activity;
        this.binding = activity.getBinding();
        this.rtmpController = rtmpController;
        productType = DJIAircraftApplication.getInstance().getProductType();
    }

    public void safeResumeMission() {
        if (thermalMeasureHelper != null && thermalMeasureHelper.isUploading()) {
            XLogUtil.INSTANCE.d(TAG, "文件上传中，延迟恢复任务");
            ContextUtil.getHandler().postDelayed(this::safeResumeMission, 1000);
            return;
        }
        if (missionHelper != null) {
            missionHelper.resumeMission();
        }
    }

    public void safePauseMission() {
        if (thermalMeasureHelper != null && thermalMeasureHelper.isUploading()) {
            XLogUtil.INSTANCE.d(TAG, "文件上传中，延迟暂停任务");
            ContextUtil.getHandler().postDelayed(this::safePauseMission, 1000);
            return;
        }
        if (missionHelper != null) {
            missionHelper.pauseMission();
        }
    }

    public void onRemoteMission(String strMission) {
        autoFly = true;
        initMission(strMission);
        initUI(activity.getMapController());
        if (autoFly && mChosenMission != null) {
            //ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 10);
        }
    }

    public void onConnect(boolean connect) {
        if (connect) {
            isFlyingListener = new CommonCallbacks.KeyListener<Boolean>() {
                @Override
                public void onValueChange(@Nullable Boolean oldValue, @Nullable Boolean now) {
                    if (now != null) {
                        isFlying = now;
                        if (!lastFly && now) {
                            //ToastUtil.show("起飞");
                            DJIAircraftApplication.getInstance().setMotorOn(true);
                            DJIAircraftApplication.getInstance().setSubState(204);
                            ContextUtil.getHandler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    DJIAircraftApplication.getInstance().setSubState(209);
                                }
                            }, 3000);
                        }
                        if (!isFlying && lastFly && mChosenMission != null) {
                            //测试发现御2行业进阶最后一个点currentWaypointIndex不更新.
                            DJIAircraftApplication.getInstance().setMotorOn(false);
                            DJIAircraftApplication.getInstance().setSubState(102);
                            //stopLaser();
                            missionStatus.setMissionBatch("");
                            DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
                            if (isMissionFinish && currentWaypointIndex == mChosenMission.getWaypointList().size() || currentWaypointIndex == (mChosenMission.getWaypointList().size() - 1)) {
                                if (missionDetail.getEndTime() == null) { //再加一层保护，如果飞机落地我回去判断有没有存结束时间，如果没有我就把落地时间作为结束时间
                                    missionDetail.setEndTime(System.currentTimeMillis());
                                }
                                missionDetail.setComplete(true);
                                SpUtil.setLaserList(laserUploadBeanArrayList);
                                SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
                                /*new AlertDialog.Builder(activity)
                                        .setMessage("任务已经完成，是否立即下载上传图片？")
                                        .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                                            ToastUtil.show("稍后可以到首页飞行历史中处理");
                                        })
                                        .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                                            rtmpController.stopRtmp();
                                            MissionPhotoUploader.getInstance().onMissionComplete(mChosenMission.getId(), binding);
                                        }).create().show();*/
                            } else {
                                if (currentWaypointIndex > 0) {//可能还没飞到第一个点就返回了，这个时候不做保存
                                    ToastUtil.show("任务还未完成，进行保存");
                                }
                            }
                        }
                        lastFly = now;
                    }
                }
            };
            KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyIsFlying), this, isFlyingListener);

            addPhotoListener();
            //isH20 = DJIHelper.getInstance().isH20();

            KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyGoHomeStatus), this, new CommonCallbacks.KeyListener<GoHomeState>() {
                @Override
                public void onValueChange(@Nullable GoHomeState oldValue, @Nullable GoHomeState newValue) {
                    if (newValue != null) {
                        switch (newValue) {
                            case RETURNING_TO_HOME:
                                activity.stopMeasureTem();
                                binding.viewMissionPanel.main.setVisibility(View.GONE);
                                DJIAircraftApplication.getInstance().setSubState(302);
                                break;
                            case LANDING:
                                DJIAircraftApplication.getInstance().setSubState(306);
                                break;
                        }
                    }
                }
            });

            //实时监听无人机电池电量
            KeyManager.getInstance().listen(KeyTools.createKey(BatteryKey.KeyChargeRemainingInPercent), this, (integer, curValue) -> {
                if (curValue == null) return;
                if (missionHelper == null) return;
                int hoverValue = dji.v5.ux.core.util.SpUtil.getLowBatteryHover();
                XLogUtil.INSTANCE.i(TAG, " --当前无人机状态" + missionHelper.getCurrentState() + "----DJI状态" +  WaypointMissionExecuteState.EXECUTING.value() + "----当前电池电量：" + curValue + " ---悬停电池电量：" + hoverValue);
                //17表示任务执行中，当低电量达到阈值并且在执行任务时则暂停任务
                if ((hoverValue >= curValue) && (missionHelper.getCurrentState() == 17)) {
                    Toaster.show("当前电量不足，任务暂停中");
                    missionHelper.pauseMission();
                }
            });

            if (!TextUtils.isEmpty(SpUtil.getMissionBatch())) {
                //showResumeMissionDialog();
            }
        } else {
            if (isFlyingListener != null) {
                KeyManager.getInstance().cancelListen(isFlyingListener);
                setLayoutGroupEnable(ENABLE_START_BUTTON);
            }
            if (generatedMediaFileListener != null) {
                KeyManager.getInstance().cancelListen(generatedMediaFileListener);
            }
        }
    }

    private void addPhotoListener() {
        generatedMediaFileListener = new CommonCallbacks.KeyListener<GeneratedMediaFileInfo>() {
            @Override
            public void onValueChange(@Nullable GeneratedMediaFileInfo oldValue, @Nullable GeneratedMediaFileInfo newValue) {
                if (newValue != null) {
                    LaserUploadBean laserUploadBean = new LaserUploadBean();
                    laserUploadBean.setIndex(newValue.getIndex());
                    laserUploadBean.setTargetDistance(targetDistance);
                    laserUploadBeanArrayList.add(laserUploadBean);
                }

            }
        };
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyNewlyGeneratedMediaFile), this, generatedMediaFileListener);
    }

    public void onControlMission(MissionJson missionJson) {
        if (mChosenMission == null) {
            ToastUtil.show("没有执行的任务");
            return;
        }
        switch (missionJson.getCode()) {
            case "pause":
                ToastUtil.show("接收到暂停任务指令");
                missionHelper.pauseMission();
                break;
            case "estop":
            case "stop":
                ToastUtil.show("接收到停止任务指令");
                missionHelper.stopMission();
                break;
            case "resume":
                ToastUtil.show("接收到恢复任务指令");
                int state = missionHelper.getCurrentState();
                if (state == EXECUTION_PAUSED) {
                    missionHelper.resumeMission();
                }
                break;
            case "rths":
                ToastUtil.show("接收到返航指令");
                if (isFlying) {
                    KeyManager.getInstance().performAction(KeyTools.createKey(FlightControllerKey.KeyStartGoHome), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                        @Override
                        public void onSuccess(EmptyMsg emptyMsg) {

                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {

                        }
                    });
                }
                break;
        }
    }

    public void onRemoteMission(MissionJson missionJson) {
        XLogUtil.INSTANCE.d(TAG, "onRemoteMission" + CommExtKt.toJsonStr(missionJson));
        laserUploadBeanArrayList.clear();
        this.missionJson = missionJson;
        if (missionJson.getIsGzipMission() == 1) {
            String missionString = CompressUtil.decompressEncode(missionJson.getGzipMission());
            List<Mission> list = com.alibaba.fastjson.JSONArray.parseArray(missionString, Mission.class);
            missionJson.setMission(list);
        }
        String params = missionJson.getParams();
        try {
            org.json.JSONObject jsonObject = new org.json.JSONObject(params);
            missionType = jsonObject.optInt("missionType");//万象的任务需要去适配任务类型
            enableHighTemp = jsonObject.optBoolean("enableHighTemp"); //是否开启高温预警
            highTemp = jsonObject.optInt("highTemp"); //高温预警阈值
        } catch (JSONException e) {
            e.printStackTrace();
        }
        LiveDataEvent.INSTANCE.isInfraredTemperature().setValue(enableHighTemp);
        missionStatus.setMissionID(missionJson.getMissionID());
        missionStatus.setMissionBatch(missionJson.getMissionBatch());
        missionStatus.setMissionName(missionJson.getMissionName());
        DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
        DJIAircraftApplication.getInstance().setSubState(202);

        autoFly = true;
        Mission mission = missionJson.getMission().get(0);
        List<Flightpath> flightPaths = mission.getFlightpath();
        ToastUtil.show("接收到任务指令");
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setGlobalWaypointTurnMode(mission.getGlobalWaypointTurnMode());
        waypointMission.setHeadingMode(WaypointMission.HeadingMode.UsingWaypointHeading);
        if (missionType == 4) {//全景图任务结束后悬停
            waypointMission.setFinishedAction(BaseMission.FinishedAction.NoAction);
        } else {
            waypointMission.setFinishedAction(BaseMission.FinishedAction.GoHome);
        }

        XLogUtil.INSTANCE.d("TAG", "onRemoteMission: speed" + mission.getAutoflightSpeed());
        waypointMission.setFlySpeed(mission.getAutoflightSpeed());
        waypointMission.setMissionID(missionJson.getMissionID());
        waypointMission.setUavSTAltitude(missionJson.getUavSTAltitude());
        waypointMission.setUavRHAltitude(missionJson.getUavRHAltitude());
        waypointMission.setMissionBatch(missionJson.getMissionBatch());
        waypointMission.setName(missionJson.getMissionName());
        int uavRHAltitude = missionJson.getUavRHAltitude();
        uavRHAltitude = uavRHAltitude > 0 ? uavRHAltitude : 100;
        setGoHomeHeight(uavRHAltitude);
        for (Flightpath flightpath : flightPaths) {
            WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
            //waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));

            if (mission.getGCSType() == 2) {
                LocateInfo locateInfo = GCJ02_WGS84.gcj02_To_Wgs84(flightpath.getLatitude(), flightpath.getLongitude());
                waypoint.setLatLng(new AppLatLng(locateInfo.getLatitude(), locateInfo.getLongitude()));
            } else {
                waypoint.setLatLng(new AppLatLng(flightpath.getLatitude(), flightpath.getLongitude()));
            }
            //double[] position = GeoSysConversion.gcj02toWGS84(flightpath.getLatitude(), flightpath.getLongitude());
            waypoint.setGimbalPitch((short) flightpath.getGimbal());
            waypoint.setHeading((short) (flightpath.getHeading() >= -180 && flightpath.getHeading() <= 180 ? flightpath.getHeading() : 0));
            waypoint.setAltitude(flightpath.getAltitude());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            waypoint.setPointName(flightpath.getName());
            waypoint.setTurnMode(BaseMission.TurnMode.Clockwise);
            waypoint.setSpeed(flightpath.getSpeed());
            waypoint.setWaypointTurnMode(flightpath.getWaypointTurnMode());
            waypoint.addWaypointAction(new WaypointMission.Waypoint.Action(WaypointMission.Waypoint.ActionType.StartTakePhoto, 0));
            waypointMission.addWaypoint(waypoint);
        }

        //在这清除上一次的任务
        if (mChosenMission != null && mMissionMapPainter != null) {
            mMissionMapPainter.remove(mChosenMission);
        }

        //判断这个任务是不是上次未执行完的
        String missionBatch = SpUtil.getMissionBatch();
        resumeMissionDetail = SpUtil.getMissionData(missionBatch);
        if (resumeMissionDetail == null ) {
            isResumeMission = false;
            isCustomMission = false;
            dealMission(waypointMission);
        } else {
            WaypointMission resumeWaypointMission = resumeMissionDetail.getWaypointMission();
            String resumeMissionID = resumeWaypointMission != null ? resumeWaypointMission.getMissionID() : null;
            if (resumeMissionDetail.isComplete() || TextUtils.isEmpty(resumeMissionID) || !TextUtils.equals(resumeMissionID, waypointMission.getMissionID())) {
                isResumeMission = false;
                isCustomMission = false;
                dealMission(waypointMission);
            } else {
                int position = resumeMissionDetail.getCurrentPosition();
                AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity());
                AlertDialog dialog = builder.create();
                dialog.setCanceledOnTouchOutside(true);
                dialog.setCancelable(false);

                View view = View.inflate(ContextUtil.getCurrentActivity(), R.layout.resume_dialog, null);
                Button btnReStart = view.findViewById(R.id.btn_restart);
                Button btnContinue = view.findViewById(R.id.btn_continue);
                Button btnCustomePoint = view.findViewById(R.id.btn_custom_point);
                btnReStart.setOnClickListener(v -> {
                    isResumeMission = false;
                    isCustomMission = false;
                    dealMission(waypointMission);
                    dialog.dismiss();
                });
                // 设置按钮点击监听器，用于处理续飞逻辑
                btnContinue.setOnClickListener(v -> {
                    isResumeMission = true;
                    isCustomMission = false;
                    if (productType == ProductType.M300_RTK || productType == ProductType.M350_RTK) {
                        List<WaypointMission.Waypoint> resumeWaypointList = new ArrayList<>();
                        WaypointMission resumeMission;
                        for (int i = position + 1; i < waypointMission.getWaypointList().size(); i++) {
                            resumeWaypointList.add(waypointMission.getWaypointList().get(i));
                        }
                        resumeMission = waypointMission;
                        resumeMission.setWaypointList(resumeWaypointList);
                        resumeMission.setMissionID(""); //这里置空的原因是续飞的任务不支持再次续飞，方便判断

                        this.mChosenMission = resumeMission;
                        activity.runOnUiThread(() -> {
                            missionDetail = resumeMissionDetail.clone();
                            initUI(activity.getMapController());
                            initMissionHelper();
                            if (dji.v5.ux.core.util.SpUtil.getIsAutoFly()) {
                                ToastUtil.show("三秒后自动执行任务");
                                ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                            }
                            //ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 10);
                        });
                    } else {
                        dealMission(waypointMission);
                    }
                    dialog.dismiss();
                });

                btnCustomePoint.setOnClickListener(v -> {
                    dialog.dismiss();
                    showCustomPointDialog(waypointMission);
                });
                //设置背景透明,去四个角
                dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
                dialog.show();
                dialog.getWindow().setLayout(DensityUtil.dip2px(ContextUtil.getCurrentActivity(), 290), LinearLayout.LayoutParams.WRAP_CONTENT);
//        dialog.getWindow().setWindowAnimations(R.style.AnimMM);
                dialog.setContentView(view);
            }
        }
    }

    private void showCustomPointDialog(WaypointMission waypointMission) {
        Dialog dialog = new Dialog(ContextUtil.getCurrentActivity());
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(false);

        View view = View.inflate(ContextUtil.getCurrentActivity(), R.layout.custom_point_dialog, null);
        Button buttonCancel = view.findViewById(R.id.btn_alert_cancel);
        Button buttonOk = view.findViewById(R.id.btn_alert_ok);
        EditText editPoint = view.findViewById(R.id.edit_point);
        buttonCancel.setOnClickListener(v -> {
            dialog.dismiss();
        });
        buttonOk.setOnClickListener(v -> {
            int position = Integer.parseInt(editPoint.getText().toString());
            resumeMissionDetail.setCurrentPosition(position);//下面上传任务动作要helper里面会用到
            if (position < 1 || position > waypointMission.getWaypointList().size() - 1) {
                ToastUtil.show("航点不在区间内，请重新填写");
                return;
            }
            List<WaypointMission.Waypoint> resumeWaypointList = new ArrayList<>();
            WaypointMission resumeMission;
            for (int i = position - 1; i < waypointMission.getWaypointList().size(); i++) {
                resumeWaypointList.add(waypointMission.getWaypointList().get(i));
            }
            resumeMission = waypointMission;
            resumeMission.setWaypointList(resumeWaypointList);
            resumeMission.setMissionID(""); //这里置空的原因是续飞的任务不支持再次续飞，方便判断

            this.mChosenMission = resumeMission;
            activity.runOnUiThread(() -> {
                isResumeMission = false;
                isCustomMission = true;
                missionDetail = resumeMissionDetail;
                initUI(activity.getMapController());
                initMissionHelper();
                if (SpUtil.getIsAutoFly()) {
                    ToastUtil.show("三秒后自动执行任务");
                    ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                }
            });
            dialog.dismiss();
        });
        //设置背景透明,去四个角
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.show();
        dialog.getWindow().setLayout(DensityUtil.dip2px(ContextUtil.getCurrentActivity(), 290), LinearLayout.LayoutParams.WRAP_CONTENT);
        dialog.setContentView(view);


    }

    private void dealMission(WaypointMission waypointMission) {
        this.mChosenMission = waypointMission;
        if (autoFly && mChosenMission != null) {
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    initUI(activity.getMapController());
                    initMissionHelper();
                    if (SpUtil.getIsAutoFly()) {
                        ToastUtil.show("三秒后自动执行任务");
                        ContextUtil.getHandler().postDelayed(() -> binding.viewMissionPanel.ivMissionStartLay.callOnClick(), 3000);
                    }
                }
            });
        }
    }

    private void setGoHomeHeight(int goHomeHeight) {
        KeyManager.getInstance().setValue(KeyTools.createKey(FlightControllerKey.KeyGoHomeHeight), goHomeHeight, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("返航高度已设置成" + goHomeHeight + "米");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("返航高度设置失败:" + error.description());
            }
        });
    }


    private void initMission(String missionString) {
        if (missionString != null) {
            this.mChosenMission = JsonUtil.fromJson(missionString, WaypointMission.class);
        }
    }

    /**
     * 初始化任务助手对象。
     * 该方法用于在选择特定任务时初始化或重新初始化任务助手（WaypointMissionHelper），
     * 并设置任务过程的监听回调，以更新当前任务的状态和进度。
     */
    private void initMissionHelper() {
        // 检查是否已选择了任务
        if (mChosenMission != null) {
            // 检查任务助手是否已经初始化
            if (missionHelper != null) {
                // 如果任务助手已初始化，则打印日志并清除旧的助手实例
                XLogUtil.INSTANCE.d("TAG", "initMissionHelper: 再次接收任务，初始化helper");
                missionHelper.removeListener(); // 移除之前的监听器
                missionHelper = null; // 清除旧的助手实例
            }
            int waypointLength = mChosenMission.getWaypointList().size();
            if (waypointLength > 1) {
                binding.missionProgress.setVisibility(View.VISIBLE);
                binding.missionProgress.setText("0/" + waypointLength);
                boolean isOpenCheckList = dji.v5.ux.core.util.SpUtil.getOpenCheckList();
                if (isOpenCheckList && binding.btnCheckList.getVisibility() == View.GONE) {
                    binding.btnCheckList.setVisibility(View.VISIBLE);
                }
                if (!Objects.equals(mChosenMission.getWaypointList().get(0).getPointName(), ""))  {
                    binding.missionName.setVisibility(View.VISIBLE);
                    binding.missionName.setText("航点名：" + mChosenMission.getWaypointList().get(0).getPointName());
                }
            }
            // 创建新的任务助手实例并设置结果监听器
            missionHelper = new WaypointMissionHelper();
            missionHelper.setResultListener(this);
            // 设置任务过程的回调，用于更新当前 waypoint 的索引和状态
            missionHelper.setMissionProcessCallback(index -> {
                if (index >= 0 && index <= mChosenMission.getWaypointList().size()) { // 添加索引检查
                    binding.missionProgress.setText((index + 1) + "/" + mChosenMission.getWaypointList().size());
                    binding.missionName.setText("航点名：" + mChosenMission.getWaypointList().get(index).getPointName());
                    if (index > 0 && index > currentWaypointIndex) {
                        currentWaypointIndex = index;
                        //ToastUtil.show("当前第几个点" + currentWaypointIndex);
                        //这是为了mqtt上报
                        missionStatus.setCurrentIndex(currentWaypointIndex);
                        DJIAircraftApplication.getInstance().setMissionStatus(missionStatus);
                        //这是为了断点续飞
                        missionDetail.setCurrentPosition(currentWaypointIndex);
                        SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
                    }
                } else {
                    XLogUtil.INSTANCE.e(TAG, "Invalid index: " + index + ", size: " + waypointLength); // 添加日志记录
                }
            });
            // 检查是否需要启用红外测温功能
            if (enableHighTemp) {
                activity.changeDisplayMode(2);
                thermalMeasureHelper = new ThermalMeasureHelper(
                        binding,
                        mChosenMission.getMissionBatch());
                thermalMeasureHelper.setTemperatureThreshold(highTemp);
            }
        }
    }


    private void initUI(MapController mapController) {
        if (mChosenMission != null) {

            binding.viewMissionPanel.main.setVisibility(View.VISIBLE);
            mMissionMapPainter = new MissionMapPainter(activity, mapController);

            mMissionMapPainter.draw(mChosenMission, true);

            binding.viewMissionPanel.ivMissionStartLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionPauseLay.setOnClickListener(this);
            binding.viewMissionPanel.ivMissionStopLay.setOnClickListener(this);

            setLayoutGroupEnable(ENABLE_START_BUTTON);
        }
    }

    public void onDestroy() {
        if (mMissionMapPainter != null) {
            mMissionMapPainter.removeAll();
        }

        if (missionHelper != null) {
            missionHelper.removeListener();
        }

        KeyManager.getInstance().cancelListen(this);
        /*if (isFlyingListener != null) {
            KeyManager.getInstance().cancelListen(isFlyingListener);
        }
        if (generatedMediaFileListener != null) {
            KeyManager.getInstance().cancelListen(generatedMediaFileListener);
        }*/

    }

    /**
     * 上传任务的函数。
     * 该方法首先检查是否在上次上传的2秒内，如果是，则不进行上传；如果不是，则进行上传操作。
     * 上传操作包括更新提醒文本，设置飞行路径，以及准备上传任务。
     */
    private void uploadMission() {
        long currentTime = System.currentTimeMillis();
        // 检查是否在2秒内重复上传
        if (currentTime - lastUploadTime < 2000) {
            return;
        } else {
            lastUploadTime = currentTime;
        }

        // 如果已选择任务，则开始上传流程
        if (mChosenMission != null) {
            // 更新提醒文本为上传开始
            updateRemindText(R.string.fly_mission_upload_started);
            // 更新任务开始图标进度为1%
            ContextUtil.getHandler().post(() -> binding.viewMissionPanel.ivMissionStart.updateProgress(0.01f));

            // 处理断点续飞的任务信息
            if (missionJson != null) {
                List<Flightpath> originFlightPaths = missionJson.getMission().get(0).getFlightpath();
                // 判断是自定义任务还是续飞任务，并设置对应的飞行路径
                if (isCustomMission) {
                    // 自定义任务，设置从当前位置之后的飞行路径
                    int position = resumeMissionDetail.getCurrentPosition();
                    List<Flightpath> resumeFlightPaths = new ArrayList<>();
                    for (int i = position - 1; i < originFlightPaths.size(); i++) {
                        resumeFlightPaths.add(originFlightPaths.get(i));
                    }
                    missionHelper.setFlyPath(resumeFlightPaths,missionJson);
                } else if (isResumeMission){
                    int position = resumeMissionDetail.getCurrentPosition();
                    if (productType == ProductType.M300_RTK || productType == ProductType.M350_RTK) {
                        List<Flightpath> resumeFlightPaths = new ArrayList<>();
                        for (int i = position + 1; i < originFlightPaths.size(); i++) {
                            resumeFlightPaths.add(originFlightPaths.get(i));
                        }
                        missionHelper.setFlyPath(resumeFlightPaths, missionJson);
                    } else {
                        missionHelper.setFlyPath(originFlightPaths, missionJson);
                    }
                } else {
                    // 普通任务，设置全部飞行路径
                    missionHelper.setFlyPath(originFlightPaths,missionJson);
                }
            }

            // 设置任务信息
            missionHelper.setWayPointMission(mChosenMission);
            // 延迟1秒后准备上传任务
            ContextUtil.getHandler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    prepareForUpload();
                }
            }, 1000);

        }
    }


    private void startMission() {
        if (thermalMeasureHelper != null && thermalMeasureHelper.isUploading()) {
            ToastUtil.show("文件上传中，请等待上传完成");
            return;
        }
        XLogUtil.INSTANCE.d("startMission", "startMission: ");
        if (readyForMission()) {
            int state = missionHelper.getCurrentState();
            if (state == EXECUTION_PAUSED) {
                missionHelper.resumeMission();
            } else if (state == READY_TO_EXECUTE) {
                if (isResumeMission && productType != ProductType.M300_RTK && productType != ProductType.M350_RTK) {
                    missionHelper.startBPMission();
                } else {
                    missionHelper.startMission();
                }
            }
        }
    }

    /*现在把起飞，上传任务和开始任务合并到一个按钮操作了，但是前提是飞机必须先起飞才能上传任务*/
    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_mission_start_lay:

                if (!readyForStart()) {
                    return;
                }


               /* if (!isFlying) {
                    isClickStartMission = true;
                    if (mChosenMission != null) {
                        new AutoTakeOffDialog(autoFly);
                    }
                } else {*/
                isMissionFinish = false;
                //*判断是否是暂停状态*//*
                int state = missionHelper.getCurrentState();
                if (state == EXECUTION_PAUSED) {
                    missionHelper.resumeMission();
                } else {
                    setGimbalFollowMode();
                    uploadMission();
                }
                // }
                break;
            case R.id.iv_mission_pause_lay:
                missionHelper.pauseMission();
                break;
            case R.id.iv_mission_stop_lay:
                missionHelper.stopMission();
                break;
        }
    }

    private void setGimbalFollowMode() {
        KeyManager.getInstance().setValue(KeyTools.createKey(GimbalKey.KeyGimbalMode), GimbalMode.YAW_FOLLOW, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                rotateGimbal();
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("设置云台跟随失败" + error.description());
            }
        });


    }

    private void rotateGimbal() {
        if (mChosenMission.getWaypointList().isEmpty()) return;
        float firstPointPitch = mChosenMission.getWaypointList().get(0).getGimbalPitch();
        GimbalAngleRotation gimbalAngleRotation = new GimbalAngleRotation();
        gimbalAngleRotation.setPitch((double) firstPointPitch);
        gimbalAngleRotation.setRollIgnored(true);
        gimbalAngleRotation.setYawIgnored(true);
        KeyManager.getInstance().performAction(KeyTools.createKey(GimbalKey.KeyRotateByAngle), gimbalAngleRotation, new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {
                //ToastUtil.show("设置云台俯仰角度成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("设置云台俯仰角度失败:" + error.description());
            }
        });
    }

    private boolean readyForMission() {
        // 设置返航点
        /*Boolean isHomeSet = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_HOME_LOCATION_SET));
        if (isHomeSet == null || !isHomeSet) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.set_home_first, false);
            return false;
        }
        // 需起飞无人机
        Boolean isFlying = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_FLYING));
        if (isFlying == null || !isFlying) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_first_route, false);
            return false;
        }
*/
        return true;
    }

    private boolean readyForStart() {
        /*if (!DJIHelper.getInstance().isAircraftConnected()) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.please_check_connection, true);
            return false;
        }

        Boolean isLanding = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_LANDING));
        if (isLanding == null || isLanding) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }

        Boolean isGoingHome = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_GOING_HOME));
        if (isGoingHome == null || isGoingHome) {
            PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(R.string.fly_auto_home, false);
            return false;
        }*/

        return true;
    }

    private void prepareForUpload() {
        ContextUtil.getHandler().post(() -> setLayoutGroupEnable(ENABLE_NONE));
        missionHelper.stopLastAndUploadMission();
    }

    @Override
    public void onResult(int state, float value, String err) {
        switch (state) {
            case MISSION_UPLOAD:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUpload(value, err);
                break;
            case MISSION_START:
                currentWaypointIndex = -1;
                missionDetail.setComplete(false);
                onResultStart(value, err);
                break;
            case MISSION_PAUSE:
                onResultPause(value, err);
                break;
            case MISSION_RESUME:
                onResultResume(value, err);
                break;
            case MISSION_STOP:
                //currentWaypointIndex = -1;
                onResultStop(value, err);
                break;
            case MISSION_FINISH:
                //currentWaypointIndex = -1;
                onResultMissionFinish(value, err);
                break;
            case UNKNOWN:
                setLayoutGroupEnable(ENABLE_START_BUTTON);
                onResultUnknown(value, err);
                break;
        }
    }

    private void onResultUpload(float value, String err) {
        if (value > 0 && value < 1) {
            XLogUtil.INSTANCE.d("TAG", "onResultUpload: " + value);
            binding.viewMissionPanel.ivMissionStart.updateProgress(value);
        } else if (value == VALUE_FINISHED) {
            setLayoutGroupEnable(ENABLE_NONE);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
            updateRemindText(R.string.fly_mission_upload_finished);
            updateRemindText(R.string.fly_mission_prepare);
            /*上传任务后直接开始任务*/
            ContextUtil.getHandler().postDelayed(this::startMission, 3000);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务上传失败:" + err, true);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
        }
    }


    private void onResultStart(float value, String err) {
        if (value == VALUE_SUCCEED) {
            if (!isResumeMission && !isCustomMission) {
                missionDetail.setStartTime(System.currentTimeMillis());
            }
            //这里断点续飞只支持一次，如果第二次没飞完，任务只能作废
            missionDetail.setWaypointMission(mChosenMission);
            SpUtil.setMissionBatch(mChosenMission.getMissionBatch());
            SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);

            updateRemindText(R.string.fly_mission_start_succeed);
            if (isFlying) {   //这么判断的原因是，当虚拟摇杆关闭后，重新开始任务，还是显示暂停
                DJIAircraftApplication.getInstance().setSubState(209);
            }
            if(SpUtil.getIsMeasureTemOpen()){  //判断是否开启红外高温预警
                activity.startMeasureTem();
            }
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            MissionPhotoUploader.getInstance().setUploading(false);//每次开始前初始化上传状态，可能上一次上传失败了，不能影响下次任务执行
            DJIAircraftApplication.getInstance().setLastWaypoint(mChosenMission.getWaypointList().get(mChosenMission.getWaypointList().size() - 1).getLatLng());
            //startLaser();
            if (thermalMeasureHelper != null) {
                thermalMeasureHelper.startMeasure(); //开始测温
                XLogUtil.INSTANCE.d(TAG, "开启红外高温预警");
            }
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务开始失败:" + err, true);
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            binding.viewMissionPanel.ivMissionStart.backToNormal();
        }
    }

    private void onResultStop(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON);
            binding.missionProgress.setVisibility(View.GONE);
            binding.missionName.setVisibility(View.GONE);
            binding.btnCheckList.setVisibility(View.GONE);
            binding.viewMissionPanel.main.setVisibility(View.GONE);
            updateRemindText(R.string.fly_mission_stop_succeed);
            //DJIAircraftApplication.getInstance().setLastWaypoint(null);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_STOP);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务停止失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_STOP, "任务结束失败：" + err, "", false);
        }
    }

    private void onResultPause(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_START_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_pause_succeed);
            DJIAircraftApplication.getInstance().setSubState(221);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_PAUSE);

        } else if (value == VALUE_FAILED) {
            updateRemindText("任务暂停失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_PAUSE, "任务暂停失败：" + err, "", false);

        }
    }

    private void onResultResume(float value, String err) {
        if (value == VALUE_SUCCEED) {
            setLayoutGroupEnable(ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON);
            updateRemindText(R.string.fly_mission_resume_succeed);
            DJIAircraftApplication.getInstance().setSubState(209);
            //ServerResponse.getInstance().postSucceed(RemoteCmdMgr.TYPE_ROUTE_RESUME);
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务恢复失败:" + err, true);
            //ServerResponse.getInstance().post(RemoteCmdMgr.TYPE_ROUTE_RESUME, "任务恢复失败：" + err, "", false);
        }
    }

    private void onResultUnknown(float value, String err) {
        if (value == VALUE_FAILED) {
            updateRemindText("未知错误:" + err, true);
        }
    }

    private void onResultMissionFinish(float value, String err) {
        if (value == VALUE_SUCCEED) {
            //MissionDetail missionDetail = SpUtil.getMissionData(mChosenMission.getId());
            missionDetail.setEndTime(System.currentTimeMillis());
            SpUtil.setMissionData(mChosenMission.getMissionBatch(), missionDetail);
            isMissionFinish = true;
            binding.missionProgress.setVisibility(View.GONE);
            binding.missionName.setVisibility(View.GONE);
            binding.viewMissionPanel.main.setVisibility(View.GONE);
            binding.btnCheckList.setVisibility(View.GONE);
            updateRemindText(R.string.mission_complete);
            LiveDataEvent.INSTANCE.isMissionEnd().postValue(true);
            ToastUtil.show(R.string.mission_complete);
            //停止红外测温
            if (thermalMeasureHelper != null) {
                thermalMeasureHelper.cleanup();
                thermalMeasureHelper = null;
            }
        } else if (value == VALUE_FAILED) {
            updateRemindText("任务恢复失败:" + err, true);
        }

        setLayoutGroupEnable(ENABLE_START_BUTTON);
    }

    private void startPano() {
        KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCameraMode), CameraMode.PHOTO_PANORAMA, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                //XLogUtil.INSTANCE.d(TAG, "PHOTO_PANORAMA: 设置相机模式为全景模式成功");
                KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyPhotoPanoramaProgress), this, new CommonCallbacks.KeyListener<Integer>() {
                    @Override
                    public void onValueChange(@Nullable Integer oldValue, @Nullable Integer newValue) {
                        if(newValue != null){
                            binding.missionProgress.setText(newValue + "%");
                            binding.missionProgress.setVisibility(View.VISIBLE);
                            if (newValue == 100) {
                                binding.missionProgress.setVisibility(View.GONE);
                                ToastUtil.show("全景图制作完成");
                                ContextUtil.getHandler().postDelayed(() -> startGoHome(), 4000);
                            }
                            //XLogUtil.INSTANCE.d(TAG, "PHOTO_PANORAM进度: " + newValue.intValue());
                        }
                    }
                });

                KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyPhotoPanoramaMode), PhotoPanoramaMode.MODE_SPHERE, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        //XLogUtil.INSTANCE.d(TAG, "设置全景拍照的模式成功:MODE_SPHERE ");

                        KeyManager.getInstance().performAction(KeyTools.createKey(CameraKey.KeyStartShootPhoto), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
                            @Override
                            public void onSuccess(EmptyMsg emptyMsg) {
                                //ToastUtil.show("全景开始拍照");
                            }

                            @Override
                            public void onFailure(@NonNull IDJIError error) {
                                ToastUtil.show("全景拍照失败:" + error.description());
                            }
                        });
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("设置全景拍照的模式失败");
                    }
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("设置相机模式为全景模式失败" + error.description());
            }
        });
    }

    private void startGoHome() {
        KeyManager.getInstance().performAction(KeyTools.createKey(FlightControllerKey.KeyStartGoHome), new CommonCallbacks.CompletionCallbackWithParam<EmptyMsg>() {
            @Override
            public void onSuccess(EmptyMsg emptyMsg) {

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {

            }
        });
    }

    private void startLaser() {
        if (DJIAircraftApplication.getInstance().getProductType() != ProductType.M30_SERIES
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M300_RTK
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M350_RTK) {
            return;
        }
        if (!mChosenMission.getName().contains("路点")) {
            is2D = true;
            XLogUtil.INSTANCE.d("TAG", "startLaser: ");
            laserListener = new CommonCallbacks.KeyListener<LaserMeasureInformation>() {
                @Override
                public void onValueChange(@Nullable LaserMeasureInformation oldValue, @Nullable LaserMeasureInformation laserMeasureInformation) {
                    if (laserMeasureInformation.getLaserMeasureState() == LaserMeasureState.NORMAL) {
                        targetDistance = laserMeasureInformation.getDistance().floatValue();
                        XLogUtil.INSTANCE.d("TAG", "startLaser onUpdate:" + targetDistance + "/" + laserMeasureInformation.getLaserMeasureState());
                    } else {
                        //XLogUtil.INSTANCE.d("TAG", "startLaser onUpdate: 参数不准");
                    }
                }
            };
            KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyLaserMeasureInformation), this, new CommonCallbacks.KeyListener<LaserMeasureInformation>() {
                @Override
                public void onValueChange(@Nullable LaserMeasureInformation oldValue, @Nullable LaserMeasureInformation newValue) {

                }
            });
            KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserMeasureEnabled), true, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("激光雷达开启");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("激光雷达失败" + error.description());
                }
            });
        } else {
            is2D = false;
        }
    }

    private void stopLaser() {
        if (DJIAircraftApplication.getInstance().getProductType() != ProductType.M30_SERIES
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M300_RTK
                && DJIAircraftApplication.getInstance().getProductType() != ProductType.M350_RTK) {
            return;
        }
        if (is2D && laserListener != null) {
            XLogUtil.INSTANCE.d("TAG", "stopLaser: ");
            KeyManager.getInstance().cancelListen(laserListener);
            KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyLaserMeasureEnabled), false, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("激光雷达关闭");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("激光雷达关闭失败" + error.description());
                }
            });
        }
    }

    private void setLayoutGroupEnable(int state) {
        boolean isStart = false, isPause = false, isStop = false;
        switch (state) {
            case ENABLE_START_BUTTON:
                isStart = true;
                break;
            case ENABLE_START_BUTTON | ENABLE_STOP_BUTTON:
                isStart = true;
                isStop = true;
                break;
            case ENABLE_PAUSE_BUTTON | ENABLE_STOP_BUTTON:
                isPause = true;
                isStop = true;
                break;
            default:
                break;
        }

        setLayoutEnable(binding.viewMissionPanel.ivMissionStartLay, isStart);
        setLayoutEnable(binding.viewMissionPanel.ivMissionPauseLay, isPause);
        setLayoutEnable(binding.viewMissionPanel.ivMissionStopLay, isStop);

        setImageButtonState(binding.viewMissionPanel.ivMissionStart, isStart);
        setImageButtonState(binding.viewMissionPanel.ivMissionPause, isPause);
        setImageButtonState(binding.viewMissionPanel.ivMissionStop, isStop);
    }

    private void setLayoutEnable(RelativeLayout layout, boolean enable) {
        layout.setClickable(enable);
    }

    private void setImageButtonState(ImageButton imageButton, boolean enable) {
        if (enable) {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.icon_light_1));
        } else {
            DrawableCompat.setTint(imageButton.getDrawable(), ContextUtil.getApplicationContext().getResources().getColor(R.color.black_gray));
        }
    }

    private void updateRemindText(int id) {
        if (id == lastShowToastID && (System.currentTimeMillis() - lastShowToastTime) < 1500) {
            return;//解决同一条信息回调多次，显示多次的问题
        }

        lastShowToastID = id;
        lastShowToastTime = System.currentTimeMillis();
        updateRemindText(ContextUtil.getString(id), false);
    }

    private void updateRemindText(final String text, final boolean isWarning) {
        ContextUtil.getHandler().post(() -> PopupWindowController.getInstance().getPopWindowFromCenter().addRemindingView(text, isWarning));
    }
}


