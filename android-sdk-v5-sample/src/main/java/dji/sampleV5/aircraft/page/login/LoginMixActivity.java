package dji.sampleV5.aircraft.page.login;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.CompoundButton;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;

import java.io.IOException;

import dji.sampleV5.aircraft.HomeActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityLoginBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.ui.activity.config.ConfigManagerActivity;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.util.FormatUtil;
import dji.sampleV5.aircraft.util.MD5;
import dji.sampleV5.aircraft.util.SharedPreferencesUtils;
import dji.sampleV5.aircraft.util.StringUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class LoginMixActivity extends AppCompatActivity implements View.OnClickListener, CancelAdapt {

    /**
     * 是否保存密码
     */
    private boolean issave = false;
    private boolean isshow = false;
    private boolean iscode = false;

    private int width;
    private int height;
    private String password = "password";
    private boolean isusernamelogin = true;
    private ActivityLoginBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ImmerseUtil.startImmerse(this);
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_login);
        WindowManager wm = (WindowManager) this.getSystemService(Context.WINDOW_SERVICE);
        width = wm.getDefaultDisplay().getWidth();
        height = wm.getDefaultDisplay().getHeight();
        /*Constant.windowwidth = width;
        Constant.windowheight = height;*/
        //setImmerseBar(false);
        initView();
        keyboardLayout(binding.reTop, binding.etPassword, true);
    }

    private void initView() {
        //binding.tvVerioncode.setText("当前版本："+ UAVM_INFO.getInstance().versionname);
        showinfo();
        binding.checkbox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                issave = b;
            }
        });

        //initLocalVideo();
        //binding.imgFrist.setImageDrawable(getThumbnail("video"));
        binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());

        binding.login.setOnClickListener(this);
        binding.imgIsshow.setOnClickListener(this);
        binding.tvSwitch.setOnClickListener(this);
        binding.tvCode.setOnClickListener(this);
        binding.tvLoginCode.setOnClickListener(this);
        binding.tvLoginPassword.setOnClickListener(this);
        binding.imgConfig.setOnClickListener(this);
        binding.imgConfig.setVisibility(ApiConfig.INSTANCE.getOPEN_API_CONFIG() ? View.VISIBLE : View.GONE);
    }

    private void showinfo() {
        if (!SharedPreferencesUtils.getParam(this, password, "").equals("")) {
            binding.etPassword.setText(String.valueOf(SharedPreferencesUtils.getParam(this, password, "")));
        }
        if (!SharedPreferencesUtils.getParam(this, "mobile", "").equals("")) {
            binding.etMobile.setText(String.valueOf(SharedPreferencesUtils.getParam(this, "mobile", "")));
            issave = true;
            binding.checkbox.setChecked(true);
        }
    }

    /**
     * 多个 onClick 公用当前实例作为 onclick 回调
     *
     * @param view
     */
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.login: // 登录
               /* if (mqttUtil != null) {
                    mqttUtil.disconnect();
                }*/
                if (checkParas()) {
                    login();
                }
                //jumtToMainPage();
                break;
            case R.id.img_isshow: // 切换密码显示隐藏
                isshow = !isshow;
                if (!isshow) {
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_nor_gray);
                    binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                } else {
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                    binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                }
                break;
            case R.id.tv_switch:
                iscode = !iscode;
                if (iscode) {
                    binding.tvPassword.setText("验证码");
                    binding.tvCode.setVisibility(View.VISIBLE);
                    password = "verifyCode";
                    if (!isshow) {
                        isshow = true;
                        binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                        binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                    }
                } else {
                    binding.tvCode.setVisibility(View.GONE);
                    binding.tvPassword.setText("密码");
                    password = "password";
                }
                break;
           /* case R.id.tv_code:
                if (!UavmInfo2.getInstance().issendcode ){
                    if (checkParas()){
                        getphonecode();
                    }
                }else {
                    Toast.makeText(LoginActivity.this,"不可重复点击",Toast.LENGTH_SHORT).show();
                }
                break;*/
            case R.id.tv_login_code:
                isusernamelogin = false;
                binding.etMobile.setInputType(InputType.TYPE_CLASS_PHONE);
                binding.tvName.setText("手机号");
                iscode = true;
                binding.tvCode.setVisibility(View.VISIBLE);
                binding.tvPassword.setText("验证码");
                password = "verifyCode";
                binding.tvLoginCode.setTextColor(getResources().getColor(R.color.ijk_transparent_dark));
                binding.tvLoginPassword.setTextColor(getResources().getColor(R.color.black));
                binding.tvLoginCode.setBackgroundResource(R.color.transparent);
                binding.tvLoginPassword.setBackgroundResource(R.drawable.login_top2);
                binding.etPassword.setHint("请输入验证码");
                binding.imgIsshow.setVisibility(View.GONE);
                if (!isshow) {
                    isshow = true;
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_pre);
                    binding.etPassword.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                }
                binding.linSave.setVisibility(View.GONE);
                binding.etPassword.setText("");
                break;
            case R.id.tv_login_password:
                isusernamelogin = true;
                binding.etMobile.setInputType(InputType.TYPE_CLASS_TEXT);
                binding.tvName.setText("用户名");
                iscode = false;
                if (isshow) {
                    isshow = false;
                    binding.imgIsshow.setImageResource(R.drawable.vision_track_watch_nor_gray);
                    binding.etPassword.setTransformationMethod(PasswordTransformationMethod.getInstance());
                }
                binding.tvCode.setVisibility(View.GONE);
                binding.imgIsshow.setVisibility(View.VISIBLE);
                binding.tvLoginCode.setTextColor(getResources().getColor(R.color.black));
                binding.tvLoginPassword.setTextColor(getResources().getColor(R.color.ijk_transparent_dark));
                binding.tvLoginCode.setBackgroundResource(R.drawable.login_top4);
                binding.tvLoginPassword.setBackgroundResource(R.color.transparent);
                binding.tvPassword.setText("密码");
                password = "password";
                binding.etPassword.setHint("请输入密码");
                binding.linSave.setVisibility(View.VISIBLE);
                if (!SharedPreferencesUtils.getParam(this, password, "").equals("")) {
                    binding.etPassword.setText(String.valueOf(SharedPreferencesUtils.getParam(this, password, "")));
                }

                break;
            case R.id.img_config:
                startActivity(new Intent(this, ConfigManagerActivity.class));
                break;
        }
    }

    /**
     * 填写的登录信息合法: 手机合法, 密码不为空
     *
     * @return boolean
     */
    private boolean checkParas() {
        String str_password = binding.etPassword.getText().toString();
        String str_mobile = binding.etMobile.getText().toString();
        if (!FormatUtil.is_reg_mobleNumber(str_mobile) && !isusernamelogin) {
            Toast.makeText(LoginMixActivity.this, "手机号码格式有误", Toast.LENGTH_SHORT).show();
            return false;
        }
        if (StringUtil.isEmpty(str_password) && !iscode) {
            Toast.makeText(LoginMixActivity.this, "密码输入不能为空", Toast.LENGTH_SHORT).show();
            return false;
        }
        return true;
    }

    private void login() {
        String str_password = binding.etPassword.getText().toString();
        String str_mobile = binding.etMobile.getText().toString();


        JSONObject data = new JSONObject();
        data.put("username", str_mobile);
        data.put("password", MD5.stringToMD5(str_password));
        Log.e("TAG", "name: "+data.toJSONString());

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = ApiConfig.INSTANCE.getMIX_URL() + "login";
        Request request = new Request.Builder().
                url(url).
                addHeader("ProductId",ApiConfig.INSTANCE.getPID()).
                post(requestBody)
                .build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ToastUtil.show("登录失败"+e.getLocalizedMessage());
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                //Log.e("TAG", "login onResponse: " + response.body().string());
                String result = response.body().string();
                Log.e("TAG", "login onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                        ToastUtil.show("登录成功");

                        JSONObject data = jsonObject.getJSONObject("data");
                        LoginCache loginCache = JSONObject.parseObject(data.toJSONString(), LoginCache.class);
                        loginCache.setTime(System.currentTimeMillis());
                        loginCache.setUserName(str_mobile);
                        loginCache.setPwd(str_password);

                        Log.e("TAG", "onSuccess token: " + loginCache.getToken());
                        Log.e("TAG", "user: " + loginCache.getUser());
                        Log.e("TAG", "adminFlag: " + loginCache.getAdminFlag());
                        SpUtil.setLoginCache(loginCache);
                        Event.post(loginCache); // 登录参数变化通知远程调用配置

                        // 根据是否勾选"保存密码"来决定是否保存密码
                        if (issave) {
                            // 保存用户名和密码到SharedPreferences
                            SharedPreferencesUtils.setParam(LoginMixActivity.this, "mobile", str_mobile);
                            SharedPreferencesUtils.setParam(LoginMixActivity.this, password, str_password);
                        } else {
                            // 如果未勾选"保存密码"，则清除已保存的密码
                            SharedPreferencesUtils.setParam(LoginMixActivity.this, "mobile", str_mobile);
                            SharedPreferencesUtils.setParam(LoginMixActivity.this, password, "");
                        }

                        SpUtil.setAPPName(Util.getAppName(getApplicationContext()));

                        // 登录成功后跳转到新的 activity
                        startActivity(new Intent(LoginMixActivity.this, HomeActivity.class));
                        finish();
                        //loginLG();
                    } else {
                        ToastUtil.show(jsonObject.getString("message"));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }


    /**
     * 解决在页面底部置输入框，输入法弹出遮挡部分输入框的问题
     *
     * @param root       页面根元素
     * @param editLayout 被软键盘遮挡的输入框editText
     * @param hasFocus   当前editText是否获取焦点
     */
    public static void keyboardLayout(final View root,
                                      final View editLayout, boolean hasFocus) {
        // TODO Auto-generated method stub
        root.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            // TODO Auto-generated method stub
            Rect rect = new Rect();
            //获取root在窗体的可视区域
            root.getWindowVisibleDisplayFrame(rect);
            //获取root在窗体的不可视区域高度(被其他View遮挡的区域高度)
            int rootInVisibleHeigh = root.getRootView().getHeight() - rect.bottom;
            //若不可视区域高度大于100，则键盘显示
            if (hasFocus && rootInVisibleHeigh > 100) {
//                Log.v("hjb", "不可视区域高度大于100，则键盘显示");
                int[] location = new int[2];
                //获取editLayout在窗体的坐标
                editLayout.getLocationInWindow(location);
                //计算root滚动高度，使editLayout在可见区域
                int srollHeight = (location[1] + editLayout.getHeight()) - rect.bottom;
                root.scrollTo(0, srollHeight);
            } else {
                //键盘隐藏
//                Log.v("hjb", "不可视区域高度小于100，则键盘隐藏");
                root.scrollTo(0, 0);
            }
        });
    }


}
