package dji.sampleV5.aircraft.page;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.R;
import dji.v5.utils.common.LogUtils;

class TaskViewHolder extends RecyclerView.ViewHolder {
    private final String TAG = LogUtils.getTag(this);

    public TaskViewHolder(@NonNull View itemView) {
        super(itemView);
    }
}

public class TaskAdapter extends RecyclerView.Adapter<TaskViewHolder> {
    private final String TAG = LogUtils.getTag(this);
    private final List<ListItemWrap> items;
    private final View.OnClickListener listener;

    public TaskAdapter(View.OnClickListener listener) {
        this.listener = listener;
        this.items = new ArrayList<>();
    }

    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.site_item, parent, false);
        view.setOnClickListener(listener);
        return new TaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        ListItemWrap item = items.get(position);
        Log.e(TAG, String.format("onBindViewHolder position:%d, item:%s", position, item));
        holder.itemView.setTag(item); // 同 onItemClick 方法组成一对

        // 渲染界面
        item.updateUI(holder);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    /**
     * 注入新的数据, 然后更新列表渲染
     *
     * @param next 从服务器端来的数据
     */
    public void loadData(List<ListItemWrap> next) {
        this.items.clear();
        this.items.addAll(next);
        notifyItemInserted(0);
    }
}
