package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.View;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.ToggleButton;

import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;


public class AircraftVisionAdvancePager extends BasePager implements CompoundButton.OnCheckedChangeListener {

    private LinearLayout linearLayout;
    private ToggleButton toggleVisionDown;
    private ToggleButton toggleVisionFlatCheck;
    private ToggleButton toggleVisionDownProtect;

    public AircraftVisionAdvancePager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading=true;
        tvTitle.setText(ContextUtil.getString(R.string.radar_obstacle_avoidance_advance_set));
        activityMenuPresenter.setIsPrevious(true);
        activityMenuPresenter.setPreviousName("RadarPager");
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.aircaft_radar_advance_pager, null);
        linearLayout = view.findViewById(R.id.ll_protection_land);
        toggleVisionDown = view.findViewById(R.id.toggle_vision_down);
        toggleVisionFlatCheck = view.findViewById(R.id.toggle_vision_flatcheck);
        toggleVisionDownProtect = view.findViewById(R.id.toggle_vision_down_protect);
        toggleVisionFlatCheck.setOnCheckedChangeListener(this);
        toggleVisionDown.setOnCheckedChangeListener(this);
        toggleVisionDownProtect.setOnCheckedChangeListener(this);

        getVisionAssistedPosition();
        flContainer.addView(view);
    }

    private void getVisionAssistedPosition() {
        /*KeyListener<Boolean> vision_assisted_positioningListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now != null) {
                    ContextUtil.getHandler().post(() -> toggleVisionFlatCheck.setChecked(now));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.ACTIVE_OBSTACLE_AVOIDANCE_ENABLED), vision_assisted_positioningListener);*/
    }

    private void getVisionAssistedPositioning() {

       /* FlightController flightController = DJIHelper.getInstance().getFlightController();
        if (flightController != null){
            FlightAssistant flightAssistant = flightController.getFlightAssistant();
            if (flightAssistant != null){
                flightAssistant.getVisionAssistedPositioningEnabled(new CommonCallbacks.CompletionCallbackWith<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        final boolean b=aBoolean;
                        ContextUtil.getHandler().post(() -> {
                            toggleVisionDown.setChecked(b);
                            if (b){
                                linearLayout.setVisibility(View.VISIBLE);
                            }else {
                                linearLayout.setVisibility(View.GONE);
                            }
                        });
                    }

                    @Override
                    public void onFailure(DJIError djiError) {

                    }
                });
            }
        }*/
    }

    private void getVisionDowProtect() {
        /*FlightController flightController = DJIHelper.getInstance().getFlightController();
        if (flightController != null){
            FlightAssistant flightAssistant = flightController.getFlightAssistant();
            if (flightAssistant != null){
                flightAssistant.getLandingProtectionEnabled(new CommonCallbacks.CompletionCallbackWith<Boolean>() {
                    @Override
                    public void onSuccess(Boolean aBoolean) {
                        final boolean b=aBoolean;
                        ContextUtil.getHandler().post(() -> toggleVisionDownProtect.setChecked(b));
                    }

                    @Override
                    public void onFailure(DJIError djiError) {

                    }
                });
            }
        }*/


    }

    @Override
    public void removeListener() {
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading){
            if (connect){
                toggleVisionFlatCheck.setVisibility(View.VISIBLE);
                toggleVisionDown.setVisibility(View.VISIBLE);
                toggleVisionDownProtect.setVisibility(View.VISIBLE);
                getVisionDowProtect();
                getVisionAssistedPositioning();
            }else {
                toggleVisionFlatCheck.setVisibility(View.GONE);
                toggleVisionDown.setVisibility(View.GONE);
                toggleVisionDownProtect.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            switch (buttonView.getId()) {
            case R.id.toggle_vision_down:
                //视觉向下定位
                if (isChecked){
                    linearLayout.setVisibility(View.VISIBLE);
                }else {
                    linearLayout.setVisibility(View.GONE);
                }
                setVisionAssistedPositioning(isChecked);
                break;
            case R.id.toggle_vision_flatcheck:
            //返航障碍物检测
                setVisionAssistedPosition(isChecked);
                break;
            case R.id.toggle_vision_down_protect:
            //降落保护
                setVisionDowProtect(isChecked);
                break;
            }

    }

    private void setVisionAssistedPosition(final boolean isChecked) {
       /* KeyManager.getInstance().setValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.ACTIVE_OBSTACLE_AVOIDANCE_ENABLED), isChecked, new YNListener0() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    private void setVisionDowProtect(boolean b) {

       /* FlightController flightController = DJIHelper.getInstance().getFlightController();
        if (flightController != null){
            FlightAssistant flightAssistant = flightController.getFlightAssistant();
            if (flightAssistant != null){
                flightAssistant.setLandingProtectionEnabled(b, djiError -> {

                });
            }
        }*/
    }

    private void setVisionAssistedPositioning(boolean b) {
       /* FlightController flightController = DJIHelper.getInstance().getFlightController();
        if (flightController != null){
            FlightAssistant flightAssistant = flightController.getFlightAssistant();
            if (flightAssistant != null){
                flightAssistant.setVisionAssistedPositioningEnabled(b, djiError -> {

                });
            }
        }*/
    }
}
