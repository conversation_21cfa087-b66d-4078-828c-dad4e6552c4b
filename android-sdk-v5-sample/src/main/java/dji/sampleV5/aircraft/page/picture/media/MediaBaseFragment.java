package dji.sampleV5.aircraft.page.picture.media;

import android.view.View;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.v5.manager.datacenter.media.MediaManager;

public class MediaBaseFragment {

    View rootView;
    MediaManager mediaManager;
    FrameLayout flContentMedia;
    RelativeLayout downloadLL;
    TextView downloadTV;
    TextView downPercent;
    ProgressBar downloadPB;

    public Boolean isLoading = true;

    MediaBaseFragment() {
        initView();

        mediaManager = MediaManager.getInstance();
    }

    private void initView() {
        rootView = View.inflate(ContextUtil.getApplicationContext(), R.layout.media_base_fragment, null);
        flContentMedia = rootView.findViewById(R.id.fl_content_media);
        downloadLL = rootView.findViewById(R.id.downloadLL);
        downloadTV = rootView.findViewById(R.id.download_text);
        downPercent = rootView.findViewById(R.id.down_percent);
        downloadPB = rootView.findViewById(R.id.downloadProgressBar);
    }

    public void initData() {}
}
