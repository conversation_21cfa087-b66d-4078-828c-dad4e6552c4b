package dji.sampleV5.aircraft.page.fly.Task;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.MixTaskList;
import dji.sampleV5.aircraft.data.task.TaskInfo;
import dji.sampleV5.aircraft.databinding.ActivityTaskListBinding;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.view.adapter.TaskAdapter;
import dji.sampleV5.aircraft.view.pullToRefresh.PullToRefreshBase;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Describe
 */
public class TaskHistoryListActivity extends AppCompatActivity implements CancelAdapt {

    private ActivityTaskListBinding binding;
    private int page = 1, size = 10;
    private List<TaskInfo> list = new ArrayList<>();
    private TaskAdapter taskAdapter;
    private final String MISSIONBATCH = "MISSIONBATCH";
    private final String SITEID = "SITEID";
    private LoginCache loginCache;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_task_list);
        ImmerseUtil.fullScreen(this);
        loginCache = SpUtil.getLoginCache();
        initView();
        getHistoryList();
    }

    private void initView() {
        taskAdapter = new TaskAdapter(this, list);
        binding.lvTaskHistory.setAdapter(taskAdapter);
        binding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                TaskHistoryListActivity.this.finish();
            }
        });
        binding.lvTaskHistory.setOnLastItemVisibleListener(new PullToRefreshBase.OnLastItemVisibleListener() {
            @Override
            public void onLastItemVisible() {
                page++;
                getHistoryList();
            }
        });
        binding.lvTaskHistory.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                Intent intent = new Intent(TaskHistoryListActivity.this, TaskHistoryInfoActivity.class);
                intent.putExtra(MISSIONBATCH, list.get(i - 1).getMissionBatch());
                intent.putExtra(SITEID, list.get(i - 1).getSiteID());
                startActivity(intent);
            }
        });

        binding.lvTaskHistory.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                showDeleteDialog(position - 1);
                return true;
            }
        });
    }

    private void showDeleteDialog(int position){
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        AlertDialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(true);
        dialog.setCancelable(false);

        View view = View.inflate(this, R.layout.alert_dialog, null);

        //标题
        TextView tvTitle = view.findViewById(R.id.tv_alert_title);
        //内容
        TextView tvContent = view.findViewById(R.id.tv_alert_content);
        //取消按钮
        Button buttonCancle = view.findViewById(R.id.btn_alert_cancel);
        //确定按钮
        Button buttonOk = view.findViewById(R.id.btn_alert_ok);
        //线
        View viewLine = view.findViewById(R.id.v_alert_line);
        tvContent.setText("是否确认删除该条飞行历史\n"+list.get(position).getMissionName());

        buttonCancle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        buttonOk.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                deleteHistory(position);
                dialog.dismiss();
            }
        });
        //设置背景透明,去四个角
        dialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        dialog.show();
        dialog.getWindow().setLayout(DensityUtil.dip2px(this, 290), LinearLayout.LayoutParams.WRAP_CONTENT);
//        dialog.getWindow().setWindowAnimations(R.style.AnimMM);
        dialog.setContentView(view);
    }

    private void deleteHistory(int position){
        TaskInfo taskInfo = list.get(position);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(taskInfo.getMissionBatch());

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, jsonArray.toJSONString());
        String url = ApiConfig.INSTANCE.getMIX_URL() + ApiConfig.DELETE_HISTORY;
        Request request = new Request.Builder().
                url(url).
                addHeader("ApiToken",loginCache.getApiToken()).
                addHeader("ProductId",ApiConfig.INSTANCE.getPID()).
                post(requestBody)
                .build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                ToastUtil.show("删除失败:"+e.getLocalizedMessage());
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("TAG", "history onResponse: " + result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                    ToastUtil.show("删除成功");
                    list.remove(position);
                    runOnUiThread(() -> taskAdapter.notifyDataSetChanged());

                }else {
                    ToastUtil.show(jsonObject.getString("message"));
                }

            }
        });
    }

    private void getHistoryList(){
        JSONObject data = new JSONObject();
        data.put("page", page);
        data.put("size", 10);

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = ApiConfig.INSTANCE.getMIX_URL() + ApiConfig.HISTORY_LIST;
        Request request = new Request.Builder().
                url(url).
                addHeader("ApiToken",loginCache.getApiToken()).
                addHeader("ProductId",ApiConfig.INSTANCE.getPID()).
                post(requestBody)
                .build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("TAG", "history onResponse: " + result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                    try {
                        MixTaskList pageData = new Gson().fromJson(result, MixTaskList.class);
                        List<TaskInfo> taskInfoList = pageData.getList();
                        list.addAll(taskInfoList);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                taskAdapter.notifyDataSetChanged();
                            }
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }else {
                    ToastUtil.show(jsonObject.getString("message"));
                }

            }
        });
    }

   /* private void initHttp() {
        //获取飞行历史列表
        String url = NetConfig.YG_FORMAL + Api.getTaskList;
        Map<String, String> map = new HashMap<>();
        map.put("page", page + "");
        map.put("size", "10");
        HttpUtil.getInstance().get(this, url, map, "qicloudToken", new HttpUtil.ICallBack() {
            @Override
            public void onResponse(JsonResult jsonResult) {
                TaskList pageData = new Gson().fromJson(jsonResult.getData().toString(), TaskList.class);
                List<TaskInfo> taskInfoList = pageData.getList();
                list.addAll(taskInfoList);
                taskAdapter.notifyDataSetChanged();
            }
        });
    }*/


}
