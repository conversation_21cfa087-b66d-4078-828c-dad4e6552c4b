package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;

import dji.sampleV5.aircraft.databinding.MenuBatteryPagerAdvancedSetBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;


/**
 * <AUTHOR>
 */
public class BatteryAdvancedSetPager extends BasePager {

    private BatteryAdvancedSet advancedSet;

    public BatteryAdvancedSetPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading=true;
        tvTitle.setText(ContextUtil.getString(R.string.battery_set_pager));
        activityMenuPresenter.setIsPrevious(true);
        activityMenuPresenter.setPreviousName("BatteryPager");

        MenuBatteryPagerAdvancedSetBinding binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_battery_pager_advanced_set, null, false);
        binding.setActivityMenuPresenter(activityMenuPresenter);
        advancedSet = new BatteryAdvancedSet(binding, (DefaultLayoutActivity)fragment.getActivity());
        advancedSet.setBatteryAdvanced();
        flContainer.addView(binding.getRoot());
    }

    @Override
    public void removeListener() {

    }

    @Override
    public void isConnect(boolean connect) {
        if (advancedSet!=null){
            advancedSet.setConnect(connect);
        }
    }
}
