package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.View;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.ToggleButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.AircaftRadarPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.perception.PerceptionManager;


public class AircaftRadarSenser implements CompoundButton.OnCheckedChangeListener {
    private final ToggleButton toggleObstacleAvoidance;
    private final ToggleButton toggleBupwards;
    private final RelativeLayout radarObstacleUpwards;
    /*private KeyListener<Boolean> collision_avoidance_enableListener;
    private KeyListener<Boolean> upwards_avoidance_enabledListener;*/


    public AircaftRadarSenser(AircaftRadarPagerBinding inflate) {
        ToggleButton toggleShowRadar = inflate.toggleShowRadar;
        toggleObstacleAvoidance = inflate.toggleObstacleAvoidance;
        toggleBupwards = inflate.toggleRadarObstacleUpwards;
        radarObstacleUpwards = inflate.rlRadarObstacleUpwards;
        TextView tvRadarObstacleAvoidance = inflate.tvRadarObstacleAvoidance;
        toggleBupwards.setOnCheckedChangeListener(this);
        toggleObstacleAvoidance.setOnCheckedChangeListener(this);

        toggleShowRadar.setOnCheckedChangeListener(this);
        boolean radarUIShow = SpUtil.getRadarUIShow();
        toggleShowRadar.setChecked(radarUIShow);
        getCollisionAvoidance();
    }

    private void getUpwardAvoidance() {
        /*upwards_avoidance_enabledListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now!=null){
                    radarObstacleUpwards.setVisibility(View.VISIBLE);
                    ContextUtil.getHandler().post(() -> toggleBupwards.setChecked(now));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.UPWARDS_AVOIDANCE_ENABLED), upwards_avoidance_enabledListener);*/
    }
    private void setUpwardAvoidance(boolean ischeck){
      /*  KeyManager.getInstance().setValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.UPWARDS_AVOIDANCE_ENABLED), ischeck, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }


    private void getCollisionAvoidance() {
        PerceptionManager.getInstance().getOverallObstacleAvoidanceEnabled(new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
                ContextUtil.getHandler().post(() -> toggleObstacleAvoidance.setChecked(aBoolean));
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if(error != null){
                    ToastUtil.show(error.description());
                }
            }
        });

       /* collision_avoidance_enableListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now != null) {
                    ContextUtil.getHandler().post(() -> toggleObstacleAvoidance.setChecked(now));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.COLLISION_AVOIDANCE_ENABLED), collision_avoidance_enableListener);*/
    }

    public void setConnect(boolean connect) {
        radarObstacleUpwards.setVisibility(View.GONE);
        if (connect){
            //获得顶部避障
            getUpwardAvoidance();
            //獲得感知系统防撞状态
            getCollisionAvoidance();
        }else {
            remove();
        }
        toggleObstacleAvoidance.setEnabled(connect);
        toggleBupwards.setEnabled(connect);
    }


    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.toggle_obstacleAvoidance:
                setCollisionAvoidance(isChecked);
                break;
            case R.id.toggle_Radar_obstacleUpwards:
                setUpwardAvoidance(isChecked);
                break;
            case R.id.toggle_show_radar:
                SpUtil.setRadarUIShow(isChecked);
                //((AircraftActivity) ContextUtil.getCurrentActivity()).updateRadarView();
                break;
        }
    }
//设置感知系统防撞状态
    private void setCollisionAvoidance(final boolean isChecked) {
        PerceptionManager.getInstance().setOverallObstacleAvoidanceEnabled(isChecked, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if(error != null){
                    ToastUtil.show(error.description());
                }
            }
        });
       /* KeyManager.getInstance().setValue(FlightControllerKey.createFlightAssistantKey(FlightControllerKey.COLLISION_AVOIDANCE_ENABLED), isChecked, new YNListener0() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onException(Throwable e) {
            }
        });*/
    }

    public void remove() {
       /* KeyManager.getInstance().removeListener(collision_avoidance_enableListener);
        KeyManager.getInstance().removeListener(upwards_avoidance_enabledListener);*/
    }
}
