package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.os.Environment;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.MenuSetingSetPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.camera.CameraWatermarkSettings;
import dji.sdk.keyvalue.value.camera.WatermarkDisplayContentSettings;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Locale;


public class GeneralSettingPager extends BasePager {
    MenuSetingSetPagerBinding binding;
    private boolean isInit = false;

    public GeneralSettingPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {
        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.common_setting));
        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_seting_set_pager, null, false);

        flContainer.addView(binding.getRoot());

        binding.switchRecord.setChecked(SpUtil.getDeviceRecorder());
        binding.switchRecord.setOnCheckedChangeListener((compoundButton, b) -> SpUtil.setDeviceRecorder(b));

        binding.btnExport.setOnClickListener(view -> exportRecorder());


        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraWatermarkSettings), new CommonCallbacks.CompletionCallbackWithParam<CameraWatermarkSettings>() {
            @Override
            public void onSuccess(CameraWatermarkSettings cameraWatermarkSettings) {
                binding.watermarkTb.setChecked(cameraWatermarkSettings.getEnabledForVideos());
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("WatermarkSettings" + error.description());
            }
        });


        binding.watermarkTb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Log.e("onCheckedChanged", "onCheckedChanged: " + isChecked);
                KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyCameraWatermarkSettings), new CameraWatermarkSettings(isChecked, isChecked, isChecked), new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        ToastUtil.show("设置成功");


                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("WatermarkSettings" + error.description());
                    }
                });

/*if(isChecked){
                                WatermarkDisplayContentSettings contentSettings = new WatermarkDisplayContentSettings();
                                contentSettings.setDateTimeEnabled(true);
                                contentSettings.setGpsLonLatEnabled(true);
                                KeyManager.getInstance().setValue(KeyTools.createKey(CameraKey.KeyWatermarkDisplayContentSettings), contentSettings, new CommonCallbacks.CompletionCallback() {
                                    @Override
                                    public void onSuccess() {
                                        ToastUtil.show("设置成功2");
                                    }

                                    @Override
                                    public void onFailure(@NonNull IDJIError error) {
                                        ToastUtil.show("ContentSettings" + error.description());
                                    }
                                });
                            }*/

                isInit = true;
            }
        });

    }

    @Override
    public void removeListener() {
    }


    private void exportRecorder() {
       /* SubstationTable table = new SubstationTable(ContextUtil.getApplicationContext());
        List<SubstationRecorder> list = table.getAll();

        String data = JsonUtil.toJson(list);
        boolean rst = save2File(data);
        if (rst){
            table.deleteAll();
            SpUtil.setRecorderIndex(1);
        }*/
    }

    private boolean save2File(String data) {

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss", Locale.CHINA);
        String filePath = Environment.getExternalStorageDirectory().toString() + File.separator + sf.format(System.currentTimeMillis()) + "_recorder.txt";

        try {
            File file = new File(filePath);
            if (!file.exists()) {
                File dir = new File(file.getParent());
                dir.mkdirs();
                file.createNewFile();
            }

            FileOutputStream outStream = new FileOutputStream(file);
            outStream.write(data.getBytes());
            outStream.close();
            ToastUtil.show("保存成功，保存路径：" + filePath);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
