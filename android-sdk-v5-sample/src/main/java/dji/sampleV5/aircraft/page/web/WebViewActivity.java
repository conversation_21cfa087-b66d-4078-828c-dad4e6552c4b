package dji.sampleV5.aircraft.page.web;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import com.github.lzyzsd.jsbridge.BridgeHandler;
import com.github.lzyzsd.jsbridge.CallBackFunction;
import com.github.lzyzsd.jsbridge.DefaultHandler;
import com.otaliastudios.cameraview.BitmapCallback;
import com.otaliastudios.cameraview.PictureResult;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.WebviewActivityBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.lbs.LocationInfo;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


/*
public class WebViewActivity extends Activity implements CancelAdapt {
    private WebviewActivityBinding binding;
    private String currentData;
    private Boolean formLogin = true;
    private Boolean isFirst = true;
    private String lon,lat;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ImmerseUtil.startImmerse(this);
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.webview_activity);

        Event.register(this);

        WebSettings webSettings = binding.webview.getSettings();
        //如果访问的页面中要与Javascript交互，则webview必须设置支持Javascript
        webSettings.setJavaScriptEnabled(true);
        //设置自适应屏幕，两者合用
        webSettings.setUseWideViewPort(true); //将图片调整到适合webview的大小
        webSettings.setLoadWithOverviewMode(true); // 缩放至屏幕的大小
        //缩放操作
        webSettings.setSupportZoom(false); //支持缩放，默认为true。是下面那个的前提。
        webSettings.setBuiltInZoomControls(false); //设置内置的缩放控件。若为false，则该WebView不可缩放
        webSettings.setDisplayZoomControls(false); //隐藏原生的缩放控件
        //其他细节操作
        webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        webSettings.setSupportMultipleWindows(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setPluginState(WebSettings.PluginState.ON_DEMAND);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE); //关闭webview中缓存
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccess(true); //设置可以访问文件
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true); //支持通过JS打开新窗口
        webSettings.setLoadsImagesAutomatically(true); //支持自动加载图片
        webSettings.setDefaultTextEncodingName("utf-8");//设置编码格式
        syncCookie();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            CookieManager.getInstance().setAcceptThirdPartyCookies(binding.webview, true);


       */
/* binding.webview.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.e("TAG", "shouldOverrideUrlLoading!!!!: "+url );
                return true;
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                Log.e("TAG", "shouldOverrideUrlLoading222: " + request.getUrl().toString());
                view.loadUrl(request.getUrl().toString());
                return true;
            }

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.e("TAG", "onPageStarted: "+url);
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.e("TAG", "onPageFinished: "+url);
            }
        });*//*


        formLogin = getIntent().getBooleanExtra("FromLogin", true);
        binding.webview.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Log.e("TAG", "onProgressChanged: " + newProgress);
                if (newProgress == 100 && isFirst) {
                    Log.e("TAG", "加载完成: " + newProgress);
                    MapServiceFactory.getLocationServiceInstance().startPeriodLocation();
                    binding.root.setVisibility(View.VISIBLE);
                    binding.back.getRoot().setVisibility(View.INVISIBLE);
                    if (!formLogin) { //如果是单独嵌套航线规划需要告诉web端隐藏上侧和左侧列表
                        LoginCache loginCache = SpUtil.getLoginCache();
                        JSONObject jsonObject = new JSONObject();
                        try {
                            jsonObject.put("data", loginCache.getData());
                            jsonObject.put("info", "FromLogin");
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        binding.webview.callHandler("functionInJs", jsonObject.toString(), new CallBackFunction() {
                            @Override
                            public void onCallBack(String data) {
                                Log.e("TAG", "onCallBack:" + data);
                                Toast.makeText(WebViewActivity.this, data, Toast.LENGTH_LONG).show();
                            }
                        });
                    }
                    isFirst = false;
                }
            }

            @Override
            public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
                Log.e("TAG", "onJsAlert: url = " + url + "  message = " + message);
                return super.onJsAlert(view, url, message, result);
            }

            @Override
            public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
                Log.e("TAG", "onJsAlert: url = " + url + "  message = " + message);
                return super.onJsPrompt(view, url, message, defaultValue, result);
            }
        });

        if (formLogin) {
            binding.webview.loadUrl(NetConfig.WebUrl + "home/defect?userName=" + SpUtil.getLoginCache().getUserName()+"&isAndroid=true");
            //binding.webview.loadUrl("http://192.168.3.18:8085/" + "home/defect?userName=" + SpUtil.getLoginCache().getUserName()+"&isAndroid=true");
        } else {
            int type = getIntent().getIntExtra("type", 0);
            switch (type) {
                case 1:
                    //binding.webview.loadUrl("http://192.168.3.20:9650/plan/arealist");//苏宁
                    //binding.webview.loadUrl("http://192.168.3.8:9650/plan/arealist");//朱莉
                    binding.webview.loadUrl(NetConfig.WebUrl + "plan/arealist");
                    break;
                case 2:
                    binding.webview.loadUrl(NetConfig.WebUrl + "plan/linelist");
                    //binding.webview.loadUrl("http://192.168.3.8:9650/plan/linelist");//朱莉
                    //binding.webview.loadUrl("http://192.168.3.20:9650/plan/linelist");//苏宁

                    break;
            }
        }

        //binding.webview.loadUrl("https://qicloud.skysys.cn/#/login");
        //binding.webview.loadUrl("http://124.70.158.88:8080/plan/area/");
        //binding.webview.loadUrl("file:///android_asset/test.html");
        //binding.webview.loadUrl("http://192.168.3.20:9650/home/<USER>");
//      注册监听方法当js中调用callHandler方法时会调用此方法（handlerName必须和js中相同）
        binding.webview.registerHandler("submitFromWeb", new BridgeHandler() {
            @Override
            public void handler(String data, CallBackFunction function) {
                Log.e("TAG", "submitFromWeb js返回：" + data);
                //Toast.makeText(WebViewActivity.this, "js返回:" + data, Toast.LENGTH_LONG).show();

                if (data.contains("subMission")) {
                   */
/* Intent intent = new Intent(WebViewActivity.this, AircraftActivity.class);
                    intent.putExtra("subMission", data);
                    startActivity(intent);
                    finish();*//*

                } else if (data.contains("lnglat")) {
                    try {
                        JSONObject jsonObject = new JSONObject(data);
                        JSONArray lnglat = jsonObject.getJSONArray("lnglat");
                        lon = lnglat.getString(0);
                        lat = lnglat.getString(1);
                        Log.e("TAG", "handler: " + lnglat.getString(0));

                        //binding.mapChoose.setVisibility(View.VISIBLE);

                        */
/*AlertDialog.Builder builder = new AlertDialog.Builder(WebViewActivity.this);
                        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.map_choose, null);
                        RelativeLayout gaode = view.findViewById(R.id.rl_gaode);
                        RelativeLayout baidu = view.findViewById(R.id.rl_baidu);
                        gaode.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                Util.toGaode(WebViewActivity.this, lat, lon);
                            }
                        });
                        baidu.setOnClickListener(v -> Util.toBaidu(WebViewActivity.this, lat, lon));
                        builder.setView(view);
                        builder.setCancelable(false);
                        Dialog mDialog = builder.create();
                        //mDialog.getWindow().setLayout(200, 183);
                        mDialog.setCanceledOnTouchOutside(true);

                        ImmerseUtil.showDialog(mDialog);
                        DisplayMetrics dm = getResources().getDisplayMetrics();
                        int width = dm.widthPixels;
                        int height = dm.heightPixels;
                        android.view.WindowManager.LayoutParams params = mDialog.getWindow().getAttributes();
                        params.width = (int) (width * 0.4);
                        params.height = (int) (height * 0.5);

                        mDialog.getWindow().setAttributes(params);*//*

                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                } else {
                    currentData = data;
                    Log.e("", "前端返回的缺陷数据: "+currentData);
                    DJIAircraftApplication.getInstance().setPictureResult(null);
                    startActivityForResult(new Intent(WebViewActivity.this, CameraActivity.class), 1);
                }


                //显示js传递给Android的消息
                //Toast.makeText(WebViewActivity.this, "我是js调用Android返回数据:" + data, Toast.LENGTH_LONG).show();
                //Android返回给JS的消息
                //function.onCallBack("我是js调用Android返回数据：" + binding.et.getText().toString());

            }
        });
        binding.bt.setOnClickListener(view -> {
//              调用js中的方法（必须和js中的handlerName想同）
            binding.webview.callHandler("functionInJs", "complete", new CallBackFunction() {
                @Override
                public void onCallBack(String data) {
                    Log.e("TAG", "onCallBack:" + data);
                    Toast.makeText(WebViewActivity.this, data, Toast.LENGTH_LONG).show();
                }
            });
        });
        binding.webview.setDefaultHandler(new DefaultHandler() {
            @Override
            public void handler(String data, CallBackFunction function) {
                Toast.makeText(WebViewActivity.this, data, Toast.LENGTH_SHORT).show();
                function.onCallBack("Android收到了默认的消息");
            }
        });
        binding.bt2.setOnClickListener(view -> {
//              发送信息给js,此处不需要配置handlerName
            binding.webview.send(binding.et.getText().toString().trim(), new CallBackFunction() {
                @Override
                public void onCallBack(String data) {
//                      接收js的回调数据
                    Toast.makeText(WebViewActivity.this, data, Toast.LENGTH_SHORT).show();
                }
            });
        });
        binding.rlBaidu.setOnClickListener(v -> {
            Util.toBaidu(WebViewActivity.this, lat, lon);
        });

        binding.rlGaode.setOnClickListener(v -> {
            Util.toGaode(WebViewActivity.this, lat, lon);
        });

        binding.close.setOnClickListener(v -> {
            binding.mapChoose.setVisibility(View.GONE);
        });
    }

    private void syncCookie() {
        CookieSyncManager.createInstance(this);
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setAcceptCookie(true);
        CookieSyncManager.getInstance().sync();
    }

    @Override
    protected void onResume() {
        super.onResume();
        binding.webview.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        binding.webview.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Event.unregister(this);
        MapServiceFactory.getLocationServiceInstance().stopLocation();
        binding.webview.loadDataWithBaseURL(null, "", "text/html", "utf-8", null);
        binding.webview.clearHistory();
        binding.root.removeView(binding.webview);
        binding.webview.destroy();
    }

    @Subscribe
    public void onLocationChangedEvent(LocationInfo info) {
        Log.e("TAG", "定位成功: ");
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("type","appLocation");
            jsonObject.put("lng",info.getLongitude());
            jsonObject.put("lat",info.getLatitude());
        } catch (JSONException e) {
            e.printStackTrace();
        }

        binding.webview.callHandler("functionInJs", jsonObject.toString(), new CallBackFunction() {
            @Override
            public void onCallBack(String data) {
                Log.e("TAG", "onCallBack:" + data);
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        */
/*PictureResult result = DJIAircraftApplication.getInstance().getPictureResult();
        //Log.e("TAG", "onCallBack: result = " + result.getData().length);
        if (requestCode == 1 && result != null) {
            try {
                result.toBitmap(new BitmapCallback() {
                    @Override
                    public void onBitmapReady(@Nullable Bitmap bitmap) {
                        try {
                            JSONObject jsonObject = new JSONObject(currentData);
                            String token = jsonObject.getString("token");
                            JSONObject defectInfo = jsonObject.getJSONObject("defectInfo");
                            //String defect = defectInfo.getString("type");
                            String missionbatch = defectInfo.getString("missionBatch");
                            String StrID = defectInfo.getString("id");
                            String filename = "xiaoque";
                            String user = SpUtil.getLoginCache().getUser();
                            String path = saveBitmap(bitmap, "test");
                            Log.e("TAG", "onBitmapReady: token =" + token  + "  missionbatch=" + missionbatch + "  StrID=" + StrID + "   filename" + filename + "   user" + user);

                            File file = new File(path);
                            OkHttpClient httpClient = new OkHttpClient();
                            MediaType mediaType = MediaType.parse("image/*");
                            RequestBody requestBody = RequestBody.create(mediaType, file);//把文件与类型放入请求体

                            MultipartBody multipartBody = new MultipartBody.Builder()
                                    .setType(MultipartBody.FORM)
                                    .addFormDataPart("file", file.getName(), requestBody)//文件名
                                    .build();
                            String url = NetConfig.LGURl3 + "defectImmediate/" + missionbatch + "/" + StrID + "/remove";
                            //String url = NetConfig.LGURl2 + "AI/Photovo/Delete/Defect/" + defect + "/" + missionbatch + "/" + StrID + "/" + user;
                            url = url.replace("#", "%23");
                            Log.e("TAG", "onBitmapReady: " + url);
                            Request request = new Request.Builder()
                                    .addHeader("token", token)
                                    .url(url)
                                    .post(multipartBody)
                                    .build();
                            Call call = httpClient.newCall(request);
                            call.enqueue(new Callback() {
                                @Override
                                public void onFailure(Call call, IOException e) {
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            ToastUtil.show("上传失败" + e.getLocalizedMessage());
                                        }
                                    });
                                    Log.e("TAG", "onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
                                }

                                @Override
                                public void onResponse(Call call, Response response) throws IOException {
                                    //在这里根据返回内容执行具体的操作
                                    final String resdata = response.body().string();
                                    Log.e("TAG", "消缺: " + resdata);
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            try {
                                                JSONObject json = new JSONObject(resdata);
                                                binding.webview.callHandler("functionInJs", json.getJSONObject("data").toString(), new CallBackFunction() {
                                                    @Override
                                                    public void onCallBack(String data) {
                                                        Log.e("TAG", "onCallBack:" + data);
                                                    }
                                                });
                                            } catch (JSONException e) {
                                                e.printStackTrace();
                                            }
                                        }
                                    });

                                    Log.e("TAG", "onResponse: " + call.toString() + "  response:" + resdata);
                                }
                            });
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }

                    }
                });
            } catch (Exception e) {
                ToastUtil.show("Can't preview this format: " + result.getFormat());
            }
        }*//*

    }

    private String saveBitmap(Bitmap bitmap, String filename) {
        String savePath = Environment.getExternalStorageDirectory().getPath() + "/DCIM/SKYSYS/" + filename + ".jpg";
        File f = new File(savePath);
        if (f.exists()) f.delete();

        File fileParent = f.getParentFile();
        if (!fileParent.exists()) {
            fileParent.mkdirs();
        }

        try {
            if (f.createNewFile()) {
                FileOutputStream out = new FileOutputStream(f);
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
                out.flush();
                out.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (bitmap != null) {
                bitmap.recycle();
            }
        }
        return savePath;
    }

    @Override
    public void onBackPressed() {
        //super.onBackPressed();
        */
/*MAlertDialogUtil alertDialogUtil = new MAlertDialogUtil();
        alertDialogUtil.showDialog(this, "提示", "确定退出？", "确定", "取消", true, true, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

            }
        }, (dialog, which) -> {
            finish();
        });*//*


        if (formLogin) {
            finish();
        } else {
            if (binding.webview.canGoBack()) {
                //isFirst = true;
                binding.webview.goBack();
                binding.webview.callHandler("functionInJs", "FromLogin", new CallBackFunction() {
                    @Override
                    public void onCallBack(String data) {
                        Log.e("TAG", "onCallBack:" + data);
                        //Toast.makeText(WebViewActivity.this, data, Toast.LENGTH_LONG).show();
                    }
                });
            } else {
                finish();
            }
        }
    }
}
*/
