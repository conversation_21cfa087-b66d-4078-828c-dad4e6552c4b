package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.View;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuBatteryPagerAdvancedSetBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.view.adapter.EasyAdapter;
import dji.sampleV5.aircraft.view.adapter.EasyHolder;

import java.util.ArrayList;


class BatteryAdvancedSet {
    private final DefaultLayoutActivity mActivity;
    private MenuBatteryPagerAdvancedSetBinding binding;
    private ListView mListView;
    private final ActivityMenuPresenter activityMenuPresenter;
    private  RelativeLayout mrlReleaseTime;
    private final TextView mtvReleaseTime;
    private final String day1;
    private ArrayList<String> daylist =new ArrayList<>();
    private int mposition;
    private EasyAdapter easyAdapter;

    public BatteryAdvancedSet(MenuBatteryPagerAdvancedSetBinding binding, DefaultLayoutActivity mActivity) {
        this.binding=binding;
        this.mActivity=mActivity;
        mrlReleaseTime = binding.rlReleaseTime;
        mtvReleaseTime = binding.tvReleaseTime;
        activityMenuPresenter = binding.getActivityMenuPresenter();
        day1 = ContextUtil.getString(R.string.day);
        for (int i = 1; i <11 ; i++) {
            daylist.add(i+day1);
        }
    }

    public void setBatteryAdvanced() {
        //获得电池状态
       /* KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.NUMBER_OF_DISCHARGES), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                ContextUtil.getHandler().post(() -> activityMenuPresenter.setCyclikTime(value+""));
            }

            @Override
            public void onException(Throwable e) {

            }
        });

        //获得序列号
        KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.SERIAL_NUMBER), new YNListener1<String>() {
            @Override
            public void onSuccess(final String value) {
                ContextUtil.getHandler().post(() -> binding.getActivityMenuPresenter().setBatteryserialnumber(value));
            }

            @Override
            public void onException(Throwable e) {

            }
        });
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        //获得天数
        getday();*/
    }
    //获得天数
    private void getday() {
        /*KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.SELF_DISCHARGE_IN_DAYS), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                mposition=value-1;
                ContextUtil.getHandler().post(() -> activityMenuPresenter.setBatteryReleaseTime(value+day1));
                //设置天数
                setDay(value-1);
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    //设置天数
    private void setDay(int i) {
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), daylist) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {

                    private TextView tv_center_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_center_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (mposition == position) {
                            tv_center_item.setTextColor(ContextUtil.getApplicationContext().getResources().getColor(R.color.colorBright));
                        } else {
                            tv_center_item.setTextColor(ContextUtil.getApplicationContext().getResources().getColor(R.color.white));
                        }
                        String itemdate = (String) item;
                        tv_center_item.setText(itemdate);
                    }
                };
            }
        };
        mListView.setAdapter(easyAdapter);
        mListView.setSelection(i-1);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            mposition=position;
            easyAdapter.notifyDataSetChanged();
            //设置放电天数
           /* KeyManager.getInstance().setValue(BatteryKey.create(BatteryKey.SELF_DISCHARGE_IN_DAYS), position+1, new YNListener0() {
                @Override
                public void onSuccess() {
                    ContextUtil.getHandler().post(() -> {
                        activityMenuPresenter.setBatteryReleaseTime(position + 1 + day1);
                        ((AircraftActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
                    });
                }

                @Override
                public void onException(Throwable e) {

                }
            });*/
        });
        //点击弹出窗口
       // mrlReleaseTime.setOnClickListener(v -> mActivity.showPopup(mtvReleaseTime,mListView,daylist.size()));
    }

    public void setConnect(Boolean connect) {
        if (connect){
            binding.rlReleaseTime.setClickable(true);
        }else {
            binding.rlReleaseTime.setClickable(false);

        }
    }
}

