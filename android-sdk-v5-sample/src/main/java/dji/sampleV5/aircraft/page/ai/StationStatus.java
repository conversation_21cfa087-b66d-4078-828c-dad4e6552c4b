package dji.sampleV5.aircraft.page.ai;

import android.app.Activity;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;

import com.alibaba.fastjson.JSON;

import java.util.List;

import dji.sampleV5.aircraft.databinding.ActivityReportlistBinding;
import dji.sampleV5.aircraft.net.ApiAdapter;
import dji.sampleV5.aircraft.net.PagingResult;
import dji.sampleV5.aircraft.net.ResultNotice;
import dji.sampleV5.aircraft.page.ai.vo.LocationVO;
import dji.sampleV5.aircraft.page.ai.vo.MainLocationVO;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.utils.common.LogUtils;

/**
 * 用来维护场站选择框的状态
 */
public class StationStatus {

    private final String TAG = LogUtils.getTag(this);

    public interface ReportCallback {
        void update(QueryParam query);
    }

    private final Activity activity;
    private final ActivityReportlistBinding binding;
    private final ApiAdapter apiAdapter;
    private final ReportCallback reportList;

    /**
     * 从远程取得的数据
     */
    private List<MainLocationVO> locationVOList;
    /**
     * 选中的场站
     */
    private int chooseMain;

    /**
     * @param activity   activity 实例
     * @param binding    UI 绑定对象
     * @param reportList 界面回调函数
     */
    public StationStatus(Activity activity, ActivityReportlistBinding binding, ApiAdapter apiAdapter, ReportCallback reportList) {

        this.activity = activity;
        this.binding = binding;
        this.apiAdapter = apiAdapter;
        this.reportList = reportList;

        // 场站选中
        binding.stationSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                chooseMain = position;

                MainLocationVO main = locationVOList.get(position);
                List<LocationVO> children = main.getChildren();

                String[] locations = new String[children.size()];
                for (int i = 0; i < children.size(); i++) {
                    locations[i] = children.get(i).getLocation();
                }

                activity.runOnUiThread(() -> {
                    Log.i(TAG, "stationSecond.setData: " + JSON.toJSONString(locations));
                    binding.stationSecond.setData(locations);
                });
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        // 场站选中
        binding.stationSecond.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (chooseMain == -1 || chooseMain >= locationVOList.size()) {
                    return;
                }

                MainLocationVO main = locationVOList.get(chooseMain);
                List<LocationVO> children = main.getChildren();
                LocationVO vo = children.get(position);

                reportList.update(new QueryParam(vo.getId()));
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    /**
     * 拉取数据
     */
    public void updateData() {

        PagingResult<?, MainLocationVO> query = apiAdapter
                .createPaging(ApiAdapter.YG_LOCATION_LIST, MainLocationVO.class)
                .resetPage(1, 20);

        query.next(new ResultNotice<MainLocationVO>() {
            @Override
            public void onResult(int offset, int total, List<MainLocationVO> list) {

                String[] locations = new String[list.size()];
                for (int i = 0; i < list.size(); i++) {
                    locations[i] = list.get(i).getLocation();
                }

                locationVOList = list;
                chooseMain = -1;

                activity.runOnUiThread(() -> {
                    Log.i(TAG, "stationSpinner.setData: " + JSON.toJSONString(locations));
                    binding.stationSpinner.setData(locations);
                    binding.stationSecond.setData(new String[]{});
                    reportList.update(null);
                });
            }

            @Override
            public void onError(String message) {
                ToastUtil.show(message);
            }
        });
    }
}
