package dji.sampleV5.aircraft.page.fly.controller;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.Timer;
import java.util.TimerTask;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityDefaultLayoutBinding;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.JoyStick;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.common.Attitude;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.flightcontroller.FlightControlAuthorityChangeReason;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.virtualstick.VirtualStickManager;
import dji.v5.manager.aircraft.virtualstick.VirtualStickState;
import dji.v5.manager.aircraft.virtualstick.VirtualStickStateListener;

public class VirtualStickController {
    private static final String TAG = "VirtualStickController";
    private DefaultLayoutActivity activity;
    private ActivityDefaultLayoutBinding binding;
    private boolean isVSEnable = false;
    private MissionJson missionJson;
    private MissionJson remoteMissionJson;
    private Timer timer_vs;
    private TimerTask task_vs;
    private int lhp, lvp, rhp, rvp;
    private float targetHeading;
    private double targetAltitude;
    private Double originLat, originLon;
    private boolean isExecuting;
    private static final int DEFAULT_Multiple = 9;

    public VirtualStickController(DefaultLayoutActivity activity) {
        this.activity = activity;
        this.binding = activity.getBinding();
    }

    /*{"msgFrom":"GID_ConsoleDebug@@@skysys010","type":"joystick","mode":1,"joystick":{"vertical":0,"pitch":80,"roll":0,"yaw":0}}
     * w 对应的是pitch 100 30 50 70 80，s就是负的  A对应的是roll，负数，D是正数  q向左转，e像右转 2 6 10 14 18  page up和down代表上下 vertical*/
    public void onJoyStick(MissionJson missionJson) {
        if (missionJson == null) {
            XLogUtil.INSTANCE.e(TAG, "onJoyStick: is null");
            return;
        }
        this.missionJson = missionJson;
        if (!DJIAircraftApplication.getInstance().isVsEnable && missionJson.getEnable() == 1) {
            Log.e(TAG, "AlertDialog: ");
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setMessage("接收到远程控制指令，开启远程控制后，遥控器将无法控制无人机，退出后即可接管。如果正在执行任务，请先暂停任务！");
            builder.setNegativeButton("取消", (dialog, which) -> {
                dialog.dismiss();
            });
            builder.setPositiveButton("确定", (dialog, which) -> {
                enableVS();
                dialog.dismiss();
            });
            builder.setCancelable(false);
            builder.show();
        } else if (DJIAircraftApplication.getInstance().isVsEnable && TextUtils.equals(missionJson.getCode(), "joystick") && missionJson.getEnable() == 0) {
            Log.e(TAG, "disableVS: ");
            disableVS();
        } else {
            ContextUtil.getHandler().removeCallbacks(clearRunnable);

            JoyStick joyStick = missionJson.getJoystick();
            if (joyStick == null) {
                XLogUtil.INSTANCE.e(TAG, "onJoyStick: joyStick is null");
                return;
            }
            rvp = (int) joyStick.getPitch() * DEFAULT_Multiple;
            rhp = (int) joyStick.getRoll() * DEFAULT_Multiple;
            Log.e(TAG, "onJoyStick rvp: " + rvp + "  rhp:" + rhp);
            lhp = (int) joyStick.getYaw() * 30;
            lvp = (int) joyStick.getVertical() * 30;
            setRightStickData();
            setLeftStickData();
            ContextUtil.getHandler().postDelayed(clearRunnable,170);
        }
    }

    private Runnable clearRunnable = new Runnable() {
        @Override
        public void run() {
            rvp = 0;
            rhp = 0;
            lhp = 0;
            lvp = 0;
            setRightStickData();
            setLeftStickData();
        }
    };

    public void onAdjustAircraft(MissionJson missionJson) {
        remoteMissionJson = missionJson;
        if (!DJIAircraftApplication.getInstance().isVsEnable) {
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setMessage("接收到远程控制指令，开启远程控制后，遥控器将无法控制无人机，退出后即可接管。如果正在执行任务，请先暂停任务！");
            builder.setNegativeButton("取消", (dialog, which) -> {
                dialog.dismiss();
            });
            builder.setPositiveButton("确定", (dialog, which) -> {
                enableVS();
                dialog.dismiss();
            });
            builder.setCancelable(false);
            builder.show();
        } else {
            controlAircraft();
        }
    }

    public void sendVsData() {
        timer_vs = new Timer();
        task_vs = new vsTask();
        timer_vs.schedule(task_vs, 500, 100);
    }

    class vsTask extends TimerTask {
        @Override
        public void run() {
            Log.e(TAG, "lhp:" + lhp + "  lvp:" + lvp + "   rhp:" + rhp + "   rvp:" + rvp);
            if(TextUtils.isEmpty(missionJson.getCode())){
                return;
            }
            switch (missionJson.getCode()) {
                case "heading": //无人机航向角-180-180
                    Attitude aircraftAttitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftAttitude));
                    if (aircraftAttitude != null) {
                        double yaw = aircraftAttitude.getYaw();
                        Log.e(TAG, "目标heading: " + targetHeading + "   当前heading：" + yaw);
                        if (Math.abs(yaw - targetHeading) < 1.5) {
                            lhp = 0;
                            isExecuting = false;
                        }
                    }
                    break;
                case "right": //{"msgFrom":"skysys","type":"adjust","code":"right","right":2}
                case "forward":
                case "backward":
                case "left":
                    LocationCoordinate2D locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
                    if (locationCoordinate2D != null) {
                        Double aircraftLat = locationCoordinate2D.getLatitude();
                        Double aircraftLng = locationCoordinate2D.getLongitude();
                        if (aircraftLat != null && !Double.isNaN(aircraftLat)) {
                            float distance = LatLngUtil.calculateLineDistance(originLat, originLon, aircraftLat, aircraftLng);
                            calculateDistance(distance);
                        }
                    }
                    break;
                case "altitude":
                    double currentAltitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAltitude));
                    Log.e(TAG, "目标高度: " + targetAltitude + "   当前高度：" + currentAltitude);
                    if (Math.abs(targetAltitude - currentAltitude) < 0.1) {
                        lvp = 0;
                        isExecuting = false;
                    }
                    break;
                case "gimbalheading": //在当前的基础上加减
                    break;
                default:
                    //disableVS();
                    break;
            }

            setLeftStickData();
            setRightStickData();
        }
    }

    private void controlAircraft() {
        if (timer_vs == null) {
            Log.e(TAG, "sendVsData");
            sendVsData();
        }
        if (isExecuting) {
            ToastUtil.show("当前指令尚未执行完，请稍后发送指令");
            return;
        }

        this.missionJson = remoteMissionJson;
        ToastUtil.show("收到远程控制无人机的指令");
        switch (missionJson.getCode()) {
            case "heading": //返回的是需要旋转的角度
                float heading = missionJson.getHeading();
                Attitude aircraftAttitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftAttitude));
                if (aircraftAttitude != null) {
                    double yaw = aircraftAttitude.getYaw();
                    targetHeading = (float) yaw + heading;
                    lhp = heading < 0 ? -22 : 22;
                    isExecuting = true;
                }
                break;
            case "right": //{"msgFrom":"skysys","type":"adjust","code":"right","right":2}
            case "forward":
            case "backward":
            case "left":
                LocationCoordinate2D locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
                if (locationCoordinate2D != null) {
                    originLat = locationCoordinate2D.getLatitude();
                    originLon = locationCoordinate2D.getLongitude();
                    if (originLat != null && !Double.isNaN(originLat)) {
                        isExecuting = true;
                        moveAircraft();
                    }
                }
                break;
            case "altitude":
                double currentAltitude = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAltitude));
                targetAltitude = missionJson.getAltitude();
                lvp = (targetAltitude - currentAltitude) < 0 ? -22 : 22;
                isExecuting = true;
                break;
            case "gimbalheading": //在当前的基础上加减
                break;
            default:
                //disableVS();
                break;
        }
    }

    private void calculateDistance(float distance) {
        switch (missionJson.getCode()) {
            case "right":
                float right = missionJson.getRight();
                Log.e(TAG, "目标距离：" + right + "   当前距离: " + distance);
                if (Math.abs(right - distance) < 0.1) {
                    rhp = 0;
                    isExecuting = false;
                }
                break;
            case "left":
                float left = missionJson.getLeft();
                Log.e(TAG, "目标距离：" + left + "   当前距离: " + distance);
                if (Math.abs(left - distance) < 0.1) {
                    rhp = 0;
                    isExecuting = false;
                }
                break;
            case "forward":
                float forward = missionJson.getForward();
                Log.e(TAG, "目标距离：" + forward + "   当前距离: " + distance);
                if (Math.abs(forward - distance) < 0.1) {
                    rvp = 0;
                    isExecuting = false;
                }
                break;
            case "backward":
                float backward = missionJson.getBackward();
                Log.e(TAG, "目标距离：" + backward + "   当前距离: " + distance);
                if (Math.abs(backward - distance) < 0.1) {
                    rvp = 0;
                    isExecuting = false;
                }
                break;
        }
    }

    private void moveAircraft() {
        switch (missionJson.getCode()) {
            case "right":
                float right = missionJson.getRight();
                rhp = 22;
                break;
            case "left":
                float left = missionJson.getLeft();
                rhp = -22;
                break;
            case "forward":
                float forward = missionJson.getForward();
                rvp = 22;
                break;
            case "backward":
                float backward = missionJson.getBackward();
                rvp = -22;
                break;
        }
    }

    public void setLeftStickData() {//hp负数向左转，正数向右转  vp正数上升，负数下降
        VirtualStickManager.getInstance().getLeftStick().setHorizontalPosition(lhp);
        VirtualStickManager.getInstance().getLeftStick().setVerticalPosition(lvp);
    }

    public void setRightStickData() {//hp负数向左前进，正数向右前进  vp正数向前，负数向后
        VirtualStickManager.getInstance().getRightStick().setHorizontalPosition(rhp);
        VirtualStickManager.getInstance().getRightStick().setVerticalPosition(rvp);
    }

    public void enableVS() {
        VirtualStickManager.getInstance().enableVirtualStick(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                isVSEnable = true;
                if (missionJson != null && !TextUtils.equals("joystick", missionJson.getCode())) {
                    Log.e(TAG, "enableVS: controlAircraft");
                    controlAircraft();
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("开启虚拟摇杆失败:" + error.description());
            }
        });
    }

    public void disableVS() {
        VirtualStickManager.getInstance().disableVirtualStick(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("关闭虚拟摇杆");
                isVSEnable = false;
                isExecuting = false;
                lhp = 0;
                lvp = 0;
                rhp = 0;
                rvp = 0;

                if (timer_vs != null) {
                    timer_vs.cancel();
                    timer_vs.purge();
                    timer_vs = null;
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("关闭虚拟摇杆失败:" + error.description());
            }
        });
    }
}
