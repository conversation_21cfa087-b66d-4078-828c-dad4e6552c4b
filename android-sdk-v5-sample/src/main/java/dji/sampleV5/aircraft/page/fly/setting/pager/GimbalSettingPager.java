package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.RadioGroup;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.MenuGimbalPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.GimbalAutoCalibration;
import dji.sampleV5.aircraft.util.ToastUtil;


public class GimbalSettingPager extends BasePager implements RadioGroup.OnCheckedChangeListener {
    private MenuGimbalPagerBinding binding;
    public GimbalSettingPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.gimbal_setting));
        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_gimbal_pager, null, false);
        binding.setActivityMenuPresenter(activityMenuPresenter);
       /* GimbalAutoCalibration autoCalibration = new GimbalAutoCalibration();
        binding.setGimbalAutoCalibration(autoCalibration);*/
        binding.toggleThermalMeter.setChecked(SpUtil.getIsMeasureTemOpen());
        binding.toggleThermalMeter.setOnCheckedChangeListener((buttonView, isChecked) -> {
            Log.e("TAG", "toggleThermalMeter: "+isChecked);
            SpUtil.setIsMeasureTemOpen(isChecked);
            /*if(isChecked){
                ((DefaultLayoutActivity)ContextUtil.getCurrentActivity()).startMeasureTem();
            }else {
                ((DefaultLayoutActivity)ContextUtil.getCurrentActivity()).stopMeasureTem();
            }*/

        });
        flContainer.addView(binding.getRoot());

        binding.temLimit.setText(SpUtil.getWarningTem()+"");
        binding.temLimit.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE) {//点击软键盘完成控件时触发的行为
                //关闭光标并且关闭软键盘
//                    mevASetLimitDistance.setCursorVisible(false);
                InputMethodManager im = (InputMethodManager) ContextUtil.getApplicationContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                im.hideSoftInputFromWindow(ContextUtil.getCurrentActivity().getCurrentFocus().getApplicationWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
                if(binding.temLimit.getText() != null && Integer.parseInt(binding.temLimit.getText().toString()) >= 10){
                    ToastUtil.show("设置成功");
                    SpUtil.setWarningTem(Integer.parseInt(binding.temLimit.getText().toString()));
                }else {
                    ToastUtil.show("温度设置不能低于10℃");
                }
                int uiOptions = View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY | View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION;
                ContextUtil.getCurrentActivity().getWindow().getDecorView().setSystemUiVisibility(uiOptions);
            }
            return true;//消费掉该行为
        });
    }

    @Override
    public void removeListener() {
    }

    @Override
    public void isConnect(boolean connect) {
        /*if (isLoading) {
            binding.rlGimbalSetAdvanced.setClickable(connect);
            binding.tvGimbalAutoSet.setClickable(connect);
            binding.tvGimbalRollSet.setClickable(connect);
            if (connect) {
                *//*boolean dualGimbal = DJIHelper.dualGimbal();
                binding.vGimbalChoose.setVisibility(dualGimbal ? View.VISIBLE : View.GONE);
                binding.vFpvShow.setVisibility(dualGimbal ? View.VISIBLE : View.GONE);
                if (dualGimbal) {
                    setGimbalIndex(0);
                } else {
                    Camera camera = DJIHelper.getInstance().getCamera();
                    if (camera != null) {
                        setGimbalIndex(camera.getIndex());
                    }
                }*//*
            } else {
                binding.vGimbalChoose.setVisibility(View.GONE);
                binding.vFpvShow.setVisibility(View.GONE);
            }
        }*/
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        //setGimbalIndex(group.getChildAt(0).getId() == checkedId ? KeyManager.LEFT_INDEX : KeyManager.RIGHT_INDEX);
    }

    private void setGimbalIndex(int index) {
        activityMenuPresenter.gimbalIndex = index;
    }

}
