package dji.sampleV5.aircraft.page.fly.setting.adapter;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;


import java.util.List;

import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;

public class myPagerAdapter extends PagerAdapter {
    private final List<BasePager> mList;

    public myPagerAdapter(List<BasePager> mList) {
        this.mList=mList;
    }

    @Override
    public int getCount() {
        return mList.size();
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        BasePager basePager = mList.get(position);
        View view = basePager.rootView;
        container.addView(view);
        return view;
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView((View) object);
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }
}
