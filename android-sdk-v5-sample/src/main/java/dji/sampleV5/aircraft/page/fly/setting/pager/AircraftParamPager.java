package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.view.LayoutInflater;
import android.widget.CompoundButton;

import androidx.databinding.DataBindingUtil;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.ChangeFlyMode;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.GoHomeLocationset;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.IMUCalibrationSet;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.NoviceMode;
import dji.sampleV5.aircraft.page.fly.setting.pager.detail.OutOfControlSet;


public class AircraftParamPager extends BasePager {

    private NoviceMode noviceMode;
    private ChangeFlyMode changeFlyMode;
    private GoHomeLocationset goHomeLocationset;
    private OutOfControlSet outOfControlSet;
    private IMUCalibrationSet imuCalibrationSet;
    public AircraftParamPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {
        isLoading = true;
        binding.menuTitleBar.tvTitleMenu.setText(ContextUtil.getString(R.string.aircraft_param_setting));
        MenuAicraftSetPagerBinding aircraftSetPagerBinding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_aicraft_set_pager, null, false);

        if(outOfControlSet != null){
            return;
        }

        goHomeLocationset = new GoHomeLocationset(aircraftSetPagerBinding, (DefaultLayoutActivity)fragment.getActivity());
        aircraftSetPagerBinding.setActivityMenuPresenter(activityMenuPresenter);
        noviceMode = new NoviceMode(aircraftSetPagerBinding);
        //允许切换飞行模式
        changeFlyMode = new ChangeFlyMode(aircraftSetPagerBinding);
        //失控行为
        outOfControlSet = new OutOfControlSet(aircraftSetPagerBinding);
        //imu校准
        imuCalibrationSet = new IMUCalibrationSet(aircraftSetPagerBinding);
        //添加到帧布局
        flContainer.addView(aircraftSetPagerBinding.getRoot());

        boolean isAutoFly = SpUtil.getIsAutoFly();
        aircraftSetPagerBinding.toggleAutoMission.setChecked(isAutoFly);
        aircraftSetPagerBinding.toggleAutoMission.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SpUtil.setIsMissionAutoFly(isChecked);
            }
        });

        boolean isHover = SpUtil.getisHover();
        aircraftSetPagerBinding.toggleIsHover.setChecked(isHover);
        aircraftSetPagerBinding.toggleIsHover.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SpUtil.setIsHover(isChecked);
            }
        });
    }

    @Override
    public void removeListener() {
        if (noviceMode != null) {
            noviceMode.remove();
        }
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading) {
            changeFlyMode.connect(connect);
            noviceMode.connected(connect);
            outOfControlSet.connect(connect);
            imuCalibrationSet.connect(connect);
            if (connect) {
                goHomeLocationset.setGoHomeLocationset();
                goHomeLocationset.Connected();
            } else {
                goHomeLocationset.unConnected();
            }
        }
    }
}
