package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.LayoutInflater;

import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuBatterySmartInfoPagerBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;
import dji.sampleV5.aircraft.page.fly.setting.AircraftSettingFragment;
import dji.sampleV5.aircraft.page.fly.setting.pager.BasePager;
import dji.sampleV5.aircraft.view.adapter.CommonAdapter;

import java.util.ArrayList;
import java.util.List;



public class BaseBatteryInfoPager extends BasePager {

    private int size;

   /* private KeyListener chargeListener;
    private KeyListener voltageListener;
    private KeyListener cellVoltagesListener;
    private KeyListener temperatureKeyListener;
    private KeyListener numberOfDischargeListener;*/

    private RecyclerView rvBatteryShow;
    private CommonAdapter<BatteryInfo> commonAdapter;
    private ActivityMenuPresenter activityMenuPresenter1;
    private List<BatteryInfo> listInfo = new ArrayList<>();

    public BaseBatteryInfoPager(AircraftSettingFragment fragment) {
        super(fragment);
    }

    @Override
    public void initData() {

        size = listInfo.size();
        tvTitle.setText(ContextUtil.getString(R.string.smart_battery_info));
        activityMenuPresenter.setIsPrevious(true);
        activityMenuPresenter.setPreviousName("BatteryPager");
        MenuBatterySmartInfoPagerBinding binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_battery_smart_info_pager, null, false);
        ActivityMenuPresenter activityMenuPresenter = new ActivityMenuPresenter(fragment);
        binding.setActivityMenuPresenter(activityMenuPresenter);
        activityMenuPresenter1 = binding.getActivityMenuPresenter();
        rvBatteryShow = binding.menuBatteryPagerItem1.rvBatteryShow;
       /* Battery battery = DJIHelper.getInstance().getBattery();
        if (battery == null) {
            return;
        }
        int index = battery.getIndex() + 1;
        binding.setBatteryName("电池  " + index);*/
        //获取电池的状态
        getBatteryInfo();
        //获得六个小电池组的电压
        getCellVoltages();
        //添加到帧布局
        flContainer.removeAllViews();
        flContainer.addView(binding.getRoot());
    }

    private void getBatteryInfo() {
        //电池循环次数
        getNumberDischarge();
        //电池容量
        getFullCharge();
        //温度
        getTemperature();
        //剩余电量
        getCharge();
        //电压
        getVoltage();
    }

    private void getCellVoltages() {
       /* KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.CELL_VOLTAGES), new YNListener1<Integer[]>() {
            @Override
            public void onSuccess(Integer[] value) {
                for (int i = 0; i < size; i++) {
                    listInfo.remove(0);

                }
                for (Integer aValue : value) {

                    float percent = aValue / 4250f;
                    String CellVoltage = Util.getDot((aValue / 1000f), 2) + ContextUtil.getString(R.string.v);
                    BatteryInfo batteryInfo = new BatteryInfo();
                    batteryInfo.setCellVoltage(CellVoltage);
                    batteryInfo.setPercent(percent);
                    listInfo.add(batteryInfo);
                }
                //创建RecyclerView
                initRecyclerView();
                update();
            }

            @Override
            public void onException(Throwable e) {
            }
        });
*/
    }

    private void update() {
/*
        cellVoltagesListener = new KeyListener<Integer[]>() {
            @Override
            protected void onValueChanged(@Nullable Integer[] old, @Nullable final Integer[] value) {
                if (value == null)
                    return;

                for (int i = 0; i < value.length; i++) {

                    float percent = value[i] / 4250f;
                    String CellVoltage = Util.getDot((value[i] / 1000f), 2) + ContextUtil.getString(R.string.v);
                    BatteryInfo batteryInfo = listInfo.get(i);

                    batteryInfo.setPercent(percent);
                    batteryInfo.setCellVoltage(CellVoltage);
                    final int finalI = i;
                    ContextUtil.getHandler().post(() -> commonAdapter.notifyItemChanged(finalI));
                }
            }
        };

        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.CELL_VOLTAGES), cellVoltagesListener);*/
    }

    private void initRecyclerView() {
/*
        LinearLayoutManager mLayoutManager = new LinearLayoutManager(ContextUtil.getApplicationContext());
        mLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        rvBatteryShow.setLayoutManager(mLayoutManager);
        commonAdapter = new CommonAdapter<BatteryInfo>(ContextUtil.getApplicationContext(), R.layout.item_battery_show, listInfo) {
            @Override
            public void convert(final ViewHolder holder, final BatteryInfo batteryInfo, int position) {
                ContextUtil.getHandler().post(() -> {

                    holder.setPercent(R.id.battery_energy, batteryInfo.getPercent());
                    holder.setText(R.id.battery_text, batteryInfo.getCellVoltage());
                });
            }
        };
        ContextUtil.getHandler().post(() -> {
            rvBatteryShow.setAdapter(commonAdapter);
            rvBatteryShow.setItemAnimator(null);
        });*/

    }

    @Override
    public void removeListener() {
        /*KeyManager.getInstance().removeListener(cellVoltagesListener);
        KeyManager.getInstance().removeListener(numberOfDischargeListener);
        KeyManager.getInstance().removeListener(temperatureKeyListener);
        KeyManager.getInstance().removeListener(chargeListener);
        KeyManager.getInstance().removeListener(voltageListener);*/

    }

    //电压
    private void getVoltage() {
        /*voltageListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> {
                    if (now != null)
                        activityMenuPresenter1.setBatteryPressure(Util.getDot((now / 1000f), 2) + ContextUtil.getString(R.string.v));

                });
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.VOLTAGE), voltageListener);*/
    }

    private void getCharge() {
        /*chargeListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> activityMenuPresenter1.setCurrentEnergy(now + ContextUtil.getString(R.string.mah)));
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.CHARGE_REMAINING), chargeListener);*/
    }

    private void getNumberDischarge() {
        /*numberOfDischargeListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now != null) {
                    ContextUtil.getHandler().post(() -> activityMenuPresenter1.setCyclikTime(now + ""));
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.NUMBER_OF_DISCHARGES), numberOfDischargeListener);*/
    }

    private void getFullCharge() {
       /* KeyManager.getInstance().getValue(BatteryKey.create(BatteryKey.FULL_CHARGE_CAPACITY), new YNListener1<Integer>() {
            @Override
            public void onSuccess(final Integer value) {
                ContextUtil.getHandler().post(() -> activityMenuPresenter1.setFullEnergy(value + ContextUtil.getString(R.string.mah)));
            }

            @Override
            public void onException(Throwable e) {

            }
        });*/
    }

    private void getTemperature() {
        /*temperatureKeyListener = new KeyListener<Float>() {
            @Override
            protected void onValueChanged(@Nullable Float old, @Nullable final Float now) {
                ContextUtil.getHandler().post(() -> {
                    if (now != null)
                        activityMenuPresenter1.setBatteryTemperature(Util.getDot(now, 1) + ContextUtil.getString(R.string.temperature_unit));
                });
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(BatteryKey.create(BatteryKey.TEMPERATURE), temperatureKeyListener);*/
    }
}
