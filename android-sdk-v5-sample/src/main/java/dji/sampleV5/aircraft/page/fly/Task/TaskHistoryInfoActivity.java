package dji.sampleV5.aircraft.page.fly.Task;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.amap.api.maps.AMap;
import com.amap.api.maps.AMapOptions;
import com.amap.api.maps.CameraUpdate;
import com.amap.api.maps.CameraUpdateFactory;
import com.amap.api.maps.MapView;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.TextureMapView;
import com.amap.api.maps.UiSettings;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.CameraPosition;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.PolylineOptions;
import com.google.gson.Gson;

import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.JsonResult;
import dji.sampleV5.aircraft.data.task.MissionInfo;
import dji.sampleV5.aircraft.data.task.SiteInfo;
import dji.sampleV5.aircraft.databinding.ActivityTaskHistoryInfoBinding;
import dji.sampleV5.aircraft.lbs.MapIndex;
import dji.sampleV5.aircraft.lbs.amap.AMapTranslation;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.lbs.bean.MarkerInfo;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.PopupExtKt;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.util.CoordinateConverterUtil;
import dji.sampleV5.aircraft.mvvm.util.marker.MarkerUtil;
import dji.sampleV5.aircraft.mvvm.widget.popup.MapTypePopup;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.util.HttpUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.coordinate.GeoSysConversion;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.util.task.MapUtil;
import dji.sampleV5.aircraft.view.task.HistoryAircraftInfoView;
import dji.sampleV5.aircraft.view.task.TaskAircraftInfoView;
import dji.sampleV5.aircraft.view.task.TaskInfoController;
import dji.sampleV5.aircraft.view.task.TaskInfoPlayer;
import dji.sampleV5.aircraft.view.task.TaskMissionInfoView;
import dji.sampleV5.aircraft.view.task.TaskVideoPlayer;
import me.jessyan.autosize.internal.CancelAdapt;

import com.hjq.toast.Toaster;
import com.yc.video.player.OnVideoStateListener;
import com.yc.video.ui.view.CustomBottomView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class TaskHistoryInfoActivity extends AppCompatActivity {
    public static final int SMALL_WINDOW_RATIO = 4;
    private int bigWidth;
    private int bigHeight;
    private int smallWidth;
    private int smallHeight;
    private TaskInfoPlayer taskInfoPlayer;
    private TextureMapView mMapView;
    private MissionInfo missionInfo;
    private AMap aMap;
    private long startTimestamp;
    private MapUtil mapUtil;
    private AMapOptions aOptions;
    private TaskInfoController taskInfoController;
    private TaskVideoPlayer videoPlayer;
    private TaskAircraftInfoView taskAircraftInfoView;
    private HistoryAircraftInfoView historyAircraftInfoView;
    private TaskMissionInfoView taskMissionInfoView;
    private boolean isShowMap = false;
    private RelativeLayout.LayoutParams smallWindow;
    private long pos;
    private ActivityTaskHistoryInfoBinding binding;
    private MapTypePopup mapTypePopup;
    private MarkerUtil markerUtil;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ImmerseUtil.startImmerse(this);
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_task_history_info);
        initWindowSize();
        //地图初始化
        initMap(savedInstanceState);
        //数据初始化
        getValue();
        //视频控件初始化
        initView();

        if (markerUtil == null) {
            markerUtil = new MarkerUtil(this, mMapView.getMap());
        }

    }


    private void initView() {
        taskInfoController = findViewById(R.id.controller);
        CustomBottomView customBottomView = new CustomBottomView(this);
        customBottomView.setVisibility(View.VISIBLE);
        customBottomView.showBottomProgress(true);
        taskInfoController.removeControlComponent(customBottomView);
        taskInfoController.addControlComponent(customBottomView);
        taskInfoController.show();
        taskInfoController.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                taskInfoController.show();
            }
        });
        taskInfoPlayer = new TaskInfoPlayer(this);
        taskInfoPlayer.setController(taskInfoController);
        taskInfoPlayer.setVideoImlCallback(callback);


        //飞机信息view
        taskAircraftInfoView = findViewById(R.id.aircraft_info);
        historyAircraftInfoView = findViewById(R.id.history_aircraft_info);
        //任务信息view
        taskMissionInfoView = findViewById(R.id.mission_info);

        initSmallWindow();
        //初始化地图选择popup
        mapTypePopup = new MapTypePopup(this, position -> {
            if (position == 0) {
                binding.mapView.getMap().setMapType(AMap.MAP_TYPE_NORMAL);
            }else  {
                binding.mapView.getMap().setMapType(AMap.MAP_TYPE_SATELLITE);
            }
        });

        findViewById(R.id.iv_aircraft_info).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                taskAircraftInfoView.setVisibility(View.VISIBLE);
            }
        });
        findViewById(R.id.iv_mission_info).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                taskMissionInfoView.setVisibility(View.VISIBLE);
            }
        });
        findViewById(R.id.ivBack).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                TaskHistoryInfoActivity.this.finish();
            }
        });
        findViewById(R.id.iv_change).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                changeView();
            }
        });

        binding.btnMapType.setOnClickListener(v ->{
            if (mapTypePopup.isShow()) {
                mapTypePopup.dismiss();
            } else {
                PopupExtKt.showMapTypePopup(TaskHistoryInfoActivity.this, mapTypePopup, v);
            }
        });

    }

    private void getValue() {
        Intent intent = getIntent();
        String missionBatch = intent.getStringExtra("MISSIONBATCH");
        String siteID = intent.getStringExtra("SITEID");
        initHttp(missionBatch, siteID);
    }

    private void initSmallWindow() {
        int width = DensityUtil.dp2px(170);
        int height = DensityUtil.dp2px(95);
        int marginLeft = DensityUtil.dp2px(10);
        int marginBottom = DensityUtil.dp2px(40);
        smallWindow = new RelativeLayout.LayoutParams(width, height);
        smallWindow.setMargins(marginLeft, 0, 0, marginBottom);
        smallWindow.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
    }


    private void changeView() {
        if (videoPlayer == null) return;
        if (isShowMap) {
            updateFullWindowParams(binding.rlMap);
            binding.rlVideo.setLayoutParams(smallWindow);
            binding.root.bringChildToFront(binding.rlVideo);

        } else {
            updateFullWindowParams(binding.rlVideo);
            binding.rlMap.setLayoutParams(smallWindow);
            binding.root.bringChildToFront(binding.rlMap);
        }
        binding.root.bringChildToFront(binding.top);
        binding.root.bringChildToFront(binding.other);
        isShowMap = !isShowMap;
    }

    private void initWindowSize() {
        bigWidth = DensityUtil.getScreenWidth();
        bigHeight = DensityUtil.getScreenHeight();

        smallWidth = bigWidth / SMALL_WINDOW_RATIO;
        smallHeight = smallWidth * 9 / 16;
    }

    private void updateSmallWindowParams(View view, int width, int height) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(width, height);
        lp.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        lp.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        view.setLayoutParams(lp);
    }

    private void updateFullWindowParams(View view) {
        RelativeLayout.LayoutParams lp = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        view.setLayoutParams(lp);
    }


    private void initMap(Bundle savedInstanceState) {
//        MapsInitializer.setTerrainEnable(true);
        mMapView = findViewById(R.id.map_view);
        mMapView.onCreate(savedInstanceState);
        aMap = mMapView.getMap();
        aMap.setMapType(AMap.MAP_TYPE_SATELLITE);
        mapUtil = new MapUtil(this, aMap);
        mapUtil.setArriveCallback(arriveCallback);
        UiSettings aOptions = aMap.getUiSettings();
        aOptions.setZoomControlsEnabled(false);
        aOptions.setLogoBottomMargin(-100);
        aOptions.setRotateGesturesEnabled(false);// 禁止通过手势缩放地图
        aOptions.setTiltGesturesEnabled(false);// 禁止通过手势倾斜地图

    }

    private void initVideoConfig() {
        List<MissionInfo.FlightPath> flightPaths;
        if (missionInfo.getMissioninfo().getFlightParams().getFlightPath() == null) {
            flightPaths = missionInfo.getMissioninfo().getFlightParams().getChildrenList().get(0).getFlightPath();
        } else {
            flightPaths = missionInfo.getMissioninfo().getFlightParams().getFlightPath();
        }
        drawWaypoint(flightPaths);
        /*List<AppLatLng> pointList = new ArrayList<>(flightPaths.size());
        for(MissionInfo.FlightPath path : flightPaths){
            AppLatLng latLng = new AppLatLng(GeoSysConversion.wgs84toGCJ02(path.getLatitude(), path.getLongitude()));
            pointList.add(latLng);

            Bitmap bMap = BitmapFactory.decodeResource(getResources(), R.drawable.poi_marker);
            MarkerInfo info = new MarkerInfo()
                    .setPosition(latLng)
                    .setIcon(bMap)
                    .setZIndex(MapIndex.ROUTE_VERTEX_INDEX);
            MarkerOptions markerOption = new MarkerOptions();
            markerOption.position(AMapTranslation.toSDK(info.getPosition()));

            markerOption.draggable(info.isDraggable());
            markerOption.anchor(info.getAnchorU(), info.getAnchorV());
            markerOption.icon(BitmapDescriptorFactory.fromBitmap(info.getIcon()));
            markerOption.zIndex(info.getZIndex());
            markerOption.setFlat(info.isFlat());
            markerOption.rotateAngle(info.getRotation());
            aMap.addMarker(markerOption);
        }
        PolylineOptions polylineOptions = new PolylineOptions();
        polylineOptions.width(16);
        polylineOptions.color(0xffff8800);
        polylineOptions.addAll(AMapTranslation.toSDK(pointList));
        polylineOptions.zIndex(MapIndex.MY_LOCATION_INDEX);
        aMap.addPolyline(polylineOptions);*/
        if (missionInfo.getFlightRecords() == null) {
            Toast.makeText(this, "暂无点位信息", Toast.LENGTH_SHORT).show();
            return;
        }
        startTimestamp = missionInfo.getFlightRecords().list.get(0).getTimestamp();
        long endTimestamp = missionInfo.getFlightRecords().list.get(missionInfo.getFlightRecords().list.size() - 1).getTimestamp();
        //第一个点作为地图中心点
        MissionInfo.FlightRecordBean operateRecordsBean = missionInfo.getFlightRecords().list.get(0);
        double[] position = GeoSysConversion.wgs84toGCJ02(operateRecordsBean.getLatitude(), operateRecordsBean.getLongitude());
        CameraUpdate cameraUpdate = CameraUpdateFactory.newCameraPosition(new CameraPosition(new LatLng(position[0], position[1]), 17, 0, 17));
        aMap.animateCamera(cameraUpdate);
        //相当于给video设置视频源
        mapUtil.setInfoList(missionInfo);
        taskInfoPlayer.setMap(mapUtil);
        taskInfoPlayer.setStartTimp(startTimestamp);
        taskInfoPlayer.setDuration(endTimestamp - startTimestamp);
        taskInfoPlayer.start();
        //initVideo();
        if (missionInfo.getUAVRVPath() != null && !missionInfo.getUAVRVPath().isEmpty()) {
            initVideo();
        }else {
            binding.rlVideo.setVisibility(View.GONE);
        }
        taskInfoPlayer.play();
    }


    private void initVideo() {
        TaskInfoController videoController = new TaskInfoController(this);
        TaskInfoController videoController1 = new TaskInfoController(this);
        videoPlayer = findViewById(R.id.video);
        //videoPlayer1 = findViewById(R.id.video1);
        videoPlayer.setController(videoController);
        //videoPlayer1.setController(videoController1);
        /*videoPlayer.setUrl("https://skysys-video-hub2.oss-cn-beijing.aliyuncs.com/record/xl_uav/ch6666801/333_2022-11-25-10-01-11_2022-11-25-10-10-50.mp4");
        videoPlayer1.setUrl("https://skysys-video-hub2.oss-cn-beijing.aliyuncs.com/record/xl_uav/ch6666801/333_2022-11-25-10-01-11_2022-11-25-10-10-50.mp4");*/
        videoPlayer.setUrl(missionInfo.getUAVRVPath());
        //videoPlayer1.setUrl(missionInfo.getUAVRVPath());

        //videoPlayer1.start();
        videoPlayer.start();
        videoPlayer.addOnStateChangeListener(new OnVideoStateListener() {
            @Override
            public void onPlayerStateChanged(int playerState) {

            }

            @Override
            public void onPlayStateChanged(int playState) {
                if (playState == 6) {
                    videoPlayer.seekTo(pos);
                }
            }
        });
       /* videoPlayer1.addOnStateChangeListener(new OnVideoStateListener() {
            @Override
            public void onPlayerStateChanged(int playerState) {

            }

            @Override
            public void onPlayStateChanged(int playState) {
                if (playState == 6) {
                    videoPlayer1.seekTo(pos);
                }


            }
        });*/
    }

    private void initMissionView() {
        taskMissionInfoView.setMissionInfo(missionInfo);
    }

    private void initAircraftView(MissionInfo info, SiteInfo siteInfo) {
        taskAircraftInfoView.setAircraftInfo(missionInfo, siteInfo);
    }

    private void initHttp(String missionBatch, String siteId) {
        String url = ApiConfig.INSTANCE.getNJ_LOGIN_URL() + ApiConfig.HISTORY_DETAIL;
        Map<String, String> map = new HashMap<>();
        map.put("missionBatch", missionBatch);
        HttpUtil.getInstance().get(this, url, map, "token", jsonResult -> {
            Log.e("BATCHINFO", ": "+ CommExtKt.toJsonStr(jsonResult));
            missionInfo = new Gson().fromJson(jsonResult.getData().toString(), MissionInfo.class);
            if (missionInfo == null) {
                Toaster.show("暂无任务数据");
                return;
            }
            if(missionInfo.getFlightRecords() == null || missionInfo.getFlightRecords().getList() == null || missionInfo.getFlightRecords().getList().size() == 0){
                ToastUtil.show("没有获取到飞行数据");
            }else {
                initVideoConfig();
                initMissionView();
                initAircraftView(missionInfo, null);
            }
        });

     /*   String getSiteInfoUrl = ApiConfig.MISSION_URL + ApiConfig.GET_SITE_INFO;
        Map<String, String> map1 = new HashMap<>();
        map1.put("siteID", siteId);
        HttpUtil.getInstance().get(this, getSiteInfoUrl, map1, "token", new HttpUtil.ICallBack() {
            @Override
            public void onResponse(JsonResult jsonResult) {
                Log.e("SITEINFO", ": "+ CommExtKt.toJsonStr(jsonResult));
                *//*SiteInfo siteInfo = new Gson().fromJson(jsonResult.getData().toString(), SiteInfo.class);
                initAircraftView(null, siteInfo);*//*
            }
        });*/

    }

    private void drawWaypoint(List<MissionInfo.FlightPath> flightPaths) {
        markerUtil.clearAll();
        List<LatLng> latLngList = new ArrayList<>();
        for (MissionInfo.FlightPath path : flightPaths) {
            LatLng latLng = new LatLng(path.getLatitude(), path.getLongitude());
            latLngList.add(CoordinateConverterUtil.INSTANCE.convertWGS84ToAMap(latLng));
        }
        markerUtil.addMarker(latLngList);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapView.onPause();
        taskInfoPlayer.pause();
        if (videoPlayer != null) {
            if (videoPlayer.isPlaying()) {
                videoPlayer.pause();
            }

           /* if (videoPlayer1.isPlaying()) {
                videoPlayer1.pause();
            }*/

        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        //在activity执行onResume时执行mMapView.onResume ()，重新绘制加载地图
        mMapView.onResume();
        ImmerseUtil.startImmerse(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMapView.onDestroy();
        taskInfoPlayer.setExit(true);
        if (videoPlayer != null) {
            videoPlayer.release();
            //videoPlayer1.release();
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        //在activity执行onSaveInstanceState时执行mMapView.onSaveInstanceState (outState)，保存地图当前的状态
        mMapView.onSaveInstanceState(outState);
    }

    private void setPos(long pos) {
        this.pos = pos;
    }

    private TaskInfoPlayer.VideoImlCallback callback = new TaskInfoPlayer.VideoImlCallback() {


        @Override
        public void start() {
            if (videoPlayer != null) {
                videoPlayer.start();
                //videoPlayer1.start();
            }

        }

        @Override
        public void pause() {
            if (videoPlayer != null) {
                videoPlayer.pause();
                //videoPlayer1.pause();
            }
        }

        @Override
        public void seekTo(long pos) {
            setPos(pos);
            if (videoPlayer != null) {
                //videoPlayer1.seekTo(pos);

                videoPlayer.seekTo(pos);

            }
            taskMissionInfoView.initRefresh();
        }

        @Override
        public void end() {
            if (videoPlayer != null) {
                videoPlayer.pause();
                //videoPlayer1.pause();
            }
        }

        @Override
        public void getTime(long pos) {
            setPos(pos);
        }
    };
    private MapUtil.ArriveCallback arriveCallback = new MapUtil.ArriveCallback() {
        @Override
        public void toPoint(MissionInfo.FlightRecordBean operateRecordsBean) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    taskAircraftInfoView.refresh(operateRecordsBean);
                    historyAircraftInfoView.refresh(operateRecordsBean);
                    taskMissionInfoView.refresh(operateRecordsBean.getTimestamp());
                }
            });

        }
    };
}
