package dji.sampleV5.aircraft.page.picture;

import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.ActivityPictureBinding;
import dji.sampleV5.aircraft.page.picture.media.MediaFragment;
import dji.sampleV5.aircraft.page.picture.media.MediaShowFragment;

public class PictureFragmentManager {
    public static final String MEDIA_FRAGMENT = "media_fragment";
    public static final String MEDIA_FRAGMENT_SHOW = "media_fragment_show";

    private FragmentManager manager;
    private ActivityPictureBinding binding;

    public PictureFragmentManager(FragmentManager manager, ActivityPictureBinding binding) {
        this.manager = manager;
        this.binding = binding;
    }

    public void addFragment(String tag, Bundle bundle) {
        Fragment fragment = manager.findFragmentByTag(tag);
        if (fragment == null) {
            switch (tag) {
                case MEDIA_FRAGMENT:
                     fragment = MediaFragment.newInstance();
                    break;
                case MEDIA_FRAGMENT_SHOW:
                     fragment = MediaShowFragment.newInstance(bundle);
                    break;
            }
            manager.beginTransaction().add(R.id.container_navigation, fragment, tag).commitAllowingStateLoss();
            binding.containerNavigation.bringToFront();
        }
    }


    public Fragment getFragment(String tag) {
        return manager.findFragmentByTag(tag);
    }

    public void removeFragment(String tag) {
        Fragment fragment = manager.findFragmentByTag(tag);
        if (fragment != null) {
            manager.beginTransaction().remove(fragment).commitAllowingStateLoss();
        }
    }

    public boolean onBackPressed() {
        Fragment fragmentMediaFragment = manager.findFragmentByTag(MEDIA_FRAGMENT);
        Fragment fragmentFragmentShow = manager.findFragmentByTag(MEDIA_FRAGMENT_SHOW);
        if (fragmentFragmentShow!=null){
            removeFragment(MEDIA_FRAGMENT_SHOW);
        }
        if (fragmentMediaFragment!=null){
            removeFragment(MEDIA_FRAGMENT);
            return false;
        }
        return true;
    }


}
