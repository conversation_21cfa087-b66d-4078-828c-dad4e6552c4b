package dji.sampleV5.aircraft.page.ai;

import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityReportlistBinding;
import dji.sampleV5.aircraft.net.ApiAdapter;
import dji.sampleV5.aircraft.net.LoginNotice;
import dji.sampleV5.aircraft.net.PagingResult;
import dji.sampleV5.aircraft.page.ai.vo.ReportInfoVO;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.v5.utils.common.LogUtils;
import me.jessyan.autosize.internal.CancelAdapt;

public class AiReportActivity extends AppCompatActivity implements View.OnSystemUiVisibilityChangeListener, CancelAdapt {

    private final String TAG = LogUtils.getTag(this);

    /**
     * 界面渲染 layout.xml
     */
    private ActivityReportlistBinding binding;
    /**
     * 远程代理接口
     */
    private ApiAdapter apiAdapter;

    /**
     * item 点击时调用
     */
    private void onItemClick(View view, ReportItemVO item) {
        Log.e(TAG, String.format("onItemClick siteID: %s", item.pdfUrl));
        //PdfViewerActivity.showPdf(this, item);
    }

    /**
     * 场站切换时拉取报告列表
     */
    private void switchLocation(QueryParam param) {

        // 分页对象
        PagingResult<QueryParam, ReportInfoVO> query = apiAdapter.createPaging(ApiAdapter.YG_AI_REPORT_LIST, ReportInfoVO.class);
        query.resetParam(param == null ? new QueryParam("") : param);

        // 滑块
        RecyclerView recyclerView = new RecyclerView(this);
        // 分页滑动 adapter
        AiReportUIAdapter adapter = new AiReportUIAdapter(this, query, this::onItemClick);

        // 修饰 recycleView
        int o = getResources().getConfiguration().orientation;
        int dOri = o == Configuration.ORIENTATION_LANDSCAPE ? DividerItemDecoration.HORIZONTAL : DividerItemDecoration.VERTICAL;
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.recyclerView.getContext(), dOri);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 1));
        recyclerView.setAdapter(adapter);
        // binding.recyclerView.addItemDecoration(dividerItemDecoration);

        // 放入 container, 如果 container 中有元素, 替换
        LinearLayout.LayoutParams cLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        recyclerView.setLayoutParams(cLayout);

        LinearLayout container = binding.itemContainer;
        int count = container.getChildCount();
        if (count > 0) {
            for (int i = count - 1; i >= 0; i--) {
                container.removeViewAt(i);
            }
        }
        container.addView(recyclerView);

        // 加载数据
        if (param != null) {
            adapter.loadData();
        }
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        getWindow().setNavigationBarColor(getResources().getColor(android.R.color.transparent));
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(this);

        ImmerseUtil.startImmerse(this);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN); //键盘弹起后会把屏幕往上推
        super.onCreate(savedInstanceState);

        // ui binding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_reportlist);
        binding.ivBack.setOnClickListener(v -> finish());

        LoginCache loginCache = SpUtil.getLoginCache();
        Log.e(TAG, "onCreate:" + loginCache.getQicloudToken());

        // 调用远程程璐接口
        ApiAdapter.login(loginCache.getUserName(), loginCache.getPwd(), new LoginNotice() {
            @Override
            public void onResult(LoginCache cache) {
                // 创建远程代理对象
                apiAdapter = new ApiAdapter(cache);
                // 场站选择
                StationStatus stationStatus = new StationStatus(AiReportActivity.this, binding, apiAdapter, AiReportActivity.this::switchLocation);
                stationStatus.updateData();
            }

            @Override
            public void onError(String message) {
                AiReportActivity.this.runOnUiThread(() -> ToastUtil.show("加载数据失败"));
            }
        });
    }

    @Override
    public void onBackPressed() {
        finish();
    }

    private Runnable enterImmerseMode = () -> ImmerseUtil.startImmerse(AiReportActivity.this);

    @Override
    public void onSystemUiVisibilityChange(int visibility) {
        if (visibility == 0) {
            ContextUtil.getHandler().removeCallbacks(enterImmerseMode);
            ContextUtil.getHandler().postDelayed(enterImmerseMode, 2000);
        }
    }
}
