package dji.sampleV5.aircraft.page.fly.controller;

import android.app.AlertDialog;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.TaskInfo;
import dji.sampleV5.aircraft.databinding.ActivityRecentmissionBinding;
import dji.sampleV5.aircraft.ftp.FtpConfig;
import dji.sampleV5.aircraft.ftp.FtpConnectionUpload;
import dji.sampleV5.aircraft.ftp.OnThreadResultListener;
import dji.sampleV5.aircraft.minio.MinioConnectionUpload;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.net.bean.HistoryInfoBean;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.oss.OssConnectionUpload;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.utils.ProductUtil;
import dji.sdk.keyvalue.value.camera.DateTime;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.MediaDataCenter;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaFileListDataSource;
import dji.v5.manager.datacenter.media.MediaFileListState;
import dji.v5.manager.datacenter.media.MediaFileListStateListener;
import dji.v5.manager.datacenter.media.MediaManager;
import dji.v5.manager.datacenter.media.PullMediaFileListParam;

public class MissionPhotoUploaderForRecent {

    private static final int SHOW_PROGRESS_DIALOG = 2;
    private static final int HIDE_PROGRESS_DIALOG = 3;
    private static final int NOT_PICTURE = 4;
    private static final int HAVE_PICTURE = 5;
    //private WaypointTable table;
    private ActivityRecentmissionBinding binding;
    private static MissionPhotoUploaderForRecent instance;
    //private List<WaypointMission.Waypoint> wayPointList;
    private ArrayList<MediaFile> downloadMediaList = new ArrayList<>();
    private ArrayList<String> newFileNameList = new ArrayList<>();
    private OnUploadSuccessListener onUploadSuccessListener;
    private List<MediaFile> mediaFiles;
    private String missionId;
    private boolean isUploading;
    private String ftpPath;
    private HistoryInfoBean taskInfo;
    private FtpConnectionUpload ftpConnectionUpload;
    private MinioConnectionUpload minioConnectionUpload;
    private String minioPath;
    private OssConnectionUpload ossConnectionUpload;
    private String ossPath;

    public void setUploading(boolean uploading) {
        isUploading = uploading;
    }

    private Runnable downloadRunnable = new Runnable() {
        @Override
        public void run() {
            binding.downloadLL.setVisibility(View.VISIBLE);
            binding.downloadLL.bringToFront();
            MediaDownloadController.getInstance().setOnProgressListener(new MediaDownloadController.OnProgressUpdateListener() {
                @Override
                public void onProgressUpdate(final long total, final long current, final String fileName, final int currentPosition) {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            binding.downloadText.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (currentPosition + 1) + "个 " + fileName);
                            binding.downloadProgressBar.setProgress((int) (current * 100 / total));
                            binding.downPercent.setText((int) (current * 100 / total) + "%");
                        }
                    });
                }

                @Override
                public void onSuccess(String fileName) {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            binding.downloadText.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 " + fileName);
                            binding.downloadProgressBar.setProgress(100);
                            binding.downPercent.setText("100%");
                            XLogUtil.INSTANCE.e("TAG", "总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载完第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 " + fileName);
                            /*if (MediaDownloadController.getInstance().getCurrentPosition() < MediaDownloadController.getInstance().getTotalCount()) {
                                ToastUtil.show("总个数" + MediaDownloadController.getInstance().getTotalCount() + " 当前第" + (MediaDownloadController.getInstance().getCurrentPosition() + 1) + "个 ");
                            }

                            if (MediaDownloadController.getInstance().getTotalCount() == (MediaDownloadController.getInstance().getCurrentPosition() + 1)) {
                                Log.e("TAG", "run: 全部下载完成,开始上传");
                                binding.downloadText.setText("全部下载完成,开始上传");
                                binding.btnWarning.setText("正在上传文件，请稍等");
                                ToastUtil.show("全部下载完成");
                                upload();
                            }*/
                        }
                    });

                }

                @Override
                public void onFinish() {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            XLogUtil.INSTANCE.e("TAG", "run: 全部下载完成,开始上传");
                            binding.downloadText.setText("全部下载完成,开始上传");
                            binding.btnWarning.setText("正在上传文件，请稍等");
                            ToastUtil.show("全部下载完成");
                            if (ApiConfig.INSTANCE.getIS_INNER_NETWORK()) {
                                uploadMinio();
                            } else {
                                uploadOss();
                            }
                        }
                    });
                }
            });
            MediaDownloadController.getInstance().startDownload();
        }
    };

    private MissionPhotoUploaderForRecent() {
    }

    public static MissionPhotoUploaderForRecent getInstance() {
        if (instance == null) {
            instance = new MissionPhotoUploaderForRecent();
        }
        return instance;
    }

    //从飞行历史过来，卸载历史数据信息
    public void onMissionComplete(String missionId, HistoryInfoBean taskInfo, ActivityRecentmissionBinding binding, OnUploadSuccessListener onUploadSuccessListener) {
        if (!isUploading) {
            ToastUtil.show("开始准备数据，请稍等");
            this.binding = binding;
            this.taskInfo = taskInfo;
            this.missionId = missionId;
            this.onUploadSuccessListener = onUploadSuccessListener;
            String date = stampToDate(System.currentTimeMillis());
            UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();
            if (uavInfoSN == null || uavInfoSN.getData().getSiteInfo().getSiteID() == null) {
                onUploadSuccessListener.onError();
                ToastUtil.show("没有获取到站点数据，请联系后台！");
                return;
            }
            if (ApiConfig.INSTANCE.getIS_INNER_NETWORK()) {
                minioPath = FtpConfig.minioPath + uavInfoSN.getData().getSiteInfo().getSiteID() + "/" + date + "/" + uavInfoSN.getData().getUAVID() + "/" + missionId + "/";
            } else {
                ossPath = FtpConfig.ossPath + uavInfoSN.getData().getSiteInfo().getSiteID() + "/" + date + "/" + uavInfoSN.getData().getUAVID() + "/" + missionId + "/";
            }
//            ftpPath = FtpConfig.ftpPath + uavInfoSN.getData().getSiteInfo().getSiteID() + "/" + date + "/" + uavInfoSN.getData().getUAVID() + "/" + missionId + "/";
            isUploading = true;
            //onUploadSuccessListener.onStart();
            //table = new WaypointTable(ContextUtil.getApplicationContext());
            //wayPointList = table.getAll();
            //针对最新的Matrice400飞机，需要设置一下组件索引，否则会获取不到图片
            if (ProductUtil.isM400Product()) {
                setComponentIndex();
            }
            initData();
        } else {
            ToastUtil.show("已有上传任务，此次不上传！");
        }
    }

    private void initData() {
        MediaManager.getInstance().enable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                MediaManager.getInstance().addMediaFileListStateListener(new MediaFileListStateListener() {
                    @Override
                    public void onUpdate(MediaFileListState mediaFileListState) {
                        if (mediaFileListState == MediaFileListState.UP_TO_DATE) {
                            //ToastUtil.show("刷新列表");
                            mediaFiles = MediaManager.getInstance().getMediaFileListData().getData();

                            if (downloadMediaList != null) {
                                downloadMediaList.clear();
                                newFileNameList.clear();
                            }

                            if (mediaFiles.size() <= 0) {
                                finish();
                                ToastUtil.show("图库为空");
                                return;
                            }

                            MissionDetail missionDetail = SpUtil.getMissionData(missionId);
                            for (int i = mediaFiles.size() - 1; i >= 0; i--) {
                                final MediaFile media = mediaFiles.get(i);
                                DateTime dateTime = media.getDate();
                                Calendar calendar = Calendar.getInstance();
                                calendar.set(dateTime.getYear(), dateTime.getMonth() - 1, dateTime.getDay(), dateTime.getHour(), dateTime.getMinute(), dateTime.getSecond());
                                long time = calendar.getTime().getTime();
                                if(taskInfo != null){ //从飞行历史过来的
                                    try {
                                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                        Date startTime = simpleDateFormat.parse(taskInfo.getUavStartTime());
                                        Date endTime = simpleDateFormat.parse(taskInfo.getUavEndTime());
                                        if (time > startTime.getTime() && time < endTime.getTime()) {
                                            downloadMediaList.add(media);
                                            newFileNameList.add(media.getFileName());
                                        }
                                    } catch (ParseException e) {
                                        e.printStackTrace();
                                    }
                                }else {
                                    if (time > missionDetail.getStartTime() && time < missionDetail.getEndTime()) {
                                        downloadMediaList.add(media);
                                        newFileNameList.add(media.getFileName());
                                    }
                                }

                            }

                            if (downloadMediaList.size() > 0) {
                                ToastUtil.show("匹配到几张图片" + downloadMediaList.size());
                                MediaDownloadController.getInstance().setCheckedMediaFileList(downloadMediaList, newFileNameList);
                                ContextUtil.getHandler().post(downloadRunnable);
                            } else {
                                finish();
                                onUploadSuccessListener.onError();
                                ToastUtil.show("没有匹配到数据！");
                            }
                        }
                    }
                });
                //只拉取最新的500张数据以保证执行效率
                PullMediaFileListParam param = new PullMediaFileListParam.Builder().count(500).build();
                MediaManager.getInstance().pullMediaFileListFromCamera(param, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {

                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        onUploadSuccessListener.onError();
                        ToastUtil.show("pullMediaFileListFromCamera:" + error.description());
                    }
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                onUploadSuccessListener.onError();
                ToastUtil.show("enable error:" + error.description());
            }
        });
    }

    private void setComponentIndex() {
        MediaFileListDataSource mediaFileListDataSource = new MediaFileListDataSource.Builder().setIndexType(ComponentIndexType.PORT_1).build();
        MediaDataCenter.getInstance().getMediaManager().setMediaFileDataSource(mediaFileListDataSource);
    }

    public void finish() {
        isUploading = false;
        MediaManager.getInstance().removeAllMediaFileListStateListener();
        MediaManager.getInstance().disable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("disable error:" + error.description());
            }
        });
    }

    private void upload() {
        ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                         ftpConnectionUpload = new FtpConnectionUpload(missionId, ftpPath, downloadMediaList, 0, new OnThreadResultListener() {
                            @Override
                            public void onFinish(int index) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        Log.e("FtpConnectionUpload", "onFinish: " + index);
                                        binding.downloadText.setText("总文件个数" + downloadMediaList.size() + "    当前上传第" + (index + 1) + "个 ");
                                        binding.downloadProgressBar.setProgress((int) ((index + 1) * 100 / downloadMediaList.size()));
                                        binding.downPercent.setText((int) (index * 100 / downloadMediaList.size()) + "%");

                                        if (index == downloadMediaList.size() - 1) {
                                            Log.e("FtpConnectionUpload", "结束");
                                            SpUtil.setMissionBatch("");
                                            SpUtil.remove(missionId);
                                            ToastUtil.show("开始从图库中删除任务图片");
                                            MediaManager.getInstance().removeAllMediaFileListStateListener();
                                            MediaManager.getInstance().deleteMediaFiles(downloadMediaList, new CommonCallbacks.CompletionCallback() {
                                                @Override
                                                public void onSuccess() {
                                                    ToastUtil.show("删除成功");
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }

                                                @Override
                                                public void onFailure(@NonNull IDJIError djiError) {
                                                    if (djiError != null) {
                                                        ToastUtil.show("djiError:" + djiError.description());
                                                    }
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }
                                            });
                                        }
                                    }
                                });

                            }

                            @Override
                            public void onError(int index, String error, File localFile) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.downloadText.setText("上传失败：" + index + ":" + error);
                                        ToastUtil.show("上传失败：" + index + ":" + error);
                                        //Log.e("TAG", "onNetWorkError: ");
                                        onUploadSuccessListener.onNetWorkError();
                                    }
                                });
                                /*ContextUtil.getHandler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.downloadText.setText("上传失败：" + index + ":" + error);
                                        ToastUtil.show("上传失败：" + index + ":" + error);
                                        if (!netDialog.isShowing()) {
                                            try {
                                                netDialog.show();
                                            }catch (Exception e){
                                                e.printStackTrace();
                                            }
                                        }
                                    }
                                });*/
                                Log.e("FtpConnectionUpload", "onError  index: " + index + "  error:" + error);
                            }
                        });
                        ftpConnectionUpload.start();
                    }
                }).start();
            }
        });
    }

    public void reConnectFtp(){
        if(ftpConnectionUpload != null){
            new Thread(new Runnable() {
                @Override
                public void run() {
                    ftpConnectionUpload.reConnect();
                    ftpConnectionUpload.start();
                }
            }).start();
        }
    }

    private void uploadMinio() {
        ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        minioConnectionUpload = new MinioConnectionUpload(missionId, minioPath, downloadMediaList, new OnThreadResultListener() {
                            @Override
                            public void onFinish(int index) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        XLogUtil.INSTANCE.e("minioConnectionUpload", "onFinish: " + index);
                                        binding.downloadText.setText("总文件个数" + downloadMediaList.size() + "    当前上传第" + (index + 1) + "个 ");
                                        binding.downloadProgressBar.setProgress((int) ((index + 1) * 100 / downloadMediaList.size()));
                                        binding.downPercent.setText((int) (index * 100 / downloadMediaList.size()) + "%");

                                        if (index == downloadMediaList.size() - 1) {
                                            XLogUtil.INSTANCE.e("minioConnectionUpload", "结束");
                                            SpUtil.setMissionBatch("");
                                            SpUtil.remove(missionId);
                                            ToastUtil.show("开始从图库中删除任务图片");
                                            MediaManager.getInstance().removeAllMediaFileListStateListener();
                                            MediaManager.getInstance().deleteMediaFiles(downloadMediaList, new CommonCallbacks.CompletionCallback() {
                                                @Override
                                                public void onSuccess() {
                                                    ToastUtil.show("删除成功");
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }

                                                @Override
                                                public void onFailure(@NonNull IDJIError djiError) {
                                                    if (djiError != null) {
                                                        ToastUtil.show("djiError:" + djiError.description());
                                                    }
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }
                                            });
                                        }
                                    }
                                });

                            }

                            @Override
                            public void onError(int index, String error, File localFile) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.downloadText.setText("上传失败：" + index + ":" + error);
                                        ToastUtil.show("上传失败：" + index + ":" + error);
                                        onUploadSuccessListener.onNetWorkError();
                                    }
                                });
                                XLogUtil.INSTANCE.e("minioConnectionUpload", "onError  index: " + index + "  error:" + error);
                            }
                        });
                        minioConnectionUpload.start();
                    }
                }).start();
            }
        });
    }

    public void reConnectMinio(){
        if(minioConnectionUpload != null){
            new Thread(new Runnable() {
                @Override
                public void run() {
                    minioConnectionUpload.reConnect();
                    minioConnectionUpload.start();
                }
            }).start();
        }
    }

    private void uploadOss() {
        ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        ossConnectionUpload = new OssConnectionUpload(missionId, ossPath, downloadMediaList, new OnThreadResultListener() {
                            @Override
                            public void onFinish(int index) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        XLogUtil.INSTANCE.e("ossConnectionUpload", "onFinish: " + index);
                                        binding.downloadText.setText("总文件个数" + downloadMediaList.size() + "    当前上传第" + (index + 1) + "个 ");
                                        binding.downloadProgressBar.setProgress((int) ((index + 1) * 100 / downloadMediaList.size()));
                                        binding.downPercent.setText((int) (index * 100 / downloadMediaList.size()) + "%");

                                        if (index == downloadMediaList.size() - 1) {
                                            XLogUtil.INSTANCE.e("ossConnectionUpload", "结束");
                                            SpUtil.setMissionBatch("");
                                            SpUtil.remove(missionId);
                                            ToastUtil.show("开始从图库中删除任务图片");
                                            MediaManager.getInstance().removeAllMediaFileListStateListener();
                                            MediaManager.getInstance().deleteMediaFiles(downloadMediaList, new CommonCallbacks.CompletionCallback() {
                                                @Override
                                                public void onSuccess() {
                                                    ToastUtil.show("删除成功");
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }

                                                @Override
                                                public void onFailure(@NonNull IDJIError djiError) {
                                                    if (djiError != null) {
                                                        ToastUtil.show("djiError:" + djiError.description());
                                                    }
                                                    ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            binding.downloadLL.setVisibility(View.INVISIBLE);
                                                        }
                                                    });
                                                    finish();
                                                    onUploadSuccessListener.onSuccess();
                                                }
                                            });
                                        }
                                    }
                                });

                            }

                            @Override
                            public void onError(int index, String error, File localFile) {
                                ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        binding.downloadText.setText("上传失败：" + index + ":" + error);
                                        ToastUtil.show("上传失败：" + index + ":" + error);
                                        onUploadSuccessListener.onNetWorkError();
                                    }
                                });
                                XLogUtil.INSTANCE.e("ossConnectionUpload", "onError  index: " + index + "  error:" + error);
                            }
                        });
                        ossConnectionUpload.start();
                    }
                }).start();
            }
        });
    }

    public void reConnectOss(){
        if(ossConnectionUpload != null){
            new Thread(new Runnable() {
                @Override
                public void run() {
                    ossConnectionUpload.reConnect();
                    ossConnectionUpload.start();
                }
            }).start();
        }
    }

    public String stampToDate(long timeMillis) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(timeMillis);
        return simpleDateFormat.format(date);
    }

}
