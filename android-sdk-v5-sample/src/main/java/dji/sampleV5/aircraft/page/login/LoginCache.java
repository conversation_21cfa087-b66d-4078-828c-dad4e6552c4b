package dji.sampleV5.aircraft.page.login;

/**
 * 登录成功后从服务器获得的数据, 形如:
 * {
 * "id":"xlyg",
 * "qicloudToken":"QiCloudeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySUQiOjUsIlBMQVRJRCI6IlBUMDAzIiwidXNlck5hbWUiOiJza3lzeXMwMDMiLCJDUElEIjoiNTExZWI4MGVkYTAyNzdkOWYzOTBlYTRlN2E1OGVjMDYiLCJQSUQiOiJBMjUiLCJleHAiOjE2NzAzNDAzODAsIm5iZiI6MTY3MDMxMTU3MH0.kbax4-D8CK9x1UKw64Eq2RhQ3dMz4TBhFzLI-DXkTgo",
 * "token":"asqW83fqdiGM+dwdqwfasdqJIUHI=",
 * "apiToken":"8383bd66d93a48fe8657d2dc59c55dc9",
 * "substation_option":0,
 * "photovo_option":1,
 * "registertime":"2022-06-13 13:46:59",
 * "apiUser":"skysyszl",
 * "updateTime":"2022-06-13 13:47:05",
 * "lastlogintime":"2022-12-06 14:03:40",
 * "socketIOConfig":{
 * "accessKeyId":"QSPT03HYM4TY",
 * "accessKeySecret":"SFHJHGJTUTIOERTWER231",
 * "IPAddress":"*************"
 * }
 * }
 */
public class LoginCache {

    public String getQicloudToken() {
        return qicloudToken;
    }

    public void setQicloudToken(String qicloudToken) {
        this.qicloudToken = qicloudToken;
    }

    public String getApiToken() {
        return apiToken;
    }

    public void setApiToken(String apiToken) {
        this.apiToken = apiToken;
    }

    public int getSubstation_option() {
        return substation_option;
    }

    public void setSubstation_option(int substation_option) {
        this.substation_option = substation_option;
    }

    public int getPhotovo_option() {
        return photovo_option;
    }

    public void setPhotovo_option(int photovo_option) {
        this.photovo_option = photovo_option;
    }

    public String getRegistertime() {
        return registertime;
    }

    public void setRegistertime(String registertime) {
        this.registertime = registertime;
    }

    public String getApiUser() {
        return apiUser;
    }

    public void setApiUser(String apiUser) {
        this.apiUser = apiUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getLastlogintime() {
        return lastlogintime;
    }

    public void setLastlogintime(String lastlogintime) {
        this.lastlogintime = lastlogintime;
    }

    public static class SocketIOConfig {
        public String accessKeyId;
        public String accessKeySecret;
        public String IPAddress;
        public String namespace;

        SocketIOConfig() {
            accessKeyId = "";
            accessKeySecret = "";
            IPAddress = "";
            namespace = "";
        }
    }

    public SocketIOConfig getSocketIOConfig() {
        return socketIOConfig;
    }

    public void setSocketIOConfig(SocketIOConfig socketIOConfig) {
        this.socketIOConfig = socketIOConfig;
    }

    private AiLogin aiLogin = new AiLogin();

    public static class AiLogin {
        public String userID;
        public String token;
        public String impl;
        public String logo_url;
        public String socket_address;
        public String system_name;

        AiLogin() {
            userID = "";
            token = "";
            impl = "";
            logo_url = "";
            socket_address = "";
            system_name = "";
        }
    }

    public AiLogin getAiLogin() {
        return aiLogin;
    }

    public void setAiLogin(AiLogin aiLogin) {
        this.aiLogin = aiLogin;
    }

    private String data;
    private String token;
    private long time;
    private String id;
    private String qicloudToken;
    private String apiToken;
    private int substation_option;
    private int photovo_option;
    private String registertime;
    private String apiUser;
    private String updateTime;
    private String lastlogintime;
    private String userName;
    private String pwd;
    private String adminFlag;

    private String dispatch;

    private AppVersionInfo apkVersion;

    public String getDispatch() {
        return dispatch;
    }

    public void setDispatch(String dispatch) {
        this.dispatch = dispatch;
    }

    public String getAdminFlag() {
        return adminFlag;
    }

    public void setAdminFlag(String adminFlag) {
        this.adminFlag = adminFlag;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    private SocketIOConfig socketIOConfig = new SocketIOConfig();

    public String getUser() {
        return apiUser;
    }

    public void setUser(String user) {
        this.apiUser = user;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }


    public static class AppVersionInfo {
        public String platform;
        public String version;
        public String code;
        public String des;
        public String path;

        public String url;
        public String createTime;
        public String updateTime;
        public int size;

        AppVersionInfo() {
            platform = "";
            version = "";
            code = "";
            des = "";
            path = "";
            url = "";
            createTime = "";
            updateTime = "";
            size = 0;
        }
    }

    public AppVersionInfo getApkVersion() {
        return apkVersion;
    }

    public void setApkVersion(AppVersionInfo apkVersion){
        this.apkVersion = apkVersion;
    }
}
