package dji.sampleV5.aircraft.page.fly.setting.pager;/*
package dji.sampleV5.aircraft.page.fly.setting.pager;

import android.view.LayoutInflater;

import androidx.databinding.DataBindingUtil;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.databinding.MenuHdPagerBinding;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.page.fly.setting.AircraftSettingFragment;
import com.skysys.fly.page.fly.setting.pager.detail.HDFrequencySet;
import com.skysys.fly.page.fly.setting.pager.detail.HDMISDIVideoOutPut;
import com.skysys.fly.page.fly.setting.pager.detail.HDSignalChannel;
import com.skysys.fly.page.fly.setting.pager.detail.HDSignalChannelManual;
import com.skysys.fly.page.fly.setting.pager.detail.HDVideoOutputFormat;
import com.skysys.fly.page.fly.setting.pager.detail.HDVideoOutputMode;


public class LightBrigeTransmissionPager extends BasePager {

    private HDSignalChannel signalChannel;
    private HDFrequencySet hdFrequencySet;
    private HDVideoOutputMode hdVideoOutputMode;
    private HDVideoOutputFormat videoOutputFormat;
    private HDMISDIVideoOutPut hdmisdiVideoOutPut;
    private HDSignalChannelManual hdSignalChannelManual;

    private MenuHdPagerBinding binding;

    public LightBrigeTransmissionPager(AircraftSettingFragment activity) {
        super(activity);
    }

    @Override
    public void initData() {
        activityMenuPresenter.setIsPrevious(false);
        isLoading = true;
        tvTitle.setText(ContextUtil.getString(R.string.hd_set_pager));

        binding = DataBindingUtil.inflate(LayoutInflater.from(fragment.getActivity()), R.layout.menu_hd_pager, null, false);
        binding.setActivityMenuPresenter(activityMenuPresenter);

        signalChannel = new HDSignalChannel(binding, (AircraftActivity) fragment.getActivity());
        activityMenuPresenter.currentHDSignalChannel(signalChannel);
        signalChannel.setSignalChannel();
        hdSignalChannelManual = new HDSignalChannelManual(binding);
        hdSignalChannelManual.setHDSignalChannelManual();
        hdFrequencySet = new HDFrequencySet(binding, (AircraftActivity) fragment.getActivity(), signalChannel);
        videoOutputFormat = new HDVideoOutputFormat(binding, (AircraftActivity) fragment.getActivity());
        hdmisdiVideoOutPut = new HDMISDIVideoOutPut(binding, activityMenuPresenter, (AircraftActivity) fragment.getActivity());

        hdVideoOutputMode = new HDVideoOutputMode(binding, (AircraftActivity) fragment.getActivity());


        //添加到帧布局
        flContainer.addView(binding.getRoot());
    }

    @Override
    public void removeListener() {
        if (isLoading) {
            hdmisdiVideoOutPut.remove();
            videoOutputFormat.remove();
            hdFrequencySet.remove();
            signalChannel.remove();
            hdVideoOutputMode.remove();
        }
    }

    @Override
    public void isConnect(boolean connect) {
        if (isLoading) {
            hdmisdiVideoOutPut.connect(connect);
            videoOutputFormat.isConnect(connect);
            if (connect) {
                hdSignalChannelManual.setLBAirLinkDataRate();
                videoOutputFormat.initData();
            } else {
                activityMenuPresenter.setHDOSDISOPEN(false);
            }
            hdVideoOutputMode.isConnect(connect);
            signalChannel.connect(connect);
            hdFrequencySet.setconnect(connect);
        }
    }
}
*/
