package dji.sampleV5.aircraft.page;

import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivitySitelistBinding;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.event.Message;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.DialogExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.net.bean.SituationInfoBean;
import dji.sampleV5.aircraft.mvvm.ui.activity.operate.NewOperateActivity;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.qicloud.ListItem;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.v5.utils.common.LogUtils;
import io.socket.client.IO;
import io.socket.client.Socket;
import io.socket.emitter.Emitter;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SiteListActivity extends AppCompatActivity implements CancelAdapt {
    /**
     * 循环任务周期
     */
    private static final int INTERVAL_DELAY = 5000;
    /**
     * 最大无响应时间
     */
    private static final int MAX_NO_RESPONSE_TIME = 30000;

    private final String TAG = LogUtils.getTag(this);
    private TaskAdapter adapter;
    private Map<String, ListItemWrap> itemsMap;
    private Socket mSocket;
    private Timer mTimer;
    private TimerTask mTimerTask;
    private ActivitySitelistBinding binding;
    //站点监听器
    private Map<String, Emitter.Listener> mSiteListeners = new HashMap<>();

    /**
     * 接受服务器端数据
     */
    private Callback<ResponseBody> createDateHandler() {
        return new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                try {
                    JSONObject resp = JSONObject.parseObject(response.body().string());
                    int code = resp.getInteger("code");
                    if (code != 0) {
                        ToastUtil.show("加载数据失败");
                        return;
                    }

                    JSONObject data = resp.getJSONObject("data");
                    if (data == null) {
                        return;
                    }

                    JSONArray list = data.getJSONArray("list");
                    if (list == null) {
                        ToastUtil.show("加载数据失败");
                        return;
                    }

                    SiteListActivity.this.onListItems(list);
                } catch (NullPointerException | IOException e) {
                    e.printStackTrace();
                    ToastUtil.show("加载数据失败");
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                Log.e(TAG, "onFailure:" + t.toString());
                ToastUtil.show("加载数据失败");
            }
        };
    }

    /**
     * 监听平台态势数据变化
     */
    private Emitter.Listener createSituationChangeListener() {
        return new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                if (args[0] != null) {
                    try {
                        org.json.JSONObject json = (org.json.JSONObject) args[0];
                        String situationStr = json.optString("data");
                        List<SituationInfoBean> situationInfoBeans = new Gson().fromJson(situationStr, new TypeToken<List<SituationInfoBean>>() {}.getType());
                        LiveDataEvent.INSTANCE.getAllSituationInfo().postValue(situationInfoBeans);
                        Log.e("situationInfo", situationInfoBeans.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        };
    }

    /**
     * 监听所有站点的变化
     */
    private Emitter.Listener createSiteChangeListener() {
        return new Emitter.Listener() {
            @Override
            public void call(Object... args) {
                try {
                    org.json.JSONObject json = (org.json.JSONObject) args[0];
//                    Log.e("siteInfo", CommExtKt.toJsonStr(json));
                    String STID = json.getString("STID");
                    ListItemWrap target = itemsMap.get(STID);
                    if (target == null) {
                        return;
                    }
                    target.call(args);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        if (NewOperateActivity.Companion.isCreate()) {
                            Message message = new Message();
                            message.setTag(ValueKey.WEB_SOCKET_INFO);
                            message.setObj(args[0]);
                            LiveDataEvent.INSTANCE.getControlSocketInfo().postValue(message);
                            EventBus.getDefault().post(message);
                        }
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        };
    }

    void onInterval() {
        this.mTimer.schedule(this.mTimerTask, INTERVAL_DELAY);
    }

    private void setupSocket(LoginCache loginCache) {
        LoginCache.SocketIOConfig socketIOConfig = loginCache.getSocketIOConfig();
        if (socketIOConfig == null) {
            ToastUtil.show("远程连接创建失败");
            return;
        }
        try {
            String query = String.format("accessKeyId=%s&accessKeySecret=%s", socketIOConfig.accessKeyId, socketIOConfig.accessKeySecret);
            String address = String.format("%s%s", socketIOConfig.IPAddress, socketIOConfig.namespace);
            Log.e(TAG, String.format("setupSocket query:%s address:%s", query, address));

            IO.Options opts = IO.Options.builder()
                    .setQuery(query)
                    .setReconnection(false)
                    .setTransports(new String[]{"websocket", "polling"})
                    .setUpgrade(true)
                    .build();
            mSocket = IO.socket(address, opts);
            mSocket.on(Socket.EVENT_CONNECT_ERROR, new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    Log.e(TAG, String.format("EVENT_CONNECT_ERROR %s", args[0]));
                }
            });

            mSocket.on(Socket.EVENT_CONNECT, new Emitter.Listener() {
                @Override
                public void call(Object... args) {
//                    Log.e(TAG, "socket------>连接成功");
                    Log.d(TAG, "活跃监听器数量: " + mSiteListeners.size());
                }
            });
            mSocket.on("site state", createSiteChangeListener());
            mSocket.on("user login", new Emitter.Listener() {
                @Override
                public void call(Object... args) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ToastUtil.show("账号在其他设备登录");
                            Log.e(TAG, "socket.user login");
                        }
                    });
                }
            });
            mSocket.on("dji device command", createSituationChangeListener());

            mSocket.connect();
        } catch (URISyntaxException e) {
            ToastUtil.show("远程连接创建失败");
            e.printStackTrace();
        }
    }

    /**
     * 成功获得到列表数据后, 绑定 socket 监听
     */
    private void onListItems(JSONArray list) {
        List<ListItem> parsed = list.toJavaList(ListItem.class);
        Log.e(TAG, String.format("onListItems parsed:%d", parsed.size()));

        List<ListItemWrap> next = new ArrayList<>();
        Map<String, ListItemWrap> nextMap = new HashMap<>();
        for (ListItem item : parsed) {
            Log.e(TAG, String.format("onListItems each:%s, item:%s", next.size(), JSONObject.toJSONString(item)));
            ListItemWrap wrap = new ListItemWrap(this, adapter, item, next.size());
            //System.out.println(">>" + i.siteName); // todo 解除注释方便调试
            // 智慧魔方,临港M210
            //if ("智慧魔方".equals(i.siteName)) {
            //    next.add(wrap);
            //}
            next.add(wrap);
            nextMap.put(item.siteID, wrap);
        }

        // 切换列表
        this.itemsMap = nextMap;

        // adapter 接受新数据
        adapter.loadData(next);
        new Handler(Looper.getMainLooper()).post(() ->
                registerSiteListener(nextMap.keySet()));

    }

    /**
     * item 点击时调用
     */
    private void onItemClick(View view) {
        /*ListItemWrap wrap = (ListItemWrap) view.getTag();
        if (wrap == null) {
            return;
        }
        Log.e(TAG, String.format("onItemClick siteID: %s", wrap.getItem().siteID));
        ToastUtil.show(String.format("[%s]被点击了", wrap.getItem().siteID));*/
    }

    private void registerSiteListener(Set<String> siteIDs) {
        //清理旧监听器
        for (String oldSiteID : mSiteListeners.keySet()) {
            if (!siteIDs.contains(oldSiteID)) {
                mSocket.off(oldSiteID + " state", mSiteListeners.get(oldSiteID));
                mSiteListeners.remove(oldSiteID);
            }
        }
        //注册新监听器
        for (String siteID : siteIDs) {
            String eventName = siteID + " state";
            if (!mSiteListeners.containsKey(siteID)) {
                Emitter.Listener listener = createSiteChangeListener();
                mSocket.on(eventName, listener);
                mSiteListeners.put(siteID, listener);
                Log.d(TAG, "注册监听器: " + eventName);
            }
        }
    }
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
        ImmerseUtil.fullScreen(this);
        // 机场项目
        itemsMap = new HashMap<>();

        // ui binding
        binding = DataBindingUtil.setContentView(this, R.layout.activity_sitelist);
        binding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        LoginCache loginCache = SpUtil.getLoginCache();
        Log.e(TAG, "onCreate:" + loginCache.getQicloudToken());

        // 创建 socket 连接
        setupSocket(loginCache);

        // 创建 adapter
        adapter = new TaskAdapter(this::onItemClick);

        // 修饰 recycleView
        int o = getResources().getConfiguration().orientation;
        int dOri = o == Configuration.ORIENTATION_LANDSCAPE ? DividerItemDecoration.HORIZONTAL : DividerItemDecoration.VERTICAL;
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.recyclerView.getContext(), dOri);
        binding.recyclerView.setLayoutManager(new GridLayoutManager(this, 2));
        binding.recyclerView.setAdapter(adapter);
        // binding.recyclerView.addItemDecoration(dividerItemDecoration);

        // 拉取站点列表
       /* QiCloud.init(loginCache);
        QiCloud.getQcpSitesList().enqueue(createDateHandler());*/
        getSiteList();

        // 定时器存在的意义是查看, 如果有一个站点 30s 没有监听到任何事件, 标记此站点为离线
        this.mTimer = new Timer();
        this.mTimerTask = new TimerTask() {
            @Override
            public void run() {
                long earliest = new Date().getTime() - MAX_NO_RESPONSE_TIME;
                for (ListItemWrap i : itemsMap.values()) {
                    i.checkOffline(earliest);
                }
            }
        };

        // 启动定时器
        onInterval();
    }

    private void getSiteList(){
        LoginCache loginCache = SpUtil.getLoginCache();
        JSONObject data = new JSONObject();
        data.put("page", 1);
        data.put("size", 100);
        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = ApiConfig.INSTANCE.getMIX_URL() + ApiConfig.SITE_LIST;
        Request request = new Request.Builder()
                .url(url)
                .addHeader("ProductId", ApiConfig.INSTANCE.getPID())
                .addHeader("ApiToken",loginCache.getApiToken())
                .post(requestBody)
                .build();

        okhttp3.Call call = httpClient.newCall(request);
        DialogExtKt.showLoadingExt(SiteListActivity.this, "正在加载站点数据", null);
        call.enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(okhttp3.Call call, IOException e) {
                DialogExtKt.dismissLoadingExt(SiteListActivity.this);
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                DialogExtKt.dismissLoadingExt(SiteListActivity.this);
                //在这里根据返回内容执行具体的操作
                //Log.e("TAG", "login onResponse: " + response.body().string());
                String result = response.body().string();
                Log.e("TAG", "login onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                       /* JSONObject data = jsonObject.getJSONObject("data");
                        if (data == null) {
                            ToastUtil.show("加载数据失败");
                            return;
                        }*/

                        JSONArray list = jsonObject.getJSONArray("data");
                        if (list == null) {
                            ToastUtil.show("加载数据失败");
                            return;
                        }

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                SiteListActivity.this.onListItems(list);
                            }
                        });
                    } else {
                        ToastUtil.show(jsonObject.getString("message"));
                    }
                } catch (com.alibaba.fastjson.JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onBackPressed() {
        finish();
    }

    @Override
    public void onDetachedFromWindow() {
        // 清理所有监听器 ▼
        for (Map.Entry<String, Emitter.Listener> entry : mSiteListeners.entrySet()) {
            mSocket.off(entry.getKey() + " state", entry.getValue());
        }
        mSiteListeners.clear();
        super.onDetachedFromWindow();
        // 结束 webSocket
        if (this.mSocket != null) {
            this.mSocket.disconnect();
            this.mSocket = null;
        }

        // 结束定时器
        this.mTimer.cancel();
    }
}
