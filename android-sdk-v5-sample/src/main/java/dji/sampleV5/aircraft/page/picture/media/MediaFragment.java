package dji.sampleV5.aircraft.page.picture.media;

import android.app.ProgressDialog;
import android.media.ExifInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.amap.api.maps.model.LatLng;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.FragmentMediaPictureBinding;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.view.NoScrollViewPager;
import dji.sampleV5.aircraft.view.adapter.EasyViewPagerAdapter;
import dji.sdk.keyvalue.value.camera.DateTime;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.MediaDataCenter;
import dji.v5.manager.datacenter.media.MediaFile;
import dji.v5.manager.datacenter.media.MediaFileListDataSource;
import dji.v5.manager.datacenter.media.MediaFileListState;
import dji.v5.manager.datacenter.media.MediaFileListStateListener;
import dji.v5.manager.datacenter.media.MediaManager;
import dji.v5.manager.datacenter.media.PullMediaFileListParam;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


public class MediaFragment extends Fragment implements View.OnClickListener {

    private static final int NEED_REFRESH_FILE_LIST = 1;
    private static final int SHOW_PROGRESS_DIALOG = 2;
    private static final int HIDE_PROGRESS_DIALOG = 3;
    private static final int NOT_PICTURE = 4;
    private static final int HAVE_PICTURE = 5;

    //private Camera camera;
    private ProgressDialog dialog;
    private MediaObserver observer;
    private MediaManager mediaManager;
    private PictureActivity activity;
    private FragmentMediaPictureBinding binding;
    private MediaFragmentPresenter presenter;
    private NoScrollViewPager vpMediaFragment;

    private int count = 0;
    private List<MediaFile> mediaFiles;
    private ArrayList<MediaFile> mediaFileList;
    private ArrayList<MediaBean> mediaList = new ArrayList<>();
    private ArrayList<MediaBaseFragment> FragmentList = new ArrayList<>();
    private boolean isLoad = false;
    //private SettingsDefinitions.StorageLocation mediaType = SettingsDefinitions.StorageLocation.SDCARD;

    public static MediaFragment newInstance() {
        return new MediaFragment();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_media_picture, container, false);
        presenter = new MediaFragmentPresenter();
        binding.setMediaFragmentPresenter(presenter);
        activity = (PictureActivity) getActivity();

        initData();
        return binding.getRoot();
    }


    private LatLng getPointFromFile(String filePath) {
        LatLng point = null;
        try {
            ExifInterface exifInterface = new ExifInterface(filePath);
            String latValue = exifInterface.getAttribute(ExifInterface.TAG_GPS_LATITUDE);
            String lngValue = exifInterface.getAttribute(ExifInterface.TAG_GPS_LONGITUDE);
            String latRef = exifInterface.getAttribute(ExifInterface.TAG_GPS_LATITUDE_REF);
            String lngRef = exifInterface.getAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF);

            if (latValue != null && latRef != null && lngValue != null && lngRef != null) {
                double lat = convertRationalLatLonToFloat(latValue, latRef);
                double lon = convertRationalLatLonToFloat(lngValue, lngRef);
                point = new LatLng(lat, lon);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        return point;
    }

    public static double convertRationalLatLonToFloat(
            String rationalString, String ref) {
        String[] parts = rationalString.split(",");

        String[] pair;
        pair = parts[0].split("/");
        double degrees = Double.parseDouble(pair[0].trim())
                / Double.parseDouble(pair[1].trim());

        pair = parts[1].split("/");
        double minutes = Double.parseDouble(pair[0].trim())
                / Double.parseDouble(pair[1].trim());

        pair = parts[2].split("/");
        double seconds = Double.parseDouble(pair[0].trim())
                / Double.parseDouble(pair[1].trim());

        double result = degrees + (minutes / 60.0) + (seconds / 3600.0);
        if ((ref.equals("S") || ref.equals("W"))) {
            return -result;
        }
        return result;
    }
/*
    @Override
    protected void onConnected() {
        super.onConnected();
        if (!isAirCraftConnected) {
            finish();
        }
    }*/

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        finish();
    }

    private void initData() {

        mediaList.clear();
        initProgressDialog();
        showProgressDialog();
        //针对最新的Matrice400飞机，需要设置一下组件索引，否则会获取不到图片
        if (DJIAircraftApplication.getInstance().getProductType() == ProductType.DJI_MATRICE_400) {
            setComponentIndex();
        }
        MediaManager.getInstance().enable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                MediaManager.getInstance().addMediaFileListStateListener(new MediaFileListStateListener() {
                    @Override
                    public void onUpdate(MediaFileListState mediaFileListState) {
                        if (mediaFileListState == MediaFileListState.UP_TO_DATE && !isLoad) {
                            isLoad = true;
                            mediaFiles = MediaManager.getInstance().getMediaFileListData().getData();

                            if (mediaList != null) {
                                mediaList.clear();
                            }
                            if (mediaFiles.size() <= 0) {
                                ToastUtil.show("没有找到图片");
                                handleMessage(NOT_PICTURE);
                                handleMessage(HIDE_PROGRESS_DIALOG);
                            } else {
                                handleMessage(HAVE_PICTURE);
                            }
                            for (int i = 0; i < mediaFiles.size(); i++) {
                                //for (int i = mediaFiles.size() - 1; i >= 0; i--) {
                                final MediaFile media = mediaFiles.get(i);
                               /* ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        DateTime dateTime = media.getDate();
                                        Calendar calendar = Calendar.getInstance();
                                        calendar.set(dateTime.getYear(),dateTime.getMonth(),dateTime.getDay(),dateTime.getHour(),dateTime.getMinute(),dateTime.getSecond());
                                        Log.e("TAG", "run: "+dateTime.getYear()+"/"+dateTime.getMonth()+"/"+dateTime.getDay()+"/"+dateTime.getHour()+"/"+dateTime.getMinute()
                                                +"/"+dateTime.getSecond());
                                        Calendar caleEnd = Calendar.getInstance();
                                        caleEnd.set(2016, 12, 16, 11, 19, 00);
                                        Date dateEnd = caleEnd.getTime();
                                        long timeEnd = dateEnd.getTime();
                                        String ctime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd.getTime());

                                        Log.e("TAG", "run: name "+media.getFileName()+"   time:"+calendar.getTimeInMillis()+"  "+"  "+ctime +"   date:"+timeEnd);
                                        //ToastUtil.show("" + media.getFileName() + "   /" + media.getFileSize());
                                    }
                                });*/
                                MediaBean mediaBean = new MediaBean();
                                String fileName = media.getFileName();
                                int mediaType = media.getFileType().value();
                                float durationInSeconds = media.getDuration();
                                String dateCreated = media.getDate().toString();
                                mediaBean.setMedia(media);
                                mediaBean.setFileName(fileName);
                                mediaBean.setDateCreated(dateCreated);
                                mediaBean.setDurationInSeconds(durationInSeconds);
                                mediaBean.setMediaType(mediaType);
                                mediaList.add(mediaBean);
                                count++;
                                if (count >= mediaFiles.size()) {
                                    handleMessage(NEED_REFRESH_FILE_LIST);
                                    handleMessage(HIDE_PROGRESS_DIALOG);
                                }
                            }
                        }
                    }
                });

                MediaManager.getInstance().pullMediaFileListFromCamera(new PullMediaFileListParam.Builder().build(), new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {

                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("pullMediaFileListFromCamera:" + error.description());
                    }
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                hideProgressDialog();
                ToastUtil.show("enable error:" + error.description());
            }
        });

        vpMediaFragment = binding.vpMediaFragment;

        binding.ivBack.setOnClickListener(this);
        binding.rlMediaDelete.setOnClickListener(this);
        binding.tvMediaSelect.setOnClickListener(this);
        binding.rlMediaDownload.setOnClickListener(this);

        binding.tvMediaAll.setOnClickListener(this);
        binding.tvMediaImage.setOnClickListener(this);
        binding.tvMediaVideo.setOnClickListener(this);
    }

    private void initView() {
        EasyViewPagerAdapter<MediaBaseFragment> viewPagerAdapter = new EasyViewPagerAdapter<MediaBaseFragment>(FragmentList) {
            @Override
            public View getView(MediaBaseFragment mediaBaseFragment, int position) {
                return mediaBaseFragment.rootView;
            }

            @Override
            public void setPrimaryItemData(ViewGroup container, int position) {

            }
        };
        vpMediaFragment.setAdapter(viewPagerAdapter);
        vpMediaFragment.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                MediaBaseFragment mediaBaseFragment = FragmentList.get(position);
                Boolean isLoading = mediaBaseFragment.isLoading;
                if (isLoading) {
                    mediaBaseFragment.initData();
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
        MediaBaseFragment mediaBaseFragment = FragmentList.get(0);
        mediaBaseFragment.initData();
        vpMediaFragment.setCurrentItem(0);
        presenter.setTabAll(true);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.iv_back:
                finish();
                break;
            case R.id.tv_media_all:
                vpMediaFragment.setCurrentItem(0);
                notSelect();
                presenter.setTabAll(true);
                break;
            case R.id.tv_media_image:
                vpMediaFragment.setCurrentItem(1);
                notSelect();
                presenter.setTabImage(true);
                break;
            case R.id.tv_media_video:
                vpMediaFragment.setCurrentItem(2);
                notSelect();
                presenter.setTabVideo(true);
                break;
            case R.id.tv_media_select:
                Boolean selectDelete = presenter.selectDelete;
                if (selectDelete) {
                    binding.tvMediaSelect.setText(ContextUtil.getString(R.string.select));
                } else {
                    binding.tvMediaSelect.setText(ContextUtil.getString(R.string.deselect));
                }
                presenter.setSelectDelete(!selectDelete);
                if (observer != null) {
                    observer.setSelect(!selectDelete);
                }
                break;
            case R.id.rl_media_delete:
                int currentItem = vpMediaFragment.getCurrentItem();
                if (observer != null) {
                    observer.setDeleteMedia(currentItem);
                }
                break;
            case R.id.rl_media_download:
                int current = vpMediaFragment.getCurrentItem();
                if (observer != null) {
                    observer.setDownloadMedia(current);
                }
                break;
        }
    }

    public void notSelect() {
        presenter.setTabAll(false);
        presenter.setTabImage(false);
        presenter.setTabVideo(false);
    }

    public void finish() {
        MediaManager.getInstance().removeAllMediaFileListStateListener();
        MediaManager.getInstance().disable(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("退出相机模式");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("disable error:" + error.description());
            }
        });
        getActivity().finish();
        /*AircraftFragmentManager manager = ((AircraftActivity) getActivity()).getAircraftFragmentManager();
        manager.removeFragment(AircraftFragmentManager.MEDIA_FRAGMENT);*/
    }

    private void initProgressDialog() {
        dialog = new ProgressDialog(activity);
        dialog.setMessage(ContextUtil.getString(R.string.message_waiting));
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
    }

    private void setComponentIndex() {
        MediaFileListDataSource mediaFileListDataSource = new MediaFileListDataSource.Builder().setIndexType(ComponentIndexType.PORT_1).build();
        MediaDataCenter.getInstance().getMediaManager().setMediaFileDataSource(mediaFileListDataSource);
    }

    private void showProgressDialog() {
        if (dialog != null) {
            dialog.show();
        }
    }

    private void hideProgressDialog() {
        if (null != dialog && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    public MediaObserver getData() {
        if (observer != null) {
            return observer;
        }
        return null;
    }

    private void handleMessage(int message){
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (message) {
                    case NEED_REFRESH_FILE_LIST:
                        //观察者模式
                        observer = new MediaObserver();
                        MediaTabALLFragment mediaTabALLFragment = new MediaTabALLFragment(activity, mediaList, observer);
                        MediaTabImageFragment mediaTabImageFragment = new MediaTabImageFragment(activity, mediaList, observer);
                        MediaTabVideoFragment mediaTabVideoFragment = new MediaTabVideoFragment(activity, mediaList, observer);
                        //添加数据
                        FragmentList.add(mediaTabALLFragment);
                        FragmentList.add(mediaTabImageFragment);
                        FragmentList.add(mediaTabVideoFragment);
                        //添加观察者
                        observer.registerObserver(mediaTabALLFragment);
                        observer.registerObserver(mediaTabImageFragment);
                        observer.registerObserver(mediaTabVideoFragment);
                        initView();
                        break;
                    case SHOW_PROGRESS_DIALOG:
                        showProgressDialog();
                        break;
                    case HIDE_PROGRESS_DIALOG:
                        hideProgressDialog();
                        break;
                    case NOT_PICTURE:
                        presenter.setEmpty(true);
                        break;
                    case HAVE_PICTURE:
                        presenter.setEmpty(false);
                        break;
                }
            }
        });

    }
}
