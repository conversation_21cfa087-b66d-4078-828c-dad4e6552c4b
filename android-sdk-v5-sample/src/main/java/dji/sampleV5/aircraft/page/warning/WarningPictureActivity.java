package dji.sampleV5.aircraft.page.warning;

import android.annotation.SuppressLint;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.widget.PopupWindowCompat;
import androidx.databinding.DataBindingUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.bumptech.glide.Glide;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.ActivityWarningPictureBinding;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.DensityUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.sampleV5.aircraft.view.adapter.EasyAdapter;
import dji.sampleV5.aircraft.view.adapter.EasyHolder;
import dji.sampleV5.aircraft.view.adapter.TaskAdapter;
import dji.sampleV5.aircraft.view.pullToRefresh.PullToRefreshBase;
import dji.sdk.keyvalue.value.flightcontroller.FailsafeAction;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class WarningPictureActivity extends AppCompatActivity implements View.OnSystemUiVisibilityChangeListener, CancelAdapt {
    private ActivityWarningPictureBinding binding;
    private SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private List<WarningPictureData.PictureDetail> list = new ArrayList<>();
    private MyAdapter myAdapter;
    private int page = 1;

    private float oldDistance;//刚按下时双指之间的距离
    private float newDistance;//在屏幕上滑动后双指之间的距离
    private float scalePoint;//缩放中心点
    private float scale = 1f;//缩放比
    private float translationX;//x轴移动量
    private float translationY;//y轴位移量
    private float oldCenterX;//刚按下时双指之间的点的x坐标
    private float oldCenterY;//刚按下时双指之间的点的y坐标
    private float newCenterX;//在屏幕上滑动后双指之间的点的x坐标
    private float newCenterY;//在屏幕上滑动后双指之间的点的y坐标

    private TextView TVAlgorithm;
    private ListView mListView;
    private PopupWindow mPopupWindow;
    private final ArrayList<String> listAlgorithms = new ArrayList<>();
    private int currentPosition = 0;
    private EasyAdapter easyAdapter;
    private String currentAlgorithm;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(this);
        ImmerseUtil.startImmerse(this);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_warning_picture);
        binding.ivBack.setOnClickListener(v -> {
            finish();
        });
        binding.gridview.setOnLastItemVisibleListener(new PullToRefreshBase.OnLastItemVisibleListener() {
            @Override
            public void onLastItemVisible() {
                Log.e("TAG", "onLastItemVisible");
                page++;
                initData(currentAlgorithm);
            }
        });

        binding.gridview.setOnRefreshListener(new PullToRefreshBase.OnRefreshListener<GridView>() {
            @Override
            public void onRefresh(PullToRefreshBase<GridView> refreshView) {
                reGetData();
            }
        });

        binding.gridview.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                binding.imgUrl.setText(list.get(position).getImg_url());
                Glide.with(ContextUtil.getApplicationContext()).load(list.get(position).getImg_url()).into(binding.imgMiddile);
                binding.rlBack.setVisibility(View.VISIBLE);
            }
        });

        binding.rlBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.rlBack.setVisibility(View.GONE);
            }
        });

        binding.imgMiddile.setOnTouchListener((v, event) -> {
            switch (event.getActionMasked()) {
                case MotionEvent.ACTION_POINTER_DOWN:
                    Log.d("TAG", "onTouch: pointer down");
                    if (event.getPointerCount() == 2) {//getPointerCount返回的是手指的数量
                        oldDistance = calculateDistance(event);//计算距离
                        oldCenterX = calculateCenter(event, true);//计算两指之间的中心点的x坐标
                        oldCenterY = calculateCenter(event, false);//计算两指之间的中心点的y坐标
                    }
                    break;
                case MotionEvent.ACTION_MOVE:
                    Log.d("TAG", "onTouch: move");
                    if (event.getPointerCount() == 2) {
                        newDistance = calculateDistance(event);
                        scale += (newDistance - oldDistance) / oldDistance;
                        newCenterX = calculateCenter(event, true);
                        newCenterY = calculateCenter(event, false);
                        //缩放
                        binding.imgMiddile.setScaleX(scale);
                        binding.imgMiddile.setScaleY(scale);
                        //位移
                        translationX += newCenterX - oldCenterX;
                        translationY += newCenterY - oldCenterY;
                        binding.imgMiddile.setTranslationX(translationX);
                        binding.imgMiddile.setTranslationY(translationY);
                    }
                    break;
            }
            return true;
        });

        TVAlgorithm = binding.tvAlgorithm;
        TVAlgorithm.setOnClickListener(v -> {
            View anchor = TVAlgorithm;
            showPopup(anchor, mListView, listAlgorithms.size());
        });
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);

        binding.gridview.getRefreshableView().setNumColumns(3);
        myAdapter = new MyAdapter();
        binding.gridview.setAdapter(myAdapter);
        getScene();
    }

    private void setListIntoMode(int integer) {
        currentAlgorithm = listAlgorithms.get(integer);
        TVAlgorithm.setText(currentAlgorithm);
        reGetData();
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listAlgorithms) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {
                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position == currentPosition) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        } else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition = position;
            easyAdapter.notifyDataSetChanged();
            currentAlgorithm = listAlgorithms.get(currentPosition);
            TVAlgorithm.setText(currentAlgorithm);
            reGetData();
            dismissPopupWindow();
        });
    }

    public void dismissPopupWindow() {
        if (mPopupWindow != null) {
            mPopupWindow.dismiss();
        }
    }

    public void showPopup(View anchor, ListView listView, int length) {
        if (listView != null) {
            if (length == 3) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(690));
            } else if (length == 2) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(460));
            } else if (length <= 1) {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(230));
            } else {
                mPopupWindow = new PopupWindow(listView, anchor.getWidth(), DensityUtil.px2dp(900));
            }
            mPopupWindow.setFocusable(false);
            mPopupWindow.setBackgroundDrawable(new ColorDrawable());
            mPopupWindow.setOutsideTouchable(true);
            mPopupWindow.setFocusable(false);
            mPopupWindow.update();
            PopupWindowCompat.showAsDropDown(mPopupWindow, anchor, 0, -anchor.getHeight(), Gravity.CENTER);
            ImmerseUtil.fullScreenImmersive(mPopupWindow.getContentView());
            mPopupWindow.setFocusable(true);
            mPopupWindow.update();
        }
    }

    private float calculateDistance(MotionEvent motionEvent) {
        float x1 = motionEvent.getX(0);//第一个点x坐标
        float x2 = motionEvent.getX(1);//第二个点x坐标
        float y1 = motionEvent.getY(0);//第一个点y坐标
        float y2 = motionEvent.getY(1);//第二个点y坐标
        return (float) Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
    }

    /*
     *@param isX 是否是x坐标
     */
    private float calculateCenter(MotionEvent motionEvent, boolean isX) {
        return isX ? (motionEvent.getX(1) + motionEvent.getX(0)) / 2 : (motionEvent.getY(1) + motionEvent.getY(0)) / 2;
    }


    private class MyAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return list.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View view;
            ViewHolder viewHolder;
            if (convertView == null) {
                view = getLayoutInflater().inflate(R.layout.item_warning_picture, null);
                viewHolder = new ViewHolder();
                viewHolder.picture = view.findViewById(R.id.img);
                viewHolder.tvTime = view.findViewById(R.id.time);
                view.setTag(viewHolder);
            } else {
                view = convertView;
                //取出ViewHolder
                viewHolder = (ViewHolder) view.getTag();
            }

            WarningPictureData.PictureDetail pictureDetail = list.get(position);
            viewHolder.tvTime.setText(pictureDetail.getEvent_time() + "   " + pictureDetail.getCls());
            Glide.with(ContextUtil.getApplicationContext()).load(pictureDetail.getImg_url()).into(viewHolder.picture);
            return view;
        }
    }

    static class ViewHolder {
        ImageView picture;
        TextView tvTime;
    }

    private void getScene() {
        LoginCache loginCache = SpUtil.getLoginCache();
//        String url = NetConfig.LGURl2 + "AI/WX/Sence/Get";
        //老李提供的新接口地址
        String url = ApiConfig.INSTANCE.getAI_URL() + ApiConfig.GET_USER_SENCE;
        Request request = new Request.Builder().
                addHeader("token", loginCache.getToken()).
                addHeader("user", loginCache.getUser())
                .url(url)
                .get()
                .build();
        OkHttpClient client = new OkHttpClient();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.e("onFailure", e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                String result = response.body().string();
                Log.e("TAG", "getScene onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "操作成功")) {
                        JSONArray data = jsonObject.getJSONArray("data"); //{"code":200,"message":"操作成功","data":[{"name":"管线巡检"}]}
                        if(data == null || data.size() == 0){
                            ToastUtil.show("没有获取到算法场景！");
                            return;
                        }
                        String[] algorithms = new String[data.size()];
                        for (int i = 0; i < data.size(); i++) {
                            listAlgorithms.add(data.getJSONObject(i).getString("name"));
                            //algorithms[i] = data.getJSONObject(i).getString("name");
                        }
                        runOnUiThread(() -> {
                            setListIntoMode(0);
                            //binding.algorithmSpinner.setData(algorithms);
                        });
                    } else {
                        ToastUtil.show(jsonObject.getString("message"));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void reGetData(){
        list.clear();
        page = 1;
        initData(currentAlgorithm);
    }

    private void initData(String algorithm) {
        binding.progressBar.setVisibility(View.VISIBLE);
        LoginCache loginCache = SpUtil.getLoginCache();
        if(TextUtils.isEmpty(loginCache.getAdminFlag())){
            ToastUtil.show("用户信息过期，请重新登录");
            return;
        }
//        String url = NetConfig.LGURl2 + "AI/WX/Defect/History";
        //老李提供的新接口地址
        String url = ApiConfig.INSTANCE.getAI_URL() + ApiConfig.WARNING_HISTORY;
        StringBuilder builder = new StringBuilder(url);
        builder.append("?");
        Map<String, String> map = new HashMap<>();
        map.put("page", String.valueOf(page));
        map.put("limit", "30");
        map.put("sence", algorithm);
        map.put("startTime", mSimpleDateFormat.format(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000));
        map.put("endTime", mSimpleDateFormat.format(System.currentTimeMillis()));
        for (Map.Entry<String, String> entry : map.entrySet()) {
            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
            builder.append("&");
        }
        builder.substring(0, builder.length() - 1);
        Request request = new Request.Builder().
                addHeader("token", loginCache.getToken()).
                addHeader("user", loginCache.getUser()).
                addHeader("adminFlag", loginCache.getAdminFlag())
                .url(builder.toString())
                .get()
                .build();
        OkHttpClient client = new OkHttpClient();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.e("onFailure", e.getMessage());
            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                String result = response.body().string();
                Log.e("TAG", "initData onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "操作成功")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        WarningPictureData warningPictureData = JSONObject.parseObject(data.toJSONString(), WarningPictureData.class);
                        list.addAll(warningPictureData.getList());

                        runOnUiThread(() -> {
                            myAdapter.notifyDataSetChanged();
                            binding.progressBar.setVisibility(View.GONE);
                            binding.gridview.onRefreshComplete();
                        });
                    } else {
                        runOnUiThread(() -> {
                            binding.progressBar.setVisibility(View.GONE);
                            binding.gridview.onRefreshComplete();
                        });
                        ToastUtil.show(jsonObject.getString("message"));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    @Override
    public void onSystemUiVisibilityChange(int visibility) {
        if (visibility == 0) {
            ContextUtil.getHandler().removeCallbacks(enterImmerseMode);
            ContextUtil.getHandler().postDelayed(enterImmerseMode, 2000);
        }
    }

    private Runnable enterImmerseMode = () -> ImmerseUtil.startImmerse(WarningPictureActivity.this);
}
