package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.view.View;
import android.widget.CompoundButton;
import android.widget.ListView;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.common.drone.key.AirLinkKey;
import com.skysys.fly.common.drone.key.KeyListener;
import com.skysys.fly.common.drone.key.KeyManager;
import com.skysys.fly.common.drone.key.ProductKey;
import com.skysys.fly.common.drone.util.DJIUtil;
import com.skysys.fly.common.listener.YNListener1;
import com.skysys.fly.page.fly.AircraftActivity;
import com.skysys.fly.page.fly.setting.ActivityMenuPresenter;
import com.skysys.fly.view.adapter.EasyAdapter;
import com.skysys.fly.view.adapter.EasyHolder;

import java.util.ArrayList;
import java.util.List;

import dji.common.camera.SettingsDefinitions;
import dji.common.error.DJIError;
import dji.common.product.Model;
import dji.common.util.CommonCallbacks;
import dji.sdk.airlink.AirLink;
import dji.sdk.camera.Camera;


public class HDMISDIVideoOutPut implements CompoundButton.OnCheckedChangeListener, SeekBar.OnSeekBarChangeListener, View.OnClickListener {

    private AircraftActivity activity;
    private com.skysys.fly.databinding.MenuHdPagerBinding binding;
    private ActivityMenuPresenter activityMenuPresenter;

    private AirLink airLink;
    private Boolean isCheckedEXT;

    private KeyListener<Float> LBAndEXTListener;
    private KeyListener<Model> modelNameListener;
    private KeyListener<Float> HDMIAndAVListener;
    private KeyListener<Boolean> extVideoInputPortEnabledListener;

    private Camera xt2Camera;
    private ListView mListView;
    private int currentPosition;
    private EasyAdapter easyAdapter;
    private ArrayList<String> displayModeList = new ArrayList<>();


    public HDMISDIVideoOutPut(com.skysys.fly.databinding.MenuHdPagerBinding binding, final ActivityMenuPresenter activityMenuPresenter, AircraftActivity activity) {
        this.binding = binding;
        this.activity = activity;
        this.activityMenuPresenter = activityMenuPresenter;

        airLink = DJIHelper.getInstance().getAirLink();
        //HDMI/SDI视频输出
        binding.menuHdItem5Signal.toggleHdmISDI.setOnCheckedChangeListener(this);
        //开启EXT端口
        binding.menuHdOpenExt.toggleHDEXTOPEN.setOnCheckedChangeListener(this);
        //带宽分配
        binding.menuHdBindwidth.SeekBarHdBindwidth.setOnSeekBarChangeListener(this);

        //适配 XT2 相机
        List<Camera> cameras = DJIHelper.getInstance().getCameras();
        if (cameras != null) {
            for (final Camera camera : cameras) {
                camera.getDisplayMode(new CommonCallbacks.CompletionCallbackWith<SettingsDefinitions.DisplayMode>() {

                    @Override
                    public void onSuccess(SettingsDefinitions.DisplayMode displayMode) {
                        xt2Camera = camera;
                        currentPosition = displayMode.value();

                        displayModeList.add("可见光");
                        displayModeList.add("热成像");
                        displayModeList.add("画中画");
                        displayModeList.add("融合");

                        HDMISDIVideoOutPut.this.binding.menuHdAppMode.rlMain.setVisibility(View.VISIBLE);
                        HDMISDIVideoOutPut.this.binding.menuHdAppMode.tvHdAppMode.setText(displayModeList.get(displayMode.value()));
                        HDMISDIVideoOutPut.this.binding.menuHdAppMode.rlHdAppModePop.setOnClickListener(HDMISDIVideoOutPut.this);
                        initDisplayModeView(displayMode.value());
                    }

                    @Override
                    public void onFailure(DJIError djiError) {
                    }
                });
            }
        }
    }

    public void remove() {

        KeyManager.getInstance().removeListener(modelNameListener);
        KeyManager.getInstance().removeListener(extVideoInputPortEnabledListener);
        KeyManager.getInstance().removeListener(HDMIAndAVListener);
        KeyManager.getInstance().removeListener(LBAndEXTListener);

        isCheckedEXT = null;
    }

    public void connect(boolean connect) {
        binding.menuHdItem5Signal.toggleHdmISDI.setEnabled(connect);
        binding.menuHdBindwidth.SeekBarHdBindwidth.setEnabled(connect);
        binding.menuHdBindwidth.SeekBarHdBindwidth.setEnabled(connect);
        if (connect) {
            getSupportMode();
            //获得Ext
            getHDEXT();
            //获得带宽分配
            getHTMIAndAV();
            getLBAndEXT();
            getHDMISDI();
        } else {
            remove();
            if (activityMenuPresenter != null) {
                activityMenuPresenter.setHDMISupportEXT(false);
                activityMenuPresenter.setHDMISupportSecondary(false);
            }
        }
    }

    @Override
    public void onClick(View v) {
        View anchor = binding.menuHdAppMode.tvHdAppMode;
        activity.showPopup(anchor, mListView, displayModeList.size());
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.toggle_HdmI_SDI:
                setHDMISDI(isChecked);
                break;
            case R.id.toggle_HD_EXT_OPEN:
                setHDEXT(isChecked);
                break;
        }
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (fromUser) {
            if (isCheckedEXT != null) {
                if (isCheckedEXT) {
                    setLBAndEXT(progress / 10f);
                } else {
                    setHTMIAndAV(progress / 10f);
                }
            }
        }
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
    }

    private void getSupportMode() {
        modelNameListener = new KeyListener<Model>() {
            @Override
            protected void onValueChanged(@Nullable Model old, @Nullable Model now) {
                if (now != null) {
                    boolean show = DJIUtil.showBandwidthAllocation(now);
                    activityMenuPresenter.setbandWidth(show);
                    activityMenuPresenter.setHDMISupportEXT(show);
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(ProductKey.create(ProductKey.MODEL_NAME), modelNameListener);
    }

    //获得带宽lB_EXt
    private void getLBAndEXT1() {
        KeyManager.getInstance().getValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_LB_VIDEO_INPUT_PORT), new YNListener1<Float>() {
            @Override
            public void onSuccess(final Float now) {
                if (isCheckedEXT != null && isCheckedEXT) {
                    ContextUtil.getHandler().post(() -> {
                        int value = (int) (now * 100);
                        int value1 = 100 - value;
                        binding.menuHdBindwidth.SeekBarHdBindwidth.setProgress((int) (now * 10));
                        String temp = "LB:" + value + "% EXT:" + value1 + "%";
                        binding.menuHdBindwidth.tvSeekbarHdBindwidth.setText(temp);
                    });
                }
            }

            @Override
            public void onException(Throwable e) {
            }
        });
    }

    private void getLBAndEXT() {
        LBAndEXTListener = new KeyListener<Float>() {
            @Override
            protected void onValueChanged(@Nullable Float old, @Nullable final Float now) {
                if (isCheckedEXT != null && isCheckedEXT) {
                    ContextUtil.getHandler().post(() -> {

                        int value = (int) ((now == null ? 0 : now) * 100);
                        int value1 = 100 - value;
                        binding.menuHdBindwidth.SeekBarHdBindwidth.setProgress((int) ((now == null ? 0 : now) * 10));
                        String temp = "LB:" + value + "% EXT:" + value1 + "%";
                        binding.menuHdBindwidth.tvSeekbarHdBindwidth.setText(temp);
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_LB_VIDEO_INPUT_PORT), LBAndEXTListener);
    }

    //设置带宽lB_EXt
    private void setLBAndEXT(final float band) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_LB_VIDEO_INPUT_PORT), band, null);
    }

    private void getHTMIAndAV1() {
        KeyManager.getInstance().getValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_HDMI_VIDEO_INPUT_PORT), new YNListener1<Float>() {
            @Override
            public void onSuccess(final Float now) {
                if (now != null) {
                    if (isCheckedEXT != null && !isCheckedEXT) {
                        ContextUtil.getHandler().post(() -> {
                            int value = (int) (now * 100);
                            int value1 = 100 - value;
                            binding.menuHdBindwidth.SeekBarHdBindwidth.setProgress((int) (now * 10));
                            String temp = "HDMI:" + value + "% AV:" + value1 + "%";
                            binding.menuHdBindwidth.tvSeekbarHdBindwidth.setText(temp);
                        });
                    }
                }
            }

            @Override
            public void onException(Throwable e) {
            }
        });
    }

    private void getHTMIAndAV() {
        HDMIAndAVListener = new KeyListener<Float>() {
            @Override
            protected void onValueChanged(@Nullable Float old, @Nullable final Float now) {
                if (now != null) {
                    if (isCheckedEXT != null && !isCheckedEXT) {
                        ContextUtil.getHandler().post(() -> {
                            int value = (int) (now * 100);
                            int value1 = 100 - value;
                            binding.menuHdBindwidth.SeekBarHdBindwidth.setProgress((int) (now * 10));
                            String temp = "HDMI:" + value + "% AV:" + value1 + "%";
                            binding.menuHdBindwidth.tvSeekbarHdBindwidth.setText(temp);
                        });
                    }
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_HDMI_VIDEO_INPUT_PORT), HDMIAndAVListener);
    }

    private void setHTMIAndAV(final float band) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.BANDWIDTH_ALLOCATION_FOR_HDMI_VIDEO_INPUT_PORT), band, null);
    }

    private void getHDEXT() {
        if (airLink != null) {
            extVideoInputPortEnabledListener = new KeyListener<Boolean>() {
                @Override
                protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                    isCheckedEXT = now;
                    if (now != null) {
                        if (now) {
                            getLBAndEXT1();
                        } else {
                            getHTMIAndAV1();
                        }
                        ContextUtil.getHandler().post(() -> binding.menuHdOpenExt.toggleHDEXTOPEN.setChecked(now));
                    }
                }
            };
            KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.IS_EXT_VIDEO_INPUT_PORT_ENABLED), extVideoInputPortEnabledListener);
        }
    }

    private void setHDEXT(final boolean isChecked) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.IS_EXT_VIDEO_INPUT_PORT_ENABLED), isChecked, null);
    }

    private void getHDMISDI() {
        KeyListener<Boolean> secondary_video_output_enabledListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now != null) {
                    ContextUtil.getHandler().post(() -> {
                        activityMenuPresenter.setHDMISupportSecondary(true);
                        binding.menuHdItem5Signal.toggleHdmISDI.setChecked(now);
                        activityMenuPresenter.setHDMISDIOutTure(now);
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OUTPUT_ENABLED), secondary_video_output_enabledListener);
    }

    private void setHDMISDI(final boolean isChecked) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OUTPUT_ENABLED), isChecked, null);
    }

    private void initDisplayModeView(int integer) {

        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), displayModeList) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {

                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position == currentPosition) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        } else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);

        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition = position;
            if (xt2Camera != null) {

                xt2Camera.setDisplayMode(SettingsDefinitions.DisplayMode.find(position), djiError -> {
                    if (djiError == null) {
                        if (position == 2) {
                            //画中画
                            xt2Camera.setPIPPosition(SettingsDefinitions.PIPPosition.IR_BOTTOM_RIGHT, null);
                        }
                        ContextUtil.getHandler().post(() -> {
                            easyAdapter.notifyDataSetChanged();
                            HDMISDIVideoOutPut.this.binding.menuHdAppMode.tvHdAppMode.setText(displayModeList.get(position));
                            activity.dismissPopupWindow();
                        });
                    }
                });
            }
        });
    }
}
*/
