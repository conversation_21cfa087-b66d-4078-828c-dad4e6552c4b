package dji.sampleV5.aircraft.page.operate;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import java.util.List;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.TaskInfo;
import dji.sampleV5.aircraft.view.adapter.TaskAdapter;

public class SendMissionListAdapter extends BaseAdapter {
    private List<OperateMissionInfo> missionList;
    private Context context;

    public SendMissionListAdapter(Context context, List<OperateMissionInfo> missionList) {
        this.context = context;
        this.missionList = missionList;
    }

    @Override
    public int getCount() {
        return missionList.size();
    }

    @Override
    public Object getItem(int i) {
        return missionList.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        View view = null;
        ViewHolder viewHolder = null;
        if (convertView == null) {
            view = LayoutInflater.from(context).inflate(R.layout.operate_mission_list, null);

            viewHolder = new ViewHolder();
            //实例化ViewHolder
            viewHolder.tvMissionName = view.findViewById(R.id.mission_name);
            viewHolder.tvMissionIdAndTime = view.findViewById(R.id.mission_time);


            //将viewHolder的对象存储到View中
            view.setTag(viewHolder);
        } else {
            view = convertView;
            //取出ViewHolder
            viewHolder = (ViewHolder) view.getTag();
        }
        OperateMissionInfo operateMissionInfo = missionList.get(position);
        //给item中各控件赋值
        viewHolder.tvMissionName.setText(operateMissionInfo.getMissionName());
        viewHolder.tvMissionIdAndTime.setText(operateMissionInfo.getUpdateTime());

        return view;

    }

    static class ViewHolder {
        TextView tvMissionName, tvTaskState, tvMissionIdAndTime, tvHiveName, tvAircraftNum, tvFlyMileage, tvFinshAction, tvStartAndLandTime;
    }
}
