package dji.sampleV5.aircraft.page.picture.media;

import android.app.ProgressDialog;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import dji.sampleV5.aircraft.R;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.ftp.FtpConnectionUpload;
import dji.sampleV5.aircraft.ftp.OnThreadResultListener;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.page.picture.PictureFragmentManager;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sampleV5.aircraft.view.adapter.ViewHolder;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.datacenter.media.MediaFile;


public class MediaTabALLFragment extends MediaBaseFragment implements MediaUpdate {

    private final PictureActivity activity;
    private final MediaObserver observer;
    private RecyclerView rv_media_fragment;
    private RelativeLayout rootView;
    private CommonAdapter<MediaBean> commonAdapter;

    //private int mPosition = -1;
    private boolean isSelect = false;
    //private boolean isSamePosition = true;
    private ProgressDialog dialog;
    private ArrayList<String> selectedList = new ArrayList<>();

    private ArrayList<MediaBean> mediaList;
    private ArrayList<MediaFile> mediaFileList = new ArrayList<>();
   /* private long lasttime = 0;
    private SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
*/
    MediaTabALLFragment(PictureActivity activity, ArrayList<MediaBean> mediaList, MediaObserver observer) {
        super();
        this.observer = observer;
        this.activity = activity;
        this.mediaList = mediaList;

       /* for(MediaBean media : mediaList){
            long currentTime = media.getTimeCreated();
            activity.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        //Date date = sf.parse(media.getDateCreated());
                        Log.e("TAG", "文件名：" + media.getFileName() + " 时间："+media.getDateCreated() +"  currentTime:"+currentTime+"  转换后：");
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                }
            });

        }*/
    }

    public void initData() {
        isLoading = false;
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.item_recyclerview, null);
        rv_media_fragment = view.findViewById(R.id.rv_item);
        rootView = view.findViewById(R.id.root);

        initRecyclerView();
        initProgressDialog();
        flContentMedia.addView(rootView);
    }

    private void initProgressDialog() {
        dialog = new ProgressDialog(activity);
        dialog.setMessage("正在删除..");
        dialog.setCanceledOnTouchOutside(false);
        dialog.setCancelable(false);
    }

    private void showProgressDialog() {
        if (dialog != null) {
            dialog.show();
        }
    }

    private void hideProgressDialog() {
        if (null != dialog && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    private void initRecyclerView() {
        GridLayoutManager manager = new GridLayoutManager(ContextUtil.getApplicationContext(), 5);
        rv_media_fragment.setLayoutManager(manager);
        commonAdapter = new CommonAdapter<MediaBean>(ContextUtil.getApplicationContext(), R.layout.item_image, mediaList) {
            @Override
            public void convert(final ViewHolder holder, final MediaBean mediaFile, final int position) {
                //如果点击了选择
                if (isSelect) {
                    holder.setTextHide(R.id.tv_select_media, selectedList.contains(mediaFile.getFileName()));
                    /*if (mPosition != -1) {
                        holder.setTextHide(R.id.tv_select_media, isSamePosition);
                    }*/
                } else {
                    holder.setTextHide(R.id.tv_select_media, false);
                }

                final int mediaType = mediaFile.getMediaType();
                final MediaFile media = mediaFile.getMedia();
                final int durationInSeconds = (int) mediaFile.getDurationInSeconds();
                final String fileName = media.getFileName();
                holder.setText(R.id.tv_item_label, mediaFile.getFileName().trim());
                holder.setImageTag(R.id.iv_img, fileName);
                Bitmap thumbnail = media.getThumbNail();
                if (thumbnail == null) {
                    ContextUtil.getHandler().post(() -> holder.setImageBackgroundResource(R.id.iv_img, R.drawable.ic_image_black));
                    media.pullThumbnailFromCamera(new CommonCallbacks.CompletionCallbackWithParam<Bitmap>() {
                        @Override
                        public void onSuccess(Bitmap bitmap) {
                            String imageTag = holder.getImageTag(R.id.iv_img);
                            String name = media.getFileName();
                            if (name.equals(imageTag)) {
                                holder.setImageTag(R.id.iv_img, name);
                                final Bitmap thumbnail1 = media.getThumbNail();
                                setView(thumbnail1, mediaType, holder, durationInSeconds);
                            }
                        }

                        @Override
                        public void onFailure(@NonNull IDJIError error) {

                        }
                    });
                } else {
                    setView(thumbnail, mediaType, holder, durationInSeconds);
                }
            }
        };

        commonAdapter.setOnItemClickListener((view, position) -> {
            //点击选择不删除图片
            if (isSelect) {
                //获得当前选中的media
                MediaFile media = mediaList.get(position).getMedia();
                String filename = media.getFileName();
                if (selectedList.contains(filename)) {
                    selectedList.remove(filename);
                    mediaFileList.remove(media);
                } else {
                    selectedList.add(filename);
                    mediaFileList.add(media);
                }

                commonAdapter.notifyItemChanged(position);
            } else {
                //显示图片的界面
                Bundle bundle = new Bundle();
                bundle.putInt(ContextUtil.getString(R.string.position), position);
                bundle.putParcelableArrayList(ContextUtil.getString(R.string.all), mediaList);
                activity.getPictureFragmentManager().addFragment(PictureFragmentManager.MEDIA_FRAGMENT_SHOW, bundle);
            }
        });
        rv_media_fragment.setAdapter(commonAdapter);
        rv_media_fragment.setItemAnimator(null);
    }

    private void setView(Bitmap thumbnail, final int mediaType, final ViewHolder holder, final int durationInSeconds) {
        final BitmapDrawable bitmapDrawable = new BitmapDrawable(activity.getResources(), thumbnail);
        ContextUtil.getHandler().post(() -> {
            if(mediaType == 4){ //全景图
                holder.setImageResource(R.id.iv_video_play, R.drawable.ic_pano);
                holder.setImageVisible(R.id.iv_video_play, true);
            }else if (mediaType != 0 && mediaType != 5 && mediaType != 1) {
                holder.setImageResource(R.id.iv_video_play, R.drawable.ic_video_play);
                //holder.setText(R.id.tv_item_label, Util.formatSecond2Hour(durationInSeconds));
                holder.setImageVisible(R.id.iv_video_play, true);
            } else {
                holder.setImageVisible(R.id.iv_video_play, false);
            }
            holder.setImageResource(R.id.iv_img, bitmapDrawable);
        });
    }

    @Override
    public void update(String filename) {
        for (int i = 0; i < mediaList.size(); i++) {
            MediaBean mediaBean = mediaList.get(i);
            if (mediaBean.getFileName().equals(filename)) {
                mediaList.remove(i);
                commonAdapter.notifyItemRemoved(i);
                if (i != mediaList.size()) {
                    commonAdapter.notifyItemRangeChanged(i, mediaList.size() - i);
                }
            }
        }
    }

    //选择图片
    @Override
    public void selectMedia(boolean select) {
        isSelect = select;
        if (!isSelect) {
            if (commonAdapter != null) {
                //mPosition = -1;
                mediaFileList.clear();
                commonAdapter.notifyItemRangeChanged(0, mediaList.size());
            }
        }
    }

    //删除图片
    @Override
    public void deleteMedia(final int currentItem) {
        if (mediaManager != null) {
            showProgressDialog();
            mediaManager.deleteMediaFiles(mediaFileList, new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ContextUtil.getHandler().post(() -> {
                        if (observer != null) {
                            observer.setDeleteMediaUpdate(mediaFileList, currentItem);
                        }

                        //mPosition = -1;

                        ArrayList<MediaBean> newMediaList = new ArrayList<>();
                        boolean temp = false;
                        for (int i = 0; i < mediaList.size(); i++) {
                            temp = false;
                            for (int j = 0; j < mediaFileList.size(); j++) {
                                if (mediaFileList.get(j) == mediaList.get(i).getMedia()) {
                                    temp = true;
                                    break;
                                }
                            }
                            if(!temp){
                                newMediaList.add(mediaList.get(i));
                            }
                        }
                        //ToastUtil.show("删除前" + mediaList.size() + "  newMediaList:" + newMediaList.size());
                        mediaList.clear();
                        mediaList.addAll(newMediaList);
                        commonAdapter.notifyDataSetChanged();
                        mediaFileList.clear();
                        ToastUtil.show("删除成功！");
                        hideProgressDialog();
                    });
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    ToastUtil.show("deleteFiles error :" + error.description());
                    hideProgressDialog();
                }
            });
        }
    }

    @Override
    public void downloadMedia(int currentItem) {
        downloadLL.setVisibility(View.VISIBLE);
        downloadTV.setText(" 总文件个数" + mediaFileList.size());
        MediaDownloadController.getInstance().setCheckedMediaFileList(mediaFileList);
        MediaDownloadController.getInstance().setOnProgressListener(new MediaDownloadController.OnProgressUpdateListener() {
            @Override
            public void onProgressUpdate(long total, long current, String fileName, int currentPosition) {
                ContextUtil.getHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        downloadTV.setText("总文件个数" + MediaDownloadController.getInstance().getTotalCount() + "    当前下载第" + (currentPosition + 1) + "个 " + fileName);
                        downloadPB.setProgress((int) (current * 100 / total));
                        downPercent.setText((int) (current * 100 / total) + "%");
                    }
                });
            }

            @Override
            public void onSuccess(String fileName) {
                ContextUtil.getHandler().post(new Runnable() {
                    @Override
                    public void run() {
                        if (MediaDownloadController.getInstance().getTotalCount() == (MediaDownloadController.getInstance().getCurrentPosition() + 1)) {
                            downloadLL.setVisibility(View.INVISIBLE);
                            ToastUtil.show("全部下载完成！");

                           /* new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    String ftpPath = "/myfolder/MediaSource/zl/";
                                    FtpConnectionUpload ftpConnectionUpload = new FtpConnectionUpload("test",ftpPath,mediaFileList, new OnThreadResultListener() {
                                        @Override
                                        public void onFinish(int index) {
                                            ContextUtil.getCurrentActivity().runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    Log.e("FtpConnectionUpload", "onFinish: "+index);

                                                    if (index == mediaFileList.size() - 1) {
                                                        Log.e("FtpConnectionUpload", "结束");
                                                    }
                                                }
                                            });

                                        }

                                        @Override
                                        public void onError(int index, String error, File localFile) {
                                            Log.e("FtpConnectionUpload", "onError  index: "+index+"  error:"+error);
                                        }
                                    });
                                    ftpConnectionUpload.start();
                                }
                            }).start();*/

                           /* MediaFragment mediaFragment = (MediaFragment) activity.getAircraftFragmentManager().getFragment(AircraftFragmentManager.MEDIA_FRAGMENT);
                            mediaFragment.uploadVideo(mediaFileList);*/
                        }
                        downloadPB.setProgress(100);
                        downPercent.setText("100%");

                    }
                });
            }
        });
        MediaDownloadController.getInstance().startDownload();
        ToastUtil.show("开始下载-文件个数" + mediaFileList.size());
    }


    @Override
    public void deleteMediaUpdate(ArrayList<MediaFile> listMedia) {
        for (int i = 0; i < listMedia.size(); i++) {
            String fileName = listMedia.get(i).getFileName();
            update(fileName);
        }
    }
}
