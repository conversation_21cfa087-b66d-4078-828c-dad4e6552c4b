package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package com.haixia.fly.page.fly.setting.pager.detail;

import android.support.annotation.Nullable;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.ToggleButton;

import com.haixia.fly.R;
import com.haixia.fly.page.fly.setting.ActivityMenuPresenter;
import com.haixia.fly.common.listener.YNListener0;
import com.haixia.fly.databinding.MenuHdPagerBinding;
import com.haixia.fly.common.drone.key.AirLinkKey;
import com.haixia.fly.common.drone.key.KeyListener;
import com.haixia.fly.common.drone.key.KeyManager;
import com.haixia.fly.common.ContextUtil;

*/
/*


public class HDOSDSet implements SeekBar.OnSeekBarChangeListener {
    private  ToggleButton mtoggleHDMIOSD;
    private  SeekBar sbMarginDown;
    private  SeekBar sbMarginLeft;
    private  SeekBar sbMarginRight;
    private  SeekBar sbMarginTop;
    private  TextView tvMarginDown;
    private  TextView tvMarginLeft;
    private  TextView tvMarginRight;
    private  TextView tvMarginTop;
    private KeyListener<Boolean> videoOSDListener;
    private KeyListener<Integer> bottoMlistener;
    private KeyListener<Integer> RIGHTListener;
    private KeyListener<Integer> LEFTListener;
    private KeyListener<Integer> TopListener;
    private final ActivityMenuPresenter activityMenuPresenter;
    private final RelativeLayout rlHdOsd;
    private boolean isTouch=false;

    public HDOSDSet(MenuHdPagerBinding binding) {
        activityMenuPresenter = binding.getActivityMenuPresenter();
        mtoggleHDMIOSD = binding.menuHdItem6Signal.toggleHDMIOSD;
        sbMarginDown = binding.menuHdItem6Signal.sbMarginDown;
        sbMarginLeft = binding.menuHdItem6Signal.sbMarginLeft;
        sbMarginRight = binding.menuHdItem6Signal.sbMarginRight;
        sbMarginTop = binding.menuHdItem6Signal.sbMarginTop;
        rlHdOsd = binding.menuHdItem6Signal.rlHdOsd;

        tvMarginDown = binding.menuHdItem6Signal.tvMarginDown;
        tvMarginLeft = binding.menuHdItem6Signal.tvMarginLeft;
        tvMarginRight = binding.menuHdItem6Signal.tvMarginRight;
        tvMarginTop = binding.menuHdItem6Signal.tvMarginTop;
        sbMarginDown.setOnSeekBarChangeListener(this);
        sbMarginLeft.setOnSeekBarChangeListener(this);
        sbMarginRight.setOnSeekBarChangeListener(this);
        sbMarginTop.setOnSeekBarChangeListener(this);
        getHDOSDData();
        HDMIAndOSd();
    }
    public void getHDOSDData() {
        getSecondaryVideoOSD();
        getOSDTOPMargin();
        getOSDLeftMargin();
        getOSDrightMargin();
        getOSDbottonMargin();

    }
    //获得数据
    private void getOSDbottonMargin() {
        bottoMlistener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now!=null){
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            tvMarginDown.setText(now+"");
                            if (!isTouch){
                                sbMarginDown.setProgress(now);
                            }
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_BOTTOM_MARGIN), bottoMlistener);
    }

    private void getOSDrightMargin() {
        RIGHTListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now!=null){
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            tvMarginRight.setText(now+"");
                            if (!isTouch){
                                sbMarginRight.setProgress(now);
                            }
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_RIGHT_MARGIN), RIGHTListener);
    }

    private void getOSDLeftMargin() {
        LEFTListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now!=null){
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            tvMarginLeft.setText(now+"");
                            if (!isTouch){
                                sbMarginLeft.setProgress(now);
                            }
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_LEFT_MARGIN), LEFTListener);
    }

    private void getOSDTOPMargin() {
        TopListener = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                if (now!=null){
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            tvMarginTop.setText(now+"");
                            if (!isTouch){
                                sbMarginTop.setProgress(now);
                            }
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_TOP_MARGIN), TopListener);
    }

    private void getSecondaryVideoOSD() {
        rlHdOsd.setVisibility(View.VISIBLE);
        videoOSDListener = new KeyListener<Boolean>() {
            @Override
            protected void onValueChanged(@Nullable Boolean old, @Nullable final Boolean now) {
                if (now != null) {
                    ContextUtil.getHandler().post(new Runnable() {
                        @Override
                        public void run() {
                            activityMenuPresenter.setHDOSDISOPEN(now);
                            if (now) {
                                mtoggleHDMIOSD.setChecked(true);
                            } else {
                                mtoggleHDMIOSD.setChecked(false);
                            }
                        }
                    });
                }
            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_ENABLED), videoOSDListener);
    }

    //设置数据
    private void setSecondaryVideoOSD(boolean videoOSD) {
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_ENABLED), videoOSD, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }
    private void setOSDbottonMargin(int botton){
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_BOTTOM_MARGIN), botton, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });

    }
    private void setOSDrightMargin(int botton){
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_RIGHT_MARGIN), botton, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }
    private void setOSDLeftMargin(int botton){
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_LEFT_MARGIN), botton, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }
    private void setOSDTOPMargin(int botton){
        KeyManager.getInstance().setValue(AirLinkKey.createLightbridgeLinkKey(AirLinkKey.SECONDARY_VIDEO_OSD_TOP_MARGIN), botton, new YNListener0() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onException(Throwable e) {

            }
        });
    }

    //hdmi与osd同时输出画面
    private void HDMIAndOSd() {
        mtoggleHDMIOSD.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                setSecondaryVideoOSD(isChecked);
            }
        });
    }

    public void remove() {
        KeyManager.getInstance().removeListener(videoOSDListener);
        KeyManager.getInstance().removeListener(bottoMlistener);
        KeyManager.getInstance().removeListener(RIGHTListener);
        KeyManager.getInstance().removeListener(LEFTListener);
        KeyManager.getInstance().removeListener(TopListener);
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (isTouch){
            switch (seekBar.getId()) {
                case R.id.sb_margin_down:
                    setOSDbottonMargin(progress);
                    break;
                case R.id.sb_margin_left:
                    setOSDLeftMargin(progress);
                    break;
                case R.id.sb_margin_right:
                    setOSDrightMargin(progress);
                    break;
                case R.id.sb_margin_top:
                    setOSDTOPMargin(progress);
                    break;
            }
        }
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        isTouch=true;
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        isTouch=false;
    }

}
*/
