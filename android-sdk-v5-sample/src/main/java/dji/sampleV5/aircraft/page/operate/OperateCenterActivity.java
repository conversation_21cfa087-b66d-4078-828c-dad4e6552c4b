package dji.sampleV5.aircraft.page.operate;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.amap.api.maps.TextureMapView;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.MixMissionList;
import dji.sampleV5.aircraft.databinding.ActivityOperatecenterBinding;
import dji.sampleV5.aircraft.lbs.MapController;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.lbs.MissionMapPainter;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.v5.manager.KeyManager;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class OperateCenterActivity extends AppCompatActivity {
    private ActivityOperatecenterBinding binding;
    private MapController mMapController;
    private MissionMapPainter mMissionMapPainter;
    private WaypointMission mChosenMission;
    private TextureMapView mapView;
    private String apiToken;
    private String siteId;
    private String uavId;
    private String siteName;
    private String FCSN;
    private OperateMissionInfo operateMissionInfo;
    private List<OperateMissionInfo> list = new ArrayList<>();
    private SendMissionListAdapter sendMissionListAdapter;
    private LocationCoordinate2D locationCoordinate2D;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        getWindow().setStatusBarColor(getResources().getColor(android.R.color.transparent));
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_operatecenter);

        mMapController = MapServiceFactory.produceMapController();
        mapView = mMapController.getMapView(this);
        mMapController.onCreate(savedInstanceState);
        mMissionMapPainter = new MissionMapPainter(this, mMapController);

        binding.root.addView(mapView);
        binding.rlList.getBackground().mutate().setAlpha(160);
        binding.centerTitle.bringToFront();
        binding.missionList.bringToFront();
        binding.rlList.bringToFront();
        binding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        });
        binding.fly.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                flyMission();
            }
        });

        siteId = getIntent().getStringExtra("siteId");
        uavId = getIntent().getStringExtra("uavId");
        siteName = getIntent().getStringExtra("siteName");
        FCSN = getIntent().getStringExtra("sn");

        sendMissionListAdapter = new SendMissionListAdapter(this, list);
        binding.missionList.setAdapter(sendMissionListAdapter);
        binding.missionList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (mChosenMission != null) {
                    mMissionMapPainter.remove(mChosenMission);
                }
                List<OperateMissionInfo.Detail> paths;
                operateMissionInfo = list.get(position);
                if (operateMissionInfo.getFlightParams().getChildrenList() == null) { //只有驭光扫图的任务才有ChildrenList
                    paths = operateMissionInfo.getFlightParams().getFlightPath();
                } else {
                    paths = operateMissionInfo.getFlightParams().getChildrenList().get(0).getFlightPath();
                }

                WaypointMission waypointMission = new WaypointMission();
                waypointMission.setMissionBatch(String.valueOf(System.currentTimeMillis()));
                for (OperateMissionInfo.Detail detail : paths) {
                    WaypointMission.Waypoint waypoint = new WaypointMission.Waypoint();
                    //double[] position = GeoSysConversion.gcj02toWGS84(flightpath.getLatitude(), flightpath.getLongitude());
                    waypoint.setLatLng(new AppLatLng(detail.getLatitude(), detail.getLongitude()));
                    waypointMission.addWaypoint(waypoint);
                }
                mChosenMission = waypointMission;
                mMissionMapPainter.draw(mChosenMission, true);
            }
        });

        //login();
        initData(SpUtil.getLoginCache().getApiToken());
    }

    private void flyMission() {
        if (operateMissionInfo == null) {
            ToastUtil.show("请选择一条任务!");
            return;
        }

        locationCoordinate2D = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
        if (locationCoordinate2D == null) {
            ToastUtil.show("没有获取到无人机坐标！");
            return;
        }

        UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();
        if (uavInfoSN != null) {
            String planUavID = uavInfoSN.getData().getUAVID();
            if (TextUtils.equals(planUavID, uavId)) {
                startActivity(new Intent(this, DefaultLayoutActivity.class));
                ContextUtil.getHandler().postDelayed(new Runnable() {//这么写的原因是想飞控页面加载完成后再收到任务
                    @Override
                    public void run() {
                        sendMissionData();
                    }
                }, 2000);
            } else {
                sendMissionData();
            }
        } else {
            sendMissionData();
        }

    }

    private void sendMissionData() {

        Double aircraftLat = locationCoordinate2D.getLatitude();
        Double aircraftLng = locationCoordinate2D.getLongitude();
        JSONObject data = new JSONObject();
        data.put("uavId", uavId);
        data.put("siteId", siteId);
        data.put("siteName", siteName);
        Log.e("aaa", "getMissionID: "+operateMissionInfo.getMissionId());
        data.put("missionId", operateMissionInfo.getMissionId());
        data.put("missionName", operateMissionInfo.getMissionName());
        data.put("missionType", operateMissionInfo.getMissionType());
        data.put("childMissionIndex", 0);
        data.put("flightWay", 1);
        data.put("finishAction", 1);
        JSONArray array = new JSONArray();
        array.add(aircraftLng);
        array.add(aircraftLat);
        data.put("uavCoordinate", array);
        Log.e("TAG", ": " + data.toString());

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = ApiConfig.INSTANCE.getMIX_URL() + ApiConfig.MISSION_START;
        Log.e("TAG", "url: " + url);
        Request request = new Request.Builder().url(url).post(requestBody)
                .addHeader("ProductId", ApiConfig.INSTANCE.getPID()).addHeader("ApiToken", SpUtil.getLoginCache().getApiToken()).build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("TAG", "sendMissionData onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("TAG", "flyMission onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                        ToastUtil.show("任务已下发");
                    } else {
                        ToastUtil.show(jsonObject.getString("message"));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void initData(String apiToken) {
        //获取飞行历史列表
        String url = ApiConfig.INSTANCE.getMIX_URL() + ApiConfig.MISSION_LIST;
        JSONObject data = new JSONObject();
        data.put("page", 1);
        data.put("size", 100);
        data.put("siteId", siteId);

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        Request request = new Request.Builder().
                url(url).
                addHeader("ApiToken", apiToken).
                addHeader("ProductId", ApiConfig.INSTANCE.getPID()).
                post(requestBody)
                .build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String result = response.body().string();
                Log.e("TAG", "login onResponse: " + result);
                if (response.code() != 200) {

                } else {
                    try {
                        MixMissionList pageData = new Gson().fromJson(result, MixMissionList.class);
                        if (pageData.getList() == null) {
                            ToastUtil.show("没有获取到任务数据");
                            return;
                        }

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (pageData.getList().size() > 0) {
                                    binding.rlList.setVisibility(View.VISIBLE);
                                }
                                List<OperateMissionInfo> taskInfoList = pageData.getList();
                                list.addAll(taskInfoList);
                                sendMissionListAdapter.notifyDataSetChanged();
                            }
                        });

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        });

        /*HttpUtil.getInstance().getByApiToken(this, url, map, apiToken, new HttpUtil.ICallBack() {
            @Override
            public void onResponse(JsonResult jsonResult) {
                Log.e("TAG", ": " + jsonResult.getData().toString());
                MissionList pageData = new Gson().fromJson(jsonResult.getData().toString(), MissionList.class);
                if (pageData.getList() == null) {
                    ToastUtil.show("没有获取到任务数据");
                    return;
                }
                //Log.e("TAG", ": " + pageData.getList().get(0).getFlightParams().getChildrenList().size());

                if (pageData.getList().size() > 0) {
                    binding.rlList.setVisibility(View.VISIBLE);
                }
                List<OperateMissionInfo> taskInfoList = pageData.getList();
                list.addAll(taskInfoList);
                sendMissionListAdapter.notifyDataSetChanged();
            }
        });*/
    }

    private void login() {
        /*LoginCache loginCache = SpUtil.getLoginCache();
        String str_password = loginCache.getPwd();
        String str_mobile = loginCache.getUserName();
        if (TextUtils.isEmpty(str_mobile)) {
            ToastUtil.show("请重新登录后获取数据");
            return;
        }

        JSONObject data = new JSONObject();
        data.put("userName", str_mobile);
        data.put("password", MD5.stringToMD5(str_password));

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        String url = NetConfig.NJLOGIN + "transit/user/api/v1/login";
        Request request = new Request.Builder().url(url).post(requestBody).build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("TAG", "login onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                String result = response.body().string();
                Log.e("TAG", "login onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("reason"), "Success")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        LoginCache loginCache = JSONObject.parseObject(data.toJSONString(), LoginCache.class);

                        // 登录成功后获取任务列表
                        apiToken = loginCache.getApiToken();
                        initData(loginCache.getApiToken());
                    } else {
                        ToastUtil.show(jsonObject.getString("reason"));
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });*/
    }

    @Override
    protected void onResume() {
        super.onResume();
        mMapController.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mMapController.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mMapController.onDestroy();
    }
}
