package dji.sampleV5.aircraft.page.warning;

import java.util.List;

import dji.sampleV5.aircraft.data.task.TaskInfo;

public class WarningPictureData {
    private int totalCount;
    private int pageSize;
    private int totalPage;
    private int currPage;
    private List<PictureDetail> list;

    public class PictureDetail{
        private String img_url;
        private String event_time;
        private String cls;

        public String getCls() {
            return cls;
        }

        public void setCls(String cls) {
            this.cls = cls;
        }

        public String getImg_url() {
            return img_url;
        }

        public void setImg_url(String img_url) {
            this.img_url = img_url;
        }

        public String getEvent_time() {
            return event_time;
        }

        public void setEvent_time(String event_time) {
            this.event_time = event_time;
        }
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getCurrPage() {
        return currPage;
    }

    public void setCurrPage(int currPage) {
        this.currPage = currPage;
    }

    public List<PictureDetail> getList() {
        return list;
    }

    public void setList(List<PictureDetail> list) {
        this.list = list;
    }
}
