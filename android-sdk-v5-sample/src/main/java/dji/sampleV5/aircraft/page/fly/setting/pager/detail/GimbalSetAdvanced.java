package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.util.Log;
import android.widget.SeekBar;

import androidx.annotation.Nullable;
import androidx.databinding.adapters.SeekBarBindingAdapter;

import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.databinding.MenuGimbalAdvancedSetBinding;
import dji.sampleV5.aircraft.page.fly.setting.ActivityMenuPresenter;


public class GimbalSetAdvanced implements SeekBarBindingAdapter.OnStopTrackingTouch {

    //private Gimbal gimbal = null;
    private MenuGimbalAdvancedSetBinding binding;

    private ActivityMenuPresenter activityMenuPresenter;
    */
/*private KeyListener<Integer> pitch_smoothing;
    private KeyListener<Integer> yaw_smoothing;
    private KeyListener<Integer> pitch_controller_speed;
    private KeyListener<Integer> yaw_controller_speed;*//*


    public GimbalSetAdvanced(MenuGimbalAdvancedSetBinding binding) {
        this.binding = binding;
        activityMenuPresenter = binding.getActivityMenuPresenter();
        binding.setOnStopTrackingTouch(this);
    }

    public void setGimbalSetAdvanced() {
        i*/
/*f(!isFeatureSupported(CapabilityKey.PITCH_CONTROLLER_SPEED_COEFFICIENT)){
            binding.SeekBarGimbalSpeed1.setEnabled(false);
            binding.tvSeekbarGimbalSpeed1.setTextColor(ContextUtil.getColor(R.color.text_light_3));
            binding.titleGimbalSpeed1.setTextColor(ContextUtil.getColor(R.color.text_light_3));
        }

        if(!isFeatureSupported(CapabilityKey.YAW_CONTROLLER_SPEED_COEFFICIENT)){
            binding.SeekBarGimbalSpeed2.setEnabled(false);
            binding.tvSeekbarGimbalSpeed2.setTextColor(ContextUtil.getColor(R.color.text_light_3));
            binding.titleGimbalSpeed2.setTextColor(ContextUtil.getColor(R.color.text_light_3));
        }

        if(!isFeatureSupported(CapabilityKey.PITCH_CONTROLLER_SMOOTHING_FACTOR)){
            binding.SeekBarGimbalSpeed3.setEnabled(false);
            binding.tvSeekbarGimbalSpeed3.setTextColor(ContextUtil.getColor(R.color.text_light_3));
            binding.titleGimbalSpeed3.setTextColor(ContextUtil.getColor(R.color.text_light_3));
        }

        if(!isFeatureSupported(CapabilityKey.YAW_CONTROLLER_SMOOTHING_FACTOR)){
            binding.SeekBarGimbalSpeed4.setEnabled(false);
            binding.tvSeekbarGimbalSpeed4.setTextColor(ContextUtil.getColor(R.color.text_light_3));
            binding.titleGimbalSpeed4.setTextColor(ContextUtil.getColor(R.color.text_light_3));
        }*//*


        //获得轴速度
        getSpeedOnAxisTwo();
        //获得缓启/停
        getSmoothingSpeedOnAxisTwo();
    }

    public void restSpeed() {
        */
/*KeyManager.getInstance().performAction(GimbalKey.create(GimbalKey.RESTORE_FACTORY_SETTINGS, activityMenuPresenter.gimbalIndex), new YNListener0() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> ToastUtil.show(ContextUtil.getString(R.string.reset_gimbal_param)));
            }

            @Override
            public void onException(Throwable e) {
            }
        });*//*


    }

    public void remove() {
       */
/* KeyManager.getInstance().removeListener(pitch_smoothing);
        KeyManager.getInstance().removeListener(yaw_controller_speed);
        KeyManager.getInstance().removeListener(yaw_smoothing);
        KeyManager.getInstance().removeListener(pitch_controller_speed);*//*

    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        int progress = seekBar.getProgress();
        switch (seekBar.getId()) {
            case R.id.SeekBar_gimbal_speed1:
                seekBarGimbalSpeed1(progress);
                break;
            case R.id.SeekBar_gimbal_speed2:
                seekBarGimbalSpeed2(progress);
                break;
            case R.id.SeekBar_gimbal_speed3:
                seekBarGimbalSpeed3(progress);
                break;
            case R.id.SeekBar_gimbal_speed4:
                //    偏航轴启/停
                seekBarGimbalSpeed4(progress);
                break;
        }
    }

    private void getSmoothingSpeedOnAxisTwo() {
        */
/*pitch_smoothing = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> {
                    binding.SeekBarGimbalSpeed3.setProgress(now);
                    activityMenuPresenter.setTvSeekbalSpeed3(now + "");
                });

            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(GimbalKey.create(GimbalKey.PITCH_CONTROLLER_SMOOTHING_FACTOR, activityMenuPresenter.gimbalIndex), pitch_smoothing);
        yaw_smoothing = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> {
                    binding.SeekBarGimbalSpeed4.setProgress(now);
                    activityMenuPresenter.setTvSeekbalSpeed4(now + "");
                });

            }
        };
        KeyManager.getInstance().addListenerWithInitialValue(GimbalKey.create(GimbalKey.YAW_CONTROLLER_SMOOTHING_FACTOR, activityMenuPresenter.gimbalIndex), yaw_smoothing);*//*


    }

    private void getSpeedOnAxisTwo() {
        */
/*pitch_controller_speed = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> {
                    binding.SeekBarGimbalSpeed1.setProgress(now);
                    activityMenuPresenter.setTvSeekbalSpeed1(now + "");

                });

            }
        };
        Log.e("isFeatureSupported", "俯仰 = " + isFeatureSupported(CapabilityKey.PITCH_CONTROLLER_SPEED_COEFFICIENT));
        KeyManager.getInstance().addListenerWithInitialValue(GimbalKey.create(GimbalKey.PITCH_CONTROLLER_SPEED_COEFFICIENT, activityMenuPresenter.gimbalIndex), pitch_controller_speed);
        yaw_controller_speed = new KeyListener<Integer>() {
            @Override
            protected void onValueChanged(@Nullable Integer old, @Nullable final Integer now) {
                ContextUtil.getHandler().post(() -> {
                    binding.SeekBarGimbalSpeed2.setProgress(now);
                    activityMenuPresenter.setTvSeekbalSpeed2(now + "");
                });
            }
        };
        Log.e("isFeatureSupported", "偏航 = " + isFeatureSupported(CapabilityKey.YAW_CONTROLLER_SPEED_COEFFICIENT));
        KeyManager.getInstance().addListenerWithInitialValue(GimbalKey.create(GimbalKey.YAW_CONTROLLER_SPEED_COEFFICIENT, activityMenuPresenter.gimbalIndex), yaw_controller_speed);*//*


    }

    //    偏航轴启/停
  */
/*  private void seekBarGimbalSpeed4(int progress) {
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.YAW_CONTROLLER_SMOOTHING_FACTOR, activityMenuPresenter.gimbalIndex), progress, null);
    }

    //    俯仰轴启/停
    private void seekBarGimbalSpeed3(int progress) {
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.PITCH_CONTROLLER_SMOOTHING_FACTOR, activityMenuPresenter.gimbalIndex), progress, null);
    }

    //    云台偏航轴速度
    private void seekBarGimbalSpeed2(int progress) {
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.YAW_CONTROLLER_SPEED_COEFFICIENT, activityMenuPresenter.gimbalIndex), progress, null);
    }

    //    云台俯仰轴速度
    private void seekBarGimbalSpeed1(int progress) {
        KeyManager.getInstance().setValue(GimbalKey.create(GimbalKey.PITCH_CONTROLLER_SPEED_COEFFICIENT, activityMenuPresenter.gimbalIndex), progress, null);
    }

    *//*
*/
/*
     * Check if The Gimbal Capability is supported
     *//*
*/
/*
    private boolean isFeatureSupported(CapabilityKey key) {
        Gimbal gimbal = getGimbalInstance();
        if (gimbal == null) {
            return false;
        }

        DJIParamCapability capability = null;
        if (gimbal.getCapabilities() != null) {
            capability = gimbal.getCapabilities().get(key);
        }

        if (capability != null) {
            return capability.isSupported();
        }
        return false;
    }

    private Gimbal getGimbalInstance() {
        if (gimbal == null) {
            initGimbal();
        }
        return gimbal;
    }

    private void initGimbal() {
        if (DJISDKManager.getInstance() != null) {
            BaseProduct product = DJISDKManager.getInstance().getProduct();
            if (product != null) {
                if (product instanceof Aircraft) {
                    int currentGimbalId = 0;
                    gimbal = ((Aircraft) product).getGimbals().get(currentGimbalId);
                } else {
                    gimbal = product.getGimbal();
                }
            }
        }
    }*//*

}
*/
