package dji.sampleV5.aircraft.page.fly.controller;

import android.util.Log;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.video.channel.VideoChannelType;
import dji.v5.manager.datacenter.MediaDataCenter;
import dji.v5.manager.datacenter.livestream.LiveStreamManager;
import dji.v5.manager.datacenter.livestream.LiveStreamSettings;
import dji.v5.manager.datacenter.livestream.LiveStreamStatus;
import dji.v5.manager.datacenter.livestream.LiveStreamStatusListener;
import dji.v5.manager.datacenter.livestream.LiveStreamType;
import dji.v5.manager.datacenter.livestream.StreamQuality;
import dji.v5.manager.datacenter.livestream.settings.RtmpSettings;
import dji.v5.manager.datacenter.media.MediaManager;
import dji.v5.manager.interfaces.ILiveStreamManager;
import dji.v5.ux.core.util.SpUtil;

;

public class RTMPManager implements RtmpStatusCallback {

    private static RTMPManager instance;
    private boolean isPublishing;
    private RtmpStatusCallback mPublishHandler;

    private ILiveStreamManager streamManager;

    private LiveStreamStatusListener liveStreamStatusListener = new LiveStreamStatusListener() {
        @Override
        public void onLiveStreamStatusUpdate(LiveStreamStatus status) {
            if (status.isStreaming()) {
                isPublishing = true;
                onRTMPConnected();
                onRTMPInfo(status.getFps(), (float) streamManager.getLiveVideoBitrate() * 1000);
            } else {
                isPublishing = false;
                onRTMPDisconnected(false);
            }
        }

        @Override
        public void onError(IDJIError error) {

        }
    };

    private RTMPManager() {
    }

    public static RTMPManager getInstance() {
        if (instance == null) {
            instance = new RTMPManager();
        }

        return instance;
    }

    public void setPublishEventHandler(RtmpStatusCallback handler) {
        this.mPublishHandler = handler;
    }

    public boolean isPublishing() {
        return this.isPublishing;
    }

    public void startPublish(String rtmpUrl) {
        Log.e("TAG", "推流地址: "+rtmpUrl);
        //setHDLiveViewEnabled();
        onRTMPConnecting();
        streamManager = MediaDataCenter.getInstance().getLiveStreamManager();
        streamManager.addLiveStreamStatusListener(liveStreamStatusListener);
        //String liveShowUrl = "rtmp://aaa.lindechao.work/live/dc?txSecret=3bce205cc8aa7ea98a2d92ebc34d62ed&txTime=6388C8E0";
        //String url = "rtmp://stream4.skysys.cn/xl_uav/ch96";
        //rtmpUrl = "rtmp://play3.skysys.cn/xl_uav/ch6666941";
        LiveStreamSettings liveStreamSettings = new LiveStreamSettings.Builder()
                .setLiveStreamType(LiveStreamType.RTMP)
                .setRtmpSettings(new RtmpSettings.Builder()
                        .setUrl(rtmpUrl)
                        .build())
                .build();
        streamManager.setLiveStreamSettings(liveStreamSettings);
        streamManager.setVideoChannelType(VideoChannelType.PRIMARY_STREAM_CHANNEL);
        streamManager.setLiveStreamQuality(StreamQuality.find(SpUtil.getHdmiMode() + 1));
        streamManager.startStream(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ToastUtil.show("推流开启成功");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show("推流开启失败:"+error.description());
            }
        });
    }

    public void stopPublish() {
        if (streamManager != null) {
            streamManager.stopStream(new CommonCallbacks.CompletionCallback() {
                @Override
                public void onSuccess() {
                    ToastUtil.show("关闭推流");
                    isPublishing = false;
                    streamManager.removeLiveStreamStatusListener(null);
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {

                }
            });
        }
    }

    @Override
    public void onRTMPConnecting() {
        if (mPublishHandler != null) {
            mPublishHandler.onRTMPConnecting();
        }
    }

    @Override
    public void onRTMPConnected() {
        if (mPublishHandler != null) {
            mPublishHandler.onRTMPConnected();
        }
    }

    @Override
    public void onRTMPDisconnected(boolean manually) {
        isPublishing = false;

        if (mPublishHandler != null) {
            mPublishHandler.onRTMPDisconnected(manually);
        }
    }

    @Override
    public void onRTMPInfo(float fps, float bitrate) {
        if (mPublishHandler != null) {
            mPublishHandler.onRTMPInfo(fps, bitrate);
        }
    }
}