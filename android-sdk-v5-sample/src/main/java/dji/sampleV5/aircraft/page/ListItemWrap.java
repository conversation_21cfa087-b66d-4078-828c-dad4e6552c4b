package dji.sampleV5.aircraft.page;

import android.app.Activity;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.event.Message;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.ui.activity.operate.NewOperateActivity;
import dji.sampleV5.aircraft.net.bean.qicloud.EventType;
import dji.sampleV5.aircraft.net.bean.qicloud.ListItem;
import dji.sampleV5.aircraft.net.bean.qicloud.SiteModel;
import dji.sampleV5.aircraft.net.bean.qicloud.SiteState;
import dji.sampleV5.aircraft.util.StringUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.v5.utils.common.JsonUtil;
import dji.v5.utils.common.LogUtils;
import io.socket.emitter.Emitter;

class ItemState {
    public int uavState;
    public int osdkState;
    public int subState;
    public int droneUpdate;
    public int hiveState;
    public int weather;
    public double windSpeed;
    public int isRaining;
    public int hiveUpdate;

    ItemState() {
        this.uavState = 0;
        this.osdkState = 0;
        this.subState = 0;
        this.droneUpdate = 0;
        this.hiveState = 0;
        this.weather = 0;
        this.windSpeed = 0;
        this.isRaining = 0;
        this.hiveUpdate = 0;
    }

    /**
     * 复制当前对象
     */
    protected ItemState deepClone() {
        ItemState next = new ItemState();
        next.uavState = this.uavState;
        next.osdkState = this.osdkState;
        next.subState = this.subState;
        next.droneUpdate = this.droneUpdate;
        next.hiveState = this.hiveState;
        next.weather = this.weather;
        next.windSpeed = this.windSpeed;
        next.isRaining = this.isRaining;
        next.hiveUpdate = this.hiveUpdate;
        return next;
    }

    public String getSiteStateString() {
        SiteState stata = SiteState.getByCode(subState);
        if (stata == null) {
            return "";
        }
        return stata.value;
    }
}

interface StatusWidget {
    View render(Activity activity);
}

/**
 * 数据模型, 用来渲染诸如: 天气〇 无人机〇
 */
class StatusText implements StatusWidget {
    /**
     * 文字
     */
    public String text;
    /**
     * 是否高亮显示
     */
    public boolean active;
    public int weight;

    StatusText(String text, boolean active) {
        this(text, active, 1);
    }

    StatusText(String text, boolean active, int weight) {
        this.text = text;
        this.active = active;
        this.weight = weight;
    }

    StatusText(String text, int active) {
        this(text, active, 1);
    }

    StatusText(String text, int active, int weight) {
        this.text = text;
        this.active = active == 1;
        this.weight = weight;
    }

    @Override
    public View render(Activity activity) {
        float density = activity.getResources().getDisplayMetrics().density;
        LinearLayout container = new LinearLayout(activity);
        container.setGravity(Gravity.START);
        container.setGravity(Gravity.START);
        container.setOrientation(LinearLayout.HORIZONTAL);

        LinearLayout.LayoutParams cLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        cLayout.weight = weight;
        container.setLayoutParams(cLayout);

        // --------
        // text
        // --------
        LinearLayout.LayoutParams tLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        tLayout.gravity = Gravity.CENTER_VERTICAL;

        TextView t = new TextView(activity);
        t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        t.setTextColor(Color.parseColor("#D9000000"));
        t.setLayoutParams(tLayout);
        t.setText(this.text);
        container.addView(t);

        // --------
        // image icon
        // --------
        int color = this.active ? Color.rgb(0x4d, 0xae, 0x26) : Color.rgb(0xe2, 0x5f, 0x18);
        ColorStateList stateList = ColorStateList.valueOf(color);
        LinearLayout.LayoutParams iLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        iLayout.width = LinearLayout.LayoutParams.WRAP_CONTENT;
        iLayout.height = LinearLayout.LayoutParams.WRAP_CONTENT;
        iLayout.gravity = Gravity.CENTER_VERTICAL;
        iLayout.setMargins(0, Math.round(1 * density), 0, 0);
        iLayout.setMarginStart(Math.round(2 * density));

        ImageView i = new ImageView(activity);
        i.setLayoutParams(iLayout);
        i.setAdjustViewBounds(true);
        i.setBackground(ContextUtil.getDrawable(R.drawable.ic_active));
        i.setBackgroundTintList(stateList);
        i.setMaxWidth(Math.round(10 * density));
        i.setMaxHeight(Math.round(10 * density));
        i.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        container.addView(i);

        return container;
    }
}

/**
 * 风速
 */
class WindText implements StatusWidget {
    /**
     * 文字
     */
    public String text;
    /**
     * 风速的数值
     */
    public double value;

    WindText(String text, double value) {
        this.text = text;
        this.value = value;
    }

    @Override
    public View render(Activity activity) {
        // --------
        // layout
        // --------
        LinearLayout container = new LinearLayout(activity);
        container.setGravity(Gravity.START);
        container.setOrientation(LinearLayout.HORIZONTAL);

        LinearLayout.LayoutParams cLayout = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT);
        cLayout.gravity = Gravity.CENTER_VERTICAL;
        cLayout.weight = 1;
        container.setLayoutParams(cLayout);

        // --------
        // text
        // --------
        String speed = "0";
        if (value != 0) {
            speed = String.format(Locale.getDefault(), "%.2f", value);
        }

        LinearLayout.LayoutParams tLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        tLayout.gravity = Gravity.CENTER_VERTICAL;

        TextView t = new TextView(activity);
        t.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
        t.setTextColor(Color.parseColor("#D9000000"));
        t.setLayoutParams(tLayout);
        t.setText(String.format("%s: %s m/s", this.text, speed));
        container.addView(t);

        return container;
    }
}

/**
 * 包装类, 负责接受状态改变, 过滤无用改变
 */
public class ListItemWrap implements Emitter.Listener {
    /**
     * 是否渲染 天气+风速
     * todo 如果界面进一步复杂考虑使用 '操作子' 来进行重构
     */
    private static final boolean RENDER_LINE2 = false;

    private final String TAG = LogUtils.getTag(this);
    private final SiteListActivity activity;
    private final TaskAdapter adapter;
    private final ListItem inner;
    private final int index;


    /**
     * 状态
     */
    private ItemState state;
    /**
     * 用来在主线程更新界面
     */
    private final Runnable noticeUpdate;

    /**
     * 最后一次状态更新时间
     */
    private long lastSiteUpdate;
    private long lastUavUpdate;

    public final String siteStateEvent;

    ListItemWrap(SiteListActivity activity, TaskAdapter adapter, ListItem inner, int index) {
        this.activity = activity;
        this.adapter = adapter;
        this.inner = inner;
        this.index = index;
        this.state = getDefaultState();

        this.lastSiteUpdate = new Date().getTime();
        this.lastUavUpdate = new Date().getTime();

        this.siteStateEvent = String.format("%s state", inner.siteID);
        this.noticeUpdate = new Runnable() {
            @Override
            public void run() {
                adapter.notifyItemChanged(index);
            }
        };
    }

    public ListItem getItem() {
        return inner;
    }

    /**
     * 获得默认状态对象
     */
    private static ItemState getDefaultState() {
        return new ItemState();
    }

    /**
     * 接受到消息通知
     * type=2 {"STID":"SITE1659072693518","type":2,"hiveID":"HIVE_UHM_000030","isOnline":1,"roofState":3,"braceState":0,"stageState":3,"clawState":0,"flowState":0,"chargeOn":false,"isRaining":false,"windSpeed":0,"windDirection":0,"latitudeWGS":30.865405,"longitudeWGS":121.906315,"hiveAzimuth":0,"robotOnline":false,"batterySlot0":0,"batterySlot1":0,"batterySlot2":0,"batterySlot3":0,"batterySlot4":0,"batterySlot5":0,"batterySlot6":0,"batterySlot7":0,"timestamp":1676615692797,"workState":100}
     * type=1 {"STID":"ST_00000072","type":1,"UAVID":"UAV_1676527064424","isOnline":1,"missionBatch":"","missionID":"","waypointIndex":0,"altitude":30.2,"latitudeWGS":30.866299957,"longitudeWGS":121.909586048,"azimuth":85,"GPSCount":0,"GPSLevel":0,"upLink":100,"downLink":60,"subState":102,"isUAVOnline":0,"isGSOnline":0,"isSCOnline":0,"distanceStart":0,"verticalSpeed":0,"horizontalSpeed":0,"gimbalPitch":0,"gimbalRoll":0,"gimbalYaw":85,"UAVPitch":0,"UAVRoll":0,"UAVYaw":85,"compassState":1,"isRecording":0,"cameraType":"Zenmuse H20T","zoom":5,"displayMode":1,"UAVError":{"djiError":[]},"isManualMode":false,"isTargetTrace":false,"isLaserEnabled":false,"laserTargetLocation":[],"laserError":4,"targetTrace":[[-1,-1],[-1,-1]],"timestamp":"2023-02-17 14:34:49:283","batteries":[{"index":0,"cycleCount":117,"batteryPercent":100,"temperature":30,"lifetimePercent":0},{"index":1,"cycleCount":117,"batteryPercent":100,"temperature":30,"lifetimePercent":0}],"SDMediaSourceInfo":{"uploadMissionBatchs":null,"toUploadMissionBatchs":null,"totalFileCapacity":0,"uploadFileCapacity":0,"totalNumFiles":0,"uploadNumFiles":0,"status":0}}
     */
    @Override
    public void call(Object... args) {
        //Log.e(TAG, String.format("onNotice index:%d data:%s", this.index, args[0]));
        try {
            JSONObject json = (JSONObject) args[0];
            ItemState site = this.state.deepClone();

            int type = json.optInt("type");
            if (type == EventType.UavType.value) {
                int isUAVOnline = json.optInt("isUAVOnline");
                int isSCOnline = json.optInt("isSCOnline");
                int subState = json.optInt("subState");

                site.uavState = isUAVOnline == 1 ? 1 : 0;
                site.osdkState = isSCOnline;
                site.subState = subState;
                this.lastUavUpdate = new Date().getTime();

            } else if (type == EventType.SiteType.value) {
                double windSpeed = json.optDouble("windSpeed");
                boolean isRaining = json.optBoolean("isRaining");
                int a = windSpeed > 8 ? 401 : 200;
                int b = isRaining ? 121 : 313;

                site.hiveState = 1;
                site.weather = a + b;
                site.windSpeed = windSpeed;
                site.isRaining = b;
                this.lastSiteUpdate = new Date().getTime();
            }
            this.updateState(site);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    /**
     * 最后更新时间早于 earliest 的记录都认为是离线了, 更新对应的数据状态
     */
    public void checkOffline(long earliest) {
        ItemState next = this.state.deepClone();
        long now = new Date().getTime();
        int update = 0;

        if (this.lastUavUpdate < earliest) {
            next.uavState = 0;
            next.osdkState = 0;
            next.subState = 0;
            this.lastUavUpdate = now;
            update++;
        }

        if (this.lastSiteUpdate < earliest) {
            next.hiveState = 0;
            next.weather = 0;
            next.windSpeed = 0;
            next.isRaining = 0;
            this.lastSiteUpdate = now;
            update++;
        }

        if (update > 0) {
            try {
                this.updateState(next);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 比较显示字段是否改变了
     *
     * @param state
     * @return
     * @throws JSONException
     */
    String getVisibleSign(ItemState state) throws JSONException {
        JSONObject obj = new JSONObject();
        if (inner.siteMode == SiteModel.MODEL_1.code) {
            obj.put("hiveState", state.hiveState);
            if (RENDER_LINE2) {
                obj.put("isRaining", state.isRaining);
                obj.put("windSpeed", state.windSpeed);
            }
        }
        obj.put("uavState", state.uavState);
        obj.put("osdkState", state.osdkState);
        return obj.toString();
    }

    private boolean isUiTheSame(ItemState site, ItemState next) throws JSONException {
        String siteStr = getVisibleSign(site);
        String nextStr = getVisibleSign(next);
        return siteStr.equals(nextStr);
    }

    /**
     * 接受到 webSocket 请求, 更新状态
     */
    private void updateState(ItemState site) throws JSONException {
        // 只有显示状态变化才执行界面更新
        if (!this.isUiTheSame(this.state, site)) {
            this.activity.runOnUiThread(noticeUpdate);
        }
        // 不论字段是否改变都更新状态
        this.state = site;
    }

    /**
     * 渲染开关标志位
     *
     * @param holder
     */
    private void renderStatusLine(TaskViewHolder holder) {
        List<StatusWidget> line1 = new ArrayList<>();
        List<StatusWidget> line2 = new ArrayList<>();

        if (inner.siteMode == SiteModel.MODEL_1.code) {
            line1.add(new StatusText("机库", state.hiveState));
            line2.add(new StatusText("天气", state.isRaining));
            line2.add(new WindText("风速", state.windSpeed));
        }
        line1.add(new StatusText("无人机", state.uavState));
        line1.add(new StatusText("机载计算机", state.osdkState, 2));

        renderStatus(holder.itemView.findViewById(R.id.line_1), line1);
        if (RENDER_LINE2) {
            renderStatus(holder.itemView.findViewById(R.id.line_2), line2);
        }
    }

    /**
     * 渲染一组状态组件
     */
    private void renderStatus(LinearLayout line, List<StatusWidget> widgets) {
        if (widgets.size() == 0) {
            line.setVisibility(View.GONE);
            return;
        }

        line.removeAllViews();
        for (StatusWidget sw : widgets) {
            line.addView(sw.render(activity));
        }
    }

    /**
     * 渲染地理位置
     */
    private void renderAddress(TaskViewHolder holder) {
        LinearLayout wrap = holder.itemView.findViewById(R.id.address_wrap);
        TextView address = holder.itemView.findViewById(R.id.address);

        // 飞机库不显示地址
        if (inner.siteMode != SiteModel.MODEL_1.code) {
            wrap.setVisibility(View.GONE);
            return;
        }

        // 地址不存在不显示
        if (StringUtil.isEmpty(inner.siteAddress)) {
            wrap.setVisibility(View.GONE);
            return;
        }

        wrap.setVisibility(View.VISIBLE);
        address.setText(String.format("站点地址：%s", inner.siteAddress));
    }

    /**
     * 渲染机库图片
     */
    private void renderDronIcon(TaskViewHolder holder) {
        ImageView device = holder.itemView.findViewById(R.id.ic_device); // 主图片
        ImageView control = holder.itemView.findViewById(R.id.ic_control); // 控制器
        ImageView hover = holder.itemView.findViewById(R.id.ic_hover); // 悬浮

        String main = inner.getSiteImg();
        if (main != null) {
            //device.setImageDrawable(ContextUtil.getDrawable(main));
            Glide.with(ContextUtil.getApplicationContext()).load(main).into(device);
            device.setVisibility(View.VISIBLE);
        } else {
            device.setImageDrawable(ContextUtil.getDrawable(R.drawable.site_default));
        }

        String uavImg = inner.getUavImg();
        if (uavImg != null) {
            Glide.with(ContextUtil.getApplicationContext()).load(uavImg).into(hover);
            //hover.setImageDrawable(ContextUtil.getDrawable(uavImg));
            hover.setVisibility(View.VISIBLE);
        } else {
            hover.setVisibility(View.INVISIBLE);
        }

        int cImg = inner.computerImg();
        if (cImg != 0) {
            Glide.with(ContextUtil.getApplicationContext()).load(ContextUtil.getDrawable(cImg)).into(control);
            //control.setImageDrawable(ContextUtil.getDrawable(cImg));
            control.setVisibility(View.VISIBLE);
        } else {
            control.setVisibility(View.INVISIBLE);
        }
    }

    /**
     * adapter 回调函数, 在这里更新 UI
     */
    public void updateUI(TaskViewHolder holder) {
        Log.e(TAG, String.format("updateUI index:%d", this.index));

        TextView siteName = holder.itemView.findViewById(R.id.siteName);
        TextView subState = holder.itemView.findViewById(R.id.subState);
        TextView siteMode = holder.itemView.findViewById(R.id.siteMode);
        TextView updateTime = holder.itemView.findViewById(R.id.updateTime);
        TextView siteID = holder.itemView.findViewById(R.id.siteID);
        TextView operateCenter = holder.itemView.findViewById(R.id.operate_center);

        // 渲染图标
        renderDronIcon(holder);

        siteName.setText(inner.siteName);
        subState.setText(state.getSiteStateString());

        if (state.subState > 0) {
            subState.setBackground(ContextUtil.getDrawable(R.drawable.bg_substate_green));
        } else {
            subState.setBackground(ContextUtil.getDrawable(R.drawable.bg_substate_gray));
        }

        // updateTime.setText(String.format("最后更新时间：%s", inner.getUpdateTimeText()));
        siteMode.setText(String.format("设备：%s", inner.interpretSiteModel()));
        siteID.setText(String.format("ID：%s", inner.siteID));
        updateTime.setVisibility(View.GONE);

        // 渲染状态
        renderStatusLine(holder);

        // 渲染地址(地址文字+定位图标)
        // renderAddress(holder);

        operateCenter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (inner.UAVInfo == null || inner.UAVInfo.UAVID == null) {
                    ToastUtil.show("该站点信息不完整");
                    return;
                }
                LiveDataEvent.INSTANCE.getControlUavInfo().postValue(inner);
                Intent intent = new Intent(ContextUtil.getCurrentActivity(), NewOperateActivity.class);
                intent.putExtra("uavInfo", CommExtKt.toJsonStr(inner));
                intent.putExtra("status", state.subState);
                ContextUtil.getCurrentActivity().startActivity(intent);
//                EventBus.getDefault().postSticky(message);
                Log.e(TAG, String.format("onItemClick siteID: %s", inner.siteID));
//                if (state.subState > 0) {
//                    if(inner.UAVInfo == null || inner.UAVInfo.UAVID == null){
//                        ToastUtil.show("该站点信息不完整");
//                        return;
//                    }
//                    Intent intent = new Intent(ContextUtil.getCurrentActivity(), OperateCenterActivity.class);
//                    intent.putExtra("siteId", inner.siteID);
//                    intent.putExtra("uavId", inner.UAVInfo.UAVID);
//                    intent.putExtra("siteName", inner.siteName);
//                    intent.putExtra("sn", inner.UAVInfo.FCSN);
//                    ContextUtil.getCurrentActivity().startActivity(intent);
//                    Log.e(TAG, String.format("onItemClick siteID: %s", inner.siteID));
//                } else {
//                    ToastUtil.show("站点离线，无法操作");
//                }
            }
        });
    }
}
