package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.text.TextUtils;
import android.view.View;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import dji.sampleV5.aircraft.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.databinding.MenuAicraftSetPagerBinding;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.view.adapter.EasyAdapter;
import dji.sampleV5.aircraft.view.adapter.EasyHolder;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.flightcontroller.FailsafeAction;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;

public class OutOfControlSet implements View.OnClickListener {
    private MenuAicraftSetPagerBinding aircraftSetPagerBinding;
    private TextView TVControl;
    private int currentPosition;
    private EasyAdapter easyAdapter;
    private ListView mListView;
    private final ArrayList<String> listBehavior = new ArrayList<>();

    public OutOfControlSet(MenuAicraftSetPagerBinding aircraftSetPagerBinding) {
        this.aircraftSetPagerBinding = aircraftSetPagerBinding;
        TVControl = aircraftSetPagerBinding.menuOutOfControlSet.tvOutOfControl;
        TVControl.setOnClickListener(this);
        mListView = (ListView) View.inflate(ContextUtil.getApplicationContext(), R.layout.item_listview, null);

        List list = Arrays.asList("返航", "悬停", "降落");
        listBehavior.addAll(list);
        getBehavior();
    }

    private void getBehavior() {
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyFailsafeAction), new CommonCallbacks.CompletionCallbackWithParam<FailsafeAction>() {
            @Override
            public void onSuccess(FailsafeAction failsafeAction) {
                switch (failsafeAction){
                    case GOHOME:
                        setListIntoMode(0);
                        currentPosition = 0;
                        setText(0);
                        break;
                    case HOVER:
                        setListIntoMode(1);
                        currentPosition = 1;
                        setText(1);
                        break;
                    case LANDING:
                        setListIntoMode(2);
                        currentPosition = 2;
                        setText(2);
                        break;
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if(error != null && !TextUtils.isEmpty(error.description())){
                    ToastUtil.show(error.description());
                }
            }
        });
    }

    private void setBehavior(FailsafeAction failsafeAction) {
        KeyManager.getInstance().setValue(KeyTools.createKey(FlightControllerKey.KeyFailsafeAction), failsafeAction, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                ContextUtil.getHandler().post(() -> {
                    ToastUtil.show("设置成功");
                    easyAdapter.notifyDataSetChanged();
                    TVControl.setText(listBehavior.get(currentPosition));
                    ((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).dismissPopupWindow();
                });
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                ToastUtil.show(error.description());
            }
        });
    }

    private void setText(int value) {
        ContextUtil.getCurrentActivity().runOnUiThread(() -> {
            switch (value) {
                case 0:
                    TVControl.setText("返航");
                    break;
                case 1:
                    TVControl.setText("悬停");
                    break;
                case 2:
                    TVControl.setText("降落");
                    break;
            }
        });
    }

    public void connect(Boolean connect) {
        if (connect) {
            ToastUtil.show("outof");
            //Boolean isEnable = (Boolean) KeyManager.getInstance().getValue(FlightControllerKey.create(FlightControllerKey.IS_FAIL_SAFE));
            getBehavior();
        }
    }

    private void setListIntoMode(int integer) {
        easyAdapter = new EasyAdapter(ContextUtil.getApplicationContext(), listBehavior) {
            @Override
            public EasyHolder getHolder(int type) {
                return new EasyHolder() {
                    private TextView tv_text_item;

                    @Override
                    public int getLayout() {
                        return R.layout.one_center_item;
                    }

                    @Override
                    public View createView(int position) {
                        tv_text_item = view.findViewById(R.id.tv_center_item);
                        return view;
                    }

                    @Override
                    public void refreshView(int position, Object item) {
                        if (position == currentPosition) {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.colorBright));
                        } else {
                            tv_text_item.setTextColor(ContextUtil.getColor(R.color.white));
                        }
                        String ite = (String) item;
                        tv_text_item.setText(ite);
                    }
                };
            }
        };

        mListView.setAdapter(easyAdapter);
        mListView.setSelection(integer);
        mListView.setVerticalScrollBarEnabled(false);
        mListView.setOnItemClickListener((parent, view, position, id) -> {
            currentPosition = position;
            String s = listBehavior.get(position);
            switch (s) {
                case "返航":
                    setBehavior(FailsafeAction.GOHOME);
                    break;
                case "悬停":
                    setBehavior(FailsafeAction.HOVER);
                    break;
                case "降落":
                    setBehavior(FailsafeAction.LANDING);
                    break;
            }
        });
    }

    @Override
    public void onClick(View v) {
        View anchor = TVControl;
        ((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).showPopup(anchor, mListView, listBehavior.size());
    }
}
