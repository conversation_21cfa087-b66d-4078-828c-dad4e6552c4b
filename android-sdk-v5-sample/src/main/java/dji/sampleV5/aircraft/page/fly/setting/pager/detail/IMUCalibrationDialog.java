package dji.sampleV5.aircraft.page.fly.setting.pager.detail;/*
package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.skysys.fly.R;
import com.skysys.fly.common.ContextUtil;
import com.skysys.fly.common.drone.DJIHelper;
import com.skysys.fly.databinding.DialogImuCalibrationBinding;
import com.skysys.fly.util.ToastUtil;

import dji.common.error.DJIError;
import dji.common.flightcontroller.imu.IMUState;
import dji.common.util.CommonCallbacks;

public class IMUCalibrationDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private boolean isRefresh;
    private DialogImuCalibrationBinding binding;
    private LinearLayout indicatorLL;

    IMUCalibrationDialog(@NonNull Context context) {
        super(context, R.style.IMUCalibrationDialog);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
    }

    private void init() {
        if (mContext == null)
            return;

        LayoutInflater inflater = LayoutInflater.from(mContext);
        binding = DataBindingUtil.inflate(inflater, R.layout.dialog_imu_calibration, null, false);
        setContentView(binding.getRoot());
        binding.startCalibration.setOnClickListener(this);
        binding.close.setOnClickListener(this);
        binding.finish.setOnClickListener(this);

        DisplayMetrics displayMetrics = mContext.getResources().getDisplayMetrics(); // 获取屏幕宽、高用
        int screenWidth = displayMetrics.widthPixels;
        int screenHeight = displayMetrics.heightPixels;

        Window dialogWindow = getWindow();
        if (dialogWindow != null) {
            WindowManager.LayoutParams lp = dialogWindow.getAttributes();
            lp.width = (int) (screenWidth);
            lp.height = (int) (screenHeight);
            dialogWindow.setAttributes(lp);
        }

        indicatorLL = binding.indicatorLinear;

        for (int i = 0; i < 6; i++) {
            //创建底部指示器(小圆点)
            View view = new View(ContextUtil.getCurrentActivity());
            view.setBackgroundResource(R.drawable.background);
            view.setEnabled(false);
            //设置宽高
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(15, 15);
            //设置间隔
            if (i != 0) {
                layoutParams.leftMargin = 15;
            } else {
                view.setEnabled(true);
            }
            //添加到LinearLayout
            indicatorLL.addView(view, layoutParams);
        }

        if (DJIHelper.getInstance().getFlightController() != null) {
            DJIHelper.getInstance().getFlightController().setIMUStateCallback(new IMUState.Callback() {
                @Override
                public void onUpdate(@NonNull IMUState imuState) {
                    if (imuState != null) {
                        refreshView(imuState.getCalibrationProgress());
                        binding.seekbar.setProgress(imuState.getCalibrationProgress());
                    }
                }
            });
        }
    }

    private void refreshView(int progress) {
        ContextUtil.getHandler().post(() -> {
            if (progress > 0) {
                if (!isRefresh) {
                    binding.close.setVisibility(View.GONE);
                    binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu1));
                    binding.text1.setVisibility(View.GONE);
                    binding.text2.setVisibility(View.GONE);
                    binding.text3.setVisibility(View.GONE);
                    binding.text4.setVisibility(View.VISIBLE);
                    binding.text5.setVisibility(View.VISIBLE);
                    binding.indicatorLinear.setVisibility(View.VISIBLE);
                    binding.startCalibration.setVisibility(View.GONE);
                    binding.seekbar.setVisibility(View.VISIBLE);
                    binding.seekbarTips.setVisibility(View.VISIBLE);

                    isRefresh = true;

                }

                //bottom left right tail nose top 到了百分之95完成了第一步
                switch (progress) {
                    case 95:
                        binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu2));
                        refreshPoint(1);
                        break;
                    case 96:
                        binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu3));
                        refreshPoint(2);
                        break;
                    case 97:
                        binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu4));
                        refreshPoint(3);
                        break;
                    case 98:
                        binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu5));
                        refreshPoint(4);
                        break;
                    case 99:
                        binding.indicatorImg.setImageDrawable(ContextUtil.getDrawable(R.drawable.imu6));
                        refreshPoint(5);
                        break;
                    case 100:
                        binding.calibrationLl.setVisibility(View.GONE);
                        binding.finishLl.setVisibility(View.VISIBLE);
                        break;
                }
            }
        });
    }

    private void refreshPoint(int step) {
        for (int i = 0; i < indicatorLL.getChildCount(); i++) {
            if (i == step) {
                indicatorLL.getChildAt(i).setEnabled(true);
            } else {
                indicatorLL.getChildAt(i).setEnabled(false);
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.close:
            case R.id.finish:
                dismiss();
                break;
            case R.id.start_calibration:
                if (DJIHelper.getInstance().getFlightController() != null) {
                    refreshView(0);
                    DJIHelper.getInstance().getFlightController().startIMUCalibration(new CommonCallbacks.CompletionCallback() {
                        @Override
                        public void onResult(DJIError djiError) {
                            if (djiError != null) {
                                ToastUtil.show(djiError.getDescription());
                                ContextUtil.getHandler().post(() -> dismiss());
                            }
                        }
                    });
                } else {
                    ToastUtil.show("请连接无人机");
                }
                break;
        }

    }

}
*/
