package dji.sampleV5.aircraft.page;

import android.app.AlertDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import java.text.SimpleDateFormat;
import java.util.Locale;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.common.voice.SoundPlayerBean;
import dji.sampleV5.aircraft.data.mission.MissionDetail;
import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.TaskInfo;
import dji.sampleV5.aircraft.databinding.ActivityRecentmissionBinding;
import dji.sampleV5.aircraft.mvvm.net.ApiConfig;
import dji.sampleV5.aircraft.mvvm.net.bean.HistoryInfoBean;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.page.fly.controller.MissionPhotoUploaderForRecent;
import dji.sampleV5.aircraft.page.fly.controller.OnUploadSuccessListener;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;
import dji.v5.utils.common.JsonUtil;

public class RecentMissionDetailActivity extends AppCompatActivity {
    private boolean isStart = false;
    private ActivityRecentmissionBinding binding;
    private SimpleDateFormat sf = new SimpleDateFormat("MM-dd HH:mm:ss", Locale.CHINA);
    private String missionBatch;
//    private TaskInfo taskInfo;
    private HistoryInfoBean historyInfoBean;
    private MissionPhotoUploaderForRecent missionPhotoUploaderForRecent;
    private AlertDialog netDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ImmerseUtil.startImmerse(this);
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.activity_recentmission);

        if (getIntent().getStringExtra("taskInfo") == null) {
            missionBatch = SpUtil.getMissionBatch();
            MissionDetail missionDetail = SpUtil.getMissionData(missionBatch);
            binding.missionName.setText(String.format("任务名称：%s", missionDetail.getWaypointMission().getName()));
            binding.missionPoint.setText("任务航点数量：" + missionDetail.getWaypointMission().getWaypointList().size());
            binding.missionTime.setText(String.format("任务时间：%s", sf.format(missionDetail.getStartTime())));
            if (missionDetail.isComplete()) {
                binding.start.setVisibility(View.VISIBLE);
                binding.missionStatus.setText("任务状态：任务已完成，等待上传");
            } else {
                binding.start.setVisibility(View.INVISIBLE);
                binding.missionStatus.setText("任务状态：任务尚未完成，已经执行到第" + missionDetail.getCurrentPosition() + "个点");
            }
        } else {
            historyInfoBean = JsonUtil.toBean(getIntent().getStringExtra("taskInfo"), HistoryInfoBean.class);
            missionBatch = historyInfoBean.getMissionBatch();
            binding.missionPoint.setVisibility(View.INVISIBLE);
            binding.title.setText("任务数据上传");
            binding.missionName.setText(String.format("任务名称：%s", historyInfoBean.getMissionName()));
            binding.missionTime.setText(String.format("任务时间：%s", historyInfoBean.getUavStartTime()));
            binding.missionStatus.setText("任务状态：任务已完成，等待上传");
        }

        initNetDialog();

        binding.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isStart) {
                    ToastUtil.show("正在上传下载，无法退出");
                } else {
                    finish();
                }

            }
        });

        binding.start.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(isStart){
                    ToastUtil.show("数据处理中，请稍等");
                    return;
                }
                isStart = true;
                if(missionPhotoUploaderForRecent == null){
                    missionPhotoUploaderForRecent = MissionPhotoUploaderForRecent.getInstance();
                }
                missionPhotoUploaderForRecent.onMissionComplete(missionBatch, historyInfoBean, binding, new OnUploadSuccessListener() {
                    @Override
                    public void onStart() {
                        isStart = true;
                    }

                    @Override
                    public void onSuccess() {
                        isStart = false;
                        finish();
                    }

                    @Override
                    public void onError() {
                        isStart = false;
                    }

                    @Override
                    public void onNetWorkError() {
                        Log.e("TAG", "onNetWorkError!!: "+netDialog.isShowing());
                        if (!netDialog.isShowing()) {
                            try {
                                netDialog.show();
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }
                    }
                });
            }
        });
    }

    private void initNetDialog() {
        if (netDialog == null) {
            netDialog = new AlertDialog.Builder(this)
                    .setMessage("网络异常，请检查网络,点击确定可以尝试继续上传")
                    .setPositiveButton(R.string.dialog_ok, (dialog, which) -> {
                        if (ApiConfig.INSTANCE.getIS_INNER_NETWORK()) {
                            missionPhotoUploaderForRecent.reConnectMinio();
                        } else {
                            missionPhotoUploaderForRecent.reConnectOss();
                        }
//                        missionPhotoUploaderForRecent.reConnectFtp();
                    })
                    .setNegativeButton(R.string.dialog_cancel, (dialog, which) -> {
                        //binding.downloadLL.setVisibility(View.INVISIBLE);
                        missionPhotoUploaderForRecent.finish();
                        isStart = false;
                    })
                    .create();
            netDialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void onBackPressed() {
        if (isStart) {
            ToastUtil.show("正在上传下载，无法退出");
        } else {
            finish();
        }
    }
}
