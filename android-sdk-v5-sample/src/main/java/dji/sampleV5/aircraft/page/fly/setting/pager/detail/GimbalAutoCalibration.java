package dji.sampleV5.aircraft.page.fly.setting.pager.detail;

import android.app.AlertDialog;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DefaultLayoutActivity;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.page.fly.AircraftFragmentManager;
import dji.sampleV5.aircraft.util.phone.ImmerseUtil;


public class GimbalAutoCalibration {

    private AlertDialog mAlertDialog;

    public void onClickAutoCalibration(final int gimbalIndex) {
        AlertDialog.Builder builder = new AlertDialog.Builder(ContextUtil.getCurrentActivity());
        View view = View.inflate(ContextUtil.getApplicationContext(), R.layout.gimbal_calibration_dialog_hint, null);
        Button bt_calibration_ok = view.findViewById(R.id.bt_Calibration_OK);
        Button bt_calibration_cancel = view.findViewById(R.id.bt_Calibration_cancel);
        ImageView iv_calibration_exit = view.findViewById(R.id.iv_calibration_exit);
        builder.setView(view);

        //退出
        iv_calibration_exit.setOnClickListener(v -> mAlertDialog.dismiss());
        //取消
        bt_calibration_cancel.setOnClickListener(v -> mAlertDialog.dismiss());
        //确定
        bt_calibration_ok.setOnClickListener(v -> {
            AircraftFragmentManager manager = ((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).getAircraftFragmentManager();
            manager.removeFragment(AircraftFragmentManager.SETTING);
            //((DefaultLayoutActivity) ContextUtil.getCurrentActivity()).showGimbalAutoCalibrationDialog(gimbalIndex);

            mAlertDialog.dismiss();
        });
        mAlertDialog = builder.create();
        mAlertDialog.setCanceledOnTouchOutside(false);
        ImmerseUtil.showDialog(mAlertDialog);
    }

    public void setConnect(Boolean connect) {
    }
}
