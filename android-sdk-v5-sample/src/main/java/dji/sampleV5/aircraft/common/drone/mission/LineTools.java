package dji.sampleV5.aircraft.common.drone.mission;


import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class LineTools {
	/**
	 * Finds the line that has the start or tip closest to a point.
	 * 
	 * @param point
	 *            Point to the distance will be minimized
	 * @param list
	 *            A list of lines to search
	 * @return The closest Line
	 */
	public static LineLatLng findClosestLineToPoint(AppLatLng point, List<LineLatLng> list) {
		LineLatLng answer = list.get(0);
		double shortest = Double.MAX_VALUE;

		for (LineLatLng line : list) {
			double ans1 = GeoTools.getApproximatedDistance(point, line.start);
			double ans2 = GeoTools.getApproximatedDistance(point, line.end);
			AppLatLng shorterpnt = ans1 < ans2 ? line.start : line.end;

			if (shortest > GeoTools.getApproximatedDistance(point, shorterpnt)) {
				answer = line;
				shortest = GeoTools.getApproximatedDistance(point, shorterpnt);
			}
		}
		return answer;
	}

	/**
	 * Finds the intersection of two lines http://stackoverflow.com/questions/
	 * 1119451/how-to-tell-if-a-line-intersects -a-polygon-in-c
	 */
	public static AppLatLng FindLineIntersection(LineLatLng first, LineLatLng second) {
		double denom = ((first.getEnd().getLat() - first.getStart().getLat()) * (second.getEnd().getLng() - second
				.getStart().getLng()))
				- ((first.getEnd().getLng() - first.getStart().getLng()) * (second.getEnd().getLat() - second
				.getStart().getLat()));
		if (denom == 0){
			//Parallel lines
			return null;
		}
		double numer = ((first.getStart().getLng() - second.getStart().getLng()) * (second.getEnd()
				.getLat() - second.getStart().getLat()))
				- ((first.getStart().getLat() - second.getStart().getLat()) * (second.getEnd().getLng() - second
				.getStart().getLng()));
		double r = numer / denom;
		double numer2 = ((first.getStart().getLng() - second.getStart().getLng()) * (first.getEnd()
				.getLat() - first.getStart().getLat()))
				- ((first.getStart().getLat() - second.getStart().getLat()) * (first.getEnd().getLng() - first
				.getStart().getLng()));
		double s = numer2 / denom;
		if ((dataFormat(r) < 0 || dataFormat(r) > 1) || (dataFormat(s) < 0 || dataFormat(s) > 1)){
			//No intersection
			return null;
		}
		// Find intersection point
		double x = first.getStart().getLat()
				+ (r * (first.getEnd().getLat() - first.getStart().getLat()));
		double y = first.getStart().getLng()
				+ (r * (first.getEnd().getLng() - first.getStart().getLng()));
		return (new AppLatLng(x, y));
	}

	private static double dataFormat(double d){
        DecimalFormat df = new DecimalFormat("#.0000000");
        return Double.parseDouble(df.format(d));
    }

	public static LineLatLng findExternalPoints(ArrayList<AppLatLng> crosses) {
		AppLatLng meanCoord = new CoordBounds(crosses).getMiddle();
		AppLatLng start = PointTools.findFarthestPoint(crosses, meanCoord);
		AppLatLng end = PointTools.findFarthestPoint(crosses, start);
		return new LineLatLng(start, end);
	}

    public static ArrayList<AppLatLng> findCrossings(List<LineLatLng> lines, LineLatLng gridLine) {
        ArrayList<AppLatLng> crossings = new ArrayList<>();
        for (LineLatLng polyLine : lines) {
            AppLatLng intersection = LineTools.FindLineIntersection(polyLine, gridLine);
            if(intersection != null)
                crossings.add(intersection);
        }

        return crossings;
    }

	/**
	 * 返回与传入值相垂直的角
     */
	public static double getLinePolarAngleVertical(double anlge){
		return (anlge + 90) % 360;
	}
}
