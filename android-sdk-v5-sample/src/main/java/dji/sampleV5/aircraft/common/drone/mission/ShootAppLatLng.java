package dji.sampleV5.aircraft.common.drone.mission;


import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class ShootAppLatLng {

    private boolean isShoot;
    private AppLatLng appLatLng;

    public ShootAppLatLng(AppLatLng appLatLng, boolean isShoot) {
        this.appLatLng = appLatLng;
        this.isShoot = isShoot;
    }

    public boolean isShoot() {
        return isShoot;
    }

    public void setShoot(boolean shoot) {
        isShoot = shoot;
    }

    public AppLatLng getAppLatLng() {
        return appLatLng;
    }

    public void setAppLatLng(AppLatLng appLatLng) {
        this.appLatLng = appLatLng;
    }
}
