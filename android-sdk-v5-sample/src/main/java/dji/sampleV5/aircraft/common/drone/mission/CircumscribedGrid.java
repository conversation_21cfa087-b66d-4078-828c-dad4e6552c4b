package dji.sampleV5.aircraft.common.drone.mission;


import static dji.sampleV5.aircraft.mvvm.util.plan.PolygonRouteGenerator.MAX_NUMBER_OF_LINES;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;


/**
 * 限定网格线
 * 根据传入多边形输出切割线
 *
 * 1、确定切割线起始点位置：多边形几何中心沿航线角度-135方向
 * 2、按航线角度方向延伸切割长度，确定终点位置
 * 3、根据1、2得到一条切割线
 * 4、按航线角度+90度方向、长度为lineDistance，确定下一条切割线
 * 5、返回所有切割线
 */
public class CircumscribedGrid {

    private double maxCutDistance;    //需要切割的距离，可以适当放宽切割长度，避免切割的边缘问题

    /**
     * 涵盖多边形的切割线
     *
     * @param polygonPoints 多边形坐标点
     * @param angle         生成航线角度
     * @param lineDistance  切割线间距
     * @return 涵盖多边形的切割线
     * @throws Exception too many lines
     */
    public List<LineLatLng> getCutGrid(List<AppLatLng> polygonPoints, double angle, double lineDistance) throws Exception {
        AppLatLng startCutPoint = getStartPoint(polygonPoints, angle);
        return calculateGrid(startCutPoint, lineDistance, angle);
    }

    /**
     * 确定起始切割点位置
     * 起始切割点为航线角度-135度方位
     *
     * @param polygonPoints 输入多边形位置坐标
     * @param routeAngle    生成航线角度
     * @return 起始切割点的位置坐标
     */
    private AppLatLng getStartPoint(List<AppLatLng> polygonPoints, double routeAngle) {
        CoordBounds bounds = new CoordBounds(polygonPoints);
        AppLatLng middlePoint = bounds.getMiddle();
        // 绘制区域外切圆的半径
        // 1.5是为了防止计算误差导致线切不到规划区域而增加半径
        double circle = bounds.getDiag(middlePoint) * 1.5;
        // 绘制区域外切圆的外切正方形的斜边距的一半
        double outerRectHalfDistance = circle * 1.41421356;

        AppLatLng startPoint = GeoTools.newCoordFromBearingAndDistance(middlePoint, routeAngle - 135,
                outerRectHalfDistance);

        maxCutDistance = circle * 2;

        return startPoint;
    }


    /**
     * 涵盖多边形的切割线
     *
     * @param startCutPoint 起始点
     * @param lineDistance  切割线间距
     * @param routeAngle    生成航线角度
     * @return grid
     * @throws GridWithTooManyLines too many
     */
    private List<LineLatLng> calculateGrid(AppLatLng startCutPoint, double lineDistance, double routeAngle) throws GridWithTooManyLines {
        List<LineLatLng> grid = new ArrayList<>();
        int lines = 0;
        AppLatLng startPoint = startCutPoint;
        while (lines * lineDistance < maxCutDistance) {
            AppLatLng endPoint = GeoTools.newCoordFromBearingAndDistance(startPoint, routeAngle, maxCutDistance);

            LineLatLng line = new LineLatLng(startPoint, endPoint);
            grid.add(line);

            startPoint = GeoTools.newCoordFromBearingAndDistance(startPoint, routeAngle + 90, lineDistance);
            lines++;
            if (lines > MAX_NUMBER_OF_LINES) {
                throw new GridWithTooManyLines();
            }
        }

        return grid;
    }


    public class GridWithTooManyLines extends Exception {
        GridWithTooManyLines() {
            super("too many lines");
        }
    }

}
