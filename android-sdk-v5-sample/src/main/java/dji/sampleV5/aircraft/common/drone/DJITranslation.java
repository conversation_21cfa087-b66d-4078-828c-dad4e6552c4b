package dji.sampleV5.aircraft.common.drone;

/**
 * Created by caojin on 2017/1/6.
 */

public class DJITranslation {
    /**
     * 极坐标转DJI坐标
     * <p>
     * 0-north
     * dji[-180, 180]
     * 极坐标[0, 360]
     */
    public static double polar2DJI(double angle) {
        double validAngle = angle % 360;
        if (validAngle >= 0 && validAngle <= 180) {
            return angle;
        } else {
            return validAngle - 360;
        }
    }

    /**
     * DJI坐标转极坐标
     */
    public static double DJI2Polar(double angle) {
        return (360 - angle + 90) % 360;
    }
}
