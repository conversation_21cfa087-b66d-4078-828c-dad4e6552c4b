package dji.sampleV5.aircraft.common.drone.mission;


import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

/**
 * Created by caojin on 2017/1/10.
 */

public class LineLatLng {
    public AppLatLng start;
    public AppLatLng end;

    public LineLatLng(){}

    public LineLatLng(AppLatLng start, AppLatLng end){
        this.start = start;
        this.end = end;
    }

    public AppLatLng getStart() {
        return start;
    }

    public void setStart(AppLatLng start) {
        this.start = start;
    }

    public AppLatLng getEnd() {
        return end;
    }

    public void setEnd(AppLatLng end) {
        this.end = end;
    }

    public AppLatLng getFarthestEndpointTo(AppLatLng point) {
        AppLatLng temp = getClosestEndpointTo(point);
        if (temp.getLat() == start.getLat() && temp.getLng() == start.getLng()) {
            return end;
        } else {
            return start;
        }
    }

    public AppLatLng getClosestEndpointTo(AppLatLng point) {
        if (getDistanceToStart(point) < getDistanceToEnd(point)) {
            return start;
        } else {
            return end;
        }
    }

    private Double getDistanceToEnd(AppLatLng point) {
        return GeoTools.getApproximatedDistance(end, point);
    }

    private Double getDistanceToStart(AppLatLng point) {
        return GeoTools.getApproximatedDistance(start, point);
    }

    @Override
    public String toString() {
        return "LineLatLng{" +
                "start=" + start +
                ", end=" + end +
                '}';
    }
}
