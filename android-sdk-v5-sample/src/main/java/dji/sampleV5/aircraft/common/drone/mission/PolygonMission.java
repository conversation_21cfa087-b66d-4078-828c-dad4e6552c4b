package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.net.bean.CameraInfoBean;
import dji.sampleV5.aircraft.page.plan.BaseMission;

/**
 * polygon mode
 * 功能：
 * 1.生成可视化航线
 * 2.生成SDK支持的航线
 * 3.将配置信息实时显示
 */

public class PolygonMission extends BaseMission {
    private float overlap;                  // 航向重叠率
    private float genSideLap;               // 旁向重叠率[10%, 99%]
    private float genAngle;                 // 主航线角度[0, 360]，单位：度
    private float genMargin;                // 主航线边距[-30, 30]，单位：m
    private float gimbalPitch;              // 云台俯仰角度[-90, 0]，单位：度

    private int shootNumber;                // 拍照数量
    private float shootDistance;              // 拍照间隔，单位 m
    private float shootTimeInterval;        // 拍照间隔，单位 s
    private int hoverAfterShoot;            // 拍照完成后悬停时0~32767ms

    private float missionArea;                //任务面积
    private float startHeight;                //起飞点高度
    private int inHeight;                   //进入巡区高度
    private int outHeight;                  //退出巡区高度
    private int hoverPhoto;                 //飞行模式
    private String thumbnailBase64;         //缩略图

    private GenMode genMode;                          // 生成航线模式
    private RouteMode routeMode;                      // 航线模式
    private TakePhotoMode takePhotoMode;              // 航线拍照模式
    private FinishedAction finishedAction;            // 任务结束后执行动作
    private CameraOrientation cameraOrientation;      // 相机朝向
    private List<AppLatLng> pointList = new ArrayList<>();  // 航点列表

    private CameraInfoBean cameraSetting = new CameraInfoBean();  // 相机配置信息


    public PolygonMission() {
    }

    public int getHoverAfterShoot() {
        return hoverAfterShoot;
    }

    public void setHoverAfterShoot(int hoverAfterShoot) {
        this.hoverAfterShoot = hoverAfterShoot;
    }

    public List<AppLatLng> getPointList() {
        return pointList;
    }

    public void setPointList(List<AppLatLng> pointList) {
        this.pointList.clear();
        this.pointList.addAll(pointList);
    }

    public void add(AppLatLng point) {
        this.pointList.add(point);
    }

    public void addAll(List<AppLatLng> pointList) {
        this.pointList.addAll(pointList);
    }

    public GenMode getGenMode() {
        return genMode;
    }

    public void setGenMode(GenMode genMode) {
        this.genMode = genMode;
    }

    public float getGenSideLap() {
        return genSideLap;
    }

    public void setGenSideLap(float genSideLap) {
        this.genSideLap = genSideLap;
    }

    public float getGenAngle() {
        return genAngle;
    }

    public void setGenAngle(float genAngle) {
        this.genAngle = genAngle;
    }

    public float getGenMargin() {
        return genMargin;
    }

    public void setGenMargin(float genMargin) {
        this.genMargin = genMargin;
    }

    public float getGimbalPitch() {
        return gimbalPitch;
    }

    public void setGimbalPitch(float gimbalPitch) {
        this.gimbalPitch = gimbalPitch;
    }

    public CameraOrientation getCameraOrientation() {
        return cameraOrientation;
    }

    public void setCameraOrientation(CameraOrientation cameraOrientation) {
        this.cameraOrientation = cameraOrientation;
    }

    public TakePhotoMode getTakePhotoMode() {
        return takePhotoMode;
    }

    public void setTakePhotoMode(TakePhotoMode takePhotoMode) {
        this.takePhotoMode = takePhotoMode;
    }

    public float getMissionArea() {
        return missionArea;
    }

    public void setMissionArea(float missionArea) {
        this.missionArea = missionArea;
    }


    public float getStartHeight() {
        return startHeight;
    }

    public void setStartHeight(float startHeight) {
        this.startHeight = startHeight;
    }

    public int getInHeight() {
        return inHeight;
    }

    public void setInHeight(int inHeight) {
        this.inHeight = inHeight;
    }

    public int getOutHeight() {
        return outHeight;
    }

    public void setOutHeight(int outHeight) {
        this.outHeight = outHeight;
    }


    public int getHoverPhoto() {
        return hoverPhoto;
    }

    public void setHoverPhoto(int hoverPhoto) {
        this.hoverPhoto = hoverPhoto;
    }


    public String getThumbnailBase64() {
        return thumbnailBase64;
    }

    public void setThumbnailBase64(String thumbnailBase64) {
        this.thumbnailBase64 = thumbnailBase64;
    }

    public float getOverlap() {
        return overlap;
    }

    public void setOverlap(float overlap) {
        this.overlap = overlap;
    }

    public FinishedAction getFinishedAction() {
        return finishedAction;
    }

    public void setFinishedAction(FinishedAction finishedAction) {
        this.finishedAction = finishedAction;
    }

    public int getShootNumber() {
        return shootNumber;
    }

    public void setShootNumber(int shootNumber) {
        this.shootNumber = shootNumber;
    }

    public float getShootDistance() {
        return shootDistance;
    }

    public void setShootDistance(float shootDistance) {
        this.shootDistance = shootDistance;
    }


    public float getShootTimeInterval() {
        return shootTimeInterval;
    }

    public void setShootTimeInterval(float shootTimeInterval) {
        this.shootTimeInterval = shootTimeInterval;
    }


    public CameraInfoBean getCameraSetting() {
        return this.cameraSetting;
    }

    public void setCameraSetting(CameraInfoBean cameraSetting) {
        this.cameraSetting.update(cameraSetting);
    }

    @Override
    public String toString() {
        return super.toString() + "\nSurveyMission{" +
                "pointList=" + pointList +
                ", genMode=" + genMode +
                ", overlap=" + overlap +
                ", genSideLap=" + genSideLap +
                ", genAngle=" + genAngle +
                ", genMargin=" + genMargin +
                ", cameraSetting=" + cameraSetting +
                ", takePhotoMode=" + takePhotoMode +
                ", gimbalPitch=" + gimbalPitch +
                ", cameraOrientation=" + cameraOrientation +
                ", finishedAction=" + finishedAction +
                ", inHeight=" + inHeight +
                ", outHeight=" + outHeight +
                ", shootNumber=" + shootNumber +
                '}';
    }

    public RouteMode getRouteMode() {
        return routeMode;
    }

    public void setRouteMode(RouteMode routeMode) {
        this.routeMode = routeMode;
    }

    public enum CameraOrientation {
        Parallel,
        Vertical
    }

    public enum TakePhotoMode {
        DistanceInterval,
        TimeInterval,
        HoverOnWaypoint
    }

    public enum GenMode {
        Scan,
        Area
    }

    public enum RouteMode {
        snake,
        well
    }

    public enum FinishedAction {
        GoHome,
        NoAction,
        AutoLand
    }

}
