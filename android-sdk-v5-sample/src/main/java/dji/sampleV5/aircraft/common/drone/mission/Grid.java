package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class Grid {
    public List<AppLatLng> gridPoints;
    private List<AppLatLng> boundPoints = new ArrayList<>();
    private List<ShootAppLatLng> shootGridPoints = new ArrayList<>();
    private List<AppLatLng> cameraLocations;
    private AppLatLng startPoint;
    private AppLatLng endPoint;
    private int routLength;
    private int wpNum;

    public Grid() {}

    public List<AppLatLng> getGridPoints() {
        return gridPoints;
    }

    public void setGridPoints(List<AppLatLng> gridPoints) {
        this.gridPoints = gridPoints;
    }

    public List<AppLatLng> getCameraLocations() {
        return cameraLocations;
    }

    public void setCameraLocations(List<AppLatLng> cameraLocations) {
        this.cameraLocations = cameraLocations;
    }

    public int getWpNum() {
        return wpNum;
    }

    public void setWpNum(int wpNum) {
        this.wpNum = wpNum;
    }

    public AppLatLng getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(AppLatLng startPoint) {
        this.startPoint = startPoint;
    }

    public AppLatLng getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(AppLatLng endPoint) {
        this.endPoint = endPoint;
    }

    public int getRoutLength() {
        return routLength;
    }

    public void setRoutLength(int routLength) {
        this.routLength = routLength;
    }

    public List<AppLatLng> getBoundPoints() {
        return boundPoints;
    }

    public List<ShootAppLatLng> getShootGridPoints() {
        return shootGridPoints;
    }

    public void setShootGridPoints(List<ShootAppLatLng> shootGridPoints) {
        this.shootGridPoints = shootGridPoints;
    }
}