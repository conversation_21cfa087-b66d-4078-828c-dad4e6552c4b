package dji.sampleV5.aircraft.common.drone.mission;


import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.hjq.toast.Toaster;
import com.thoughtworks.xstream.XStream;

import org.json.JSONException;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.DJIAircraftApplication;
import dji.sampleV5.aircraft.enums.PayloadLensIndexEnum;
import dji.sampleV5.aircraft.kmz.model.common.Action;
import dji.sampleV5.aircraft.kmz.model.common.ActionActuatorFuncParam;
import dji.sampleV5.aircraft.kmz.model.common.ActionGroup;
import dji.sampleV5.aircraft.kmz.model.common.ActionTrigger;
import dji.sampleV5.aircraft.kmz.model.kml.DroneInfo;
import dji.sampleV5.aircraft.kmz.model.kml.MissionConfig;
import dji.sampleV5.aircraft.kmz.model.kml.PayloadInfo;
import dji.sampleV5.aircraft.kmz.model.kml.Point;
import dji.sampleV5.aircraft.kmz.model.wpml.Folder;
import dji.sampleV5.aircraft.kmz.model.wpml.Placemark;
import dji.sampleV5.aircraft.kmz.model.wpml.WaylinesWpml;
import dji.sampleV5.aircraft.kmz.model.wpml.WaypointHeadingParam;
import dji.sampleV5.aircraft.kmz.model.wpml.WaypointTurnParam;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.net.bean.Flightpath;
import dji.sampleV5.aircraft.net.bean.FlightpathAction;
import dji.sampleV5.aircraft.net.bean.MissionJson;
import dji.sampleV5.aircraft.page.plan.WaypointMission;
import dji.sampleV5.aircraft.util.FileUtil;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.waypointV2.MISSION_ACTION;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.manager.KeyManager;
import dji.v5.manager.aircraft.waypoint3.WaylineExecutingInfoListener;
import dji.v5.manager.aircraft.waypoint3.WaypointMissionExecuteStateListener;
import dji.v5.manager.aircraft.waypoint3.WaypointMissionManager;
import dji.v5.manager.aircraft.waypoint3.model.BreakPointInfo;
import dji.v5.manager.aircraft.waypoint3.model.WaylineExecutingInfo;
import dji.v5.manager.aircraft.waypoint3.model.WaypointMissionExecuteState;
import dji.v5.utils.common.DiskUtil;
import dji.v5.utils.common.FileUtils;
import dji.v5.ux.core.util.SpUtil;

/**
 * 说明：
 * 1.上传任务后，仍然可以上传
 * 2.任务停止后，需重新上传
 * <p>
 * 任务结束回调触发时机：
 * 1.任务结束自动返航：最后一个航点执行完毕不触发
 * 2.任务结束悬停：最后一个航点执行完毕触发
 * 3.任务结束自动降落：最后一个航点执行完毕不触发
 * <p>
 * UNKNOWN          the state of the operator is unknown. It is the initial state when the operator is just created
 * DISCONNECTED     the connection between the mobile device, remote controller and aircraft is broken
 * NOT_SUPPORTED    the connected product does not support waypoint mission
 * RECOVERING       the connection between the mobile device, remote controller and aircraft is built-up. The operator is synchronizing the state from the aircraft
 * READY_TO_UPLOAD  the aircraft is ready to upload a mission
 * UPLOADING        the uploading is started successfully. Detail information for each waypoint is being uploaded one bye one
 * READY_TO_EXECUTE waypoint mission is uploaded completely and the aircraft is ready to start the execution
 * EXECUTING        the execution is started successfully
 * EXECUTION_PAUSED waypoint mission is paused successfully. User can call resumeMission to continue the execution
 */

public class WaypointMissionHelper extends BaseMissionHelper {
    //private static final String TAG = "MissionHelper";
    private WaypointMissionExecuteState waypointMissionExecuteState;

    private WaypointMission mission;
    //private static final String WAYPOINT_SAMPLE_FILE_NAME  = "hx0712-2.kmz";
    private static final String WAYPOINT_SAMPLE_FILE_NAME = "outZip.kmz";
    private static final String WAYPOINT_SAMPLE_FILE_DIR = "waypoint/";
    private static final String WAYPOINT_SAMPLE_FILE_CACHE_DIR = "waypoint/cache/";
    private static final String WAYPOINT_FILE_TAG = ".kmz";
    private String curMissionPath;
    private List<Flightpath> flightPaths = new ArrayList<>();

    private ProductType productType;
    private MissionJson missionJson;
    private int missionType = 0;
    private long lastUploadTime;
    private long lastFinishTime;
    private String payloadLensIndex = "zoom,ir";

    public WaypointMissionHelper() {
        init();
    }

    private void init() {
        WaypointMissionManager.getInstance().addWaypointMissionExecuteStateListener(new WaypointMissionExecuteStateListener() {
            @Override
            public void onMissionStateUpdate(WaypointMissionExecuteState missionState) {
                waypointMissionExecuteState = missionState;
                if (waypointMissionExecuteState == WaypointMissionExecuteState.FINISHED) {
                    lastFinishTime = System.currentTimeMillis();
                    passResult(MISSION_FINISH, VALUE_SUCCEED, "");
                } else if (waypointMissionExecuteState == WaypointMissionExecuteState.INTERRUPTED) {
                    passResult(MISSION_PAUSE, VALUE_SUCCEED, "");
                }
                Log.e("missionState", "onMissionStateUpdate: " + missionState.name());
            }
        });
        WaypointMissionManager.getInstance().addWaylineExecutingInfoListener(new WaylineExecutingInfoListener() {
            @Override
            public void onWaylineExecutingInfoUpdate(WaylineExecutingInfo excutingWaylineInfo) {
                int current = excutingWaylineInfo.getCurrentWaypointIndex();
                if (callback != null) {
                    callback.onProgress(current);
                }
            }

            @Override
            public void onWaylineExecutingInterruptReasonUpdate(IDJIError error) {
                if (error != null && !TextUtils.isEmpty(error.description())) {
                    ToastUtil.show("任务中断：" + error.description());
                }
            }
        });
    }

    @Override
    public void removeListener() {
        WaypointMissionManager.getInstance().clearAllWaypointMissionExecuteStateListener();
        WaypointMissionManager.getInstance().clearAllWaylineExecutingInfoListener();
    }

    @Override
    public int getCurrentState() {
        if (waypointMissionExecuteState != null) {
            if (waypointMissionExecuteState == WaypointMissionExecuteState.DISCONNECTED) {
                return DISCONNECTED;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.UNKNOWN) {
                return UNKNOWN;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.EXECUTING) {
                return EXECUTING;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.INTERRUPTED) {
                return EXECUTION_PAUSED;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.READY) {
                return READY_TO_EXECUTE;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.RECOVERING) {
                return RECOVERING;
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.UPLOADING) {
                return UPLOADING;
            } else {
                return UNKNOWN;
            }
        }
        return UNKNOWN;
    }

    @Override
    public void setFlyPath(List<Flightpath> flightPaths, MissionJson missionJson) {
        //为指点飞行单独处理一下,需要增加一个点,这里只为了获取动作，所以点的经纬度不做处理了
        if (flightPaths.size() == 1) {
            Flightpath flightpathMiddle = new Flightpath();//造一个空的点，只为了跟航点的个数能匹配上
            List<FlightpathAction> actions = new ArrayList<>();
            flightpathMiddle.setActions(actions);
            flightPaths.add(0, flightpathMiddle);
        }
        this.flightPaths = flightPaths;
        this.missionJson = missionJson;

        String params = missionJson.getParams();
        try {
            org.json.JSONObject jsonObject = new org.json.JSONObject(params);
            missionType = jsonObject.getInt("missionType");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        /*判断镜头存储类型*/
        if (missionJson.getCameraSetting() != null && missionJson.getCameraSetting().getMediaStorageViewTypeArray() != null) {
            payloadLensIndex = PayloadLensIndexEnum.getPayloadLensIndexs(missionJson.getCameraSetting().getMediaStorageViewTypeArray());
            Log.e("TAG", "payloadLensIndex: " + payloadLensIndex);
        }
    }

    @Override
    public void setWayPointMission(WaypointMission mission) {
        this.mission = mission;
        //curMissionPath = DiskUtil.getExternalCacheDirPath(ContextUtil.getApplicationContext(), WAYPOINT_SAMPLE_FILE_DIR + WAYPOINT_SAMPLE_FILE_NAME);
        curMissionPath = Environment.getExternalStoragePublicDirectory("/outZip.kmz").getPath();

        MissionConfig missionConfig = new MissionConfig();
        missionConfig.setFlyToWaylineMode("safely");
        //航线结束动作
        String finishPosition = SpUtil.getFinishAction();
        missionConfig.setFinishAction(finishPosition);
        //任务失控相关设置
        String rcLost = SpUtil.getExitOnRCLost();
        String lostAction = SpUtil.getExecuteRCLostAction();
        missionConfig.setExitOnRcLost(rcLost);
        if (!rcLost.equals("goContinue")) {
            missionConfig.setExecuteRcLostAction(lostAction);
        }

        float uavSTAltitude = missionJson.getUavSTAltitude();
        if (uavSTAltitude > 0) {
            missionConfig.setTakeOffSecurityHeight(Float.parseFloat(String.valueOf(uavSTAltitude)));
        } else {
            missionConfig.setTakeOffSecurityHeight(120f);
        }
        missionConfig.setGlobalTransitionalSpeed(mission.getFlySpeed());
        DroneInfo droneInfo = new DroneInfo();
        PayloadInfo payloadInfo = new PayloadInfo();
        productType = DJIAircraftApplication.getInstance().getProductType();
        if (productType == ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES) {
            droneInfo.setDroneEnumValue(77);
            droneInfo.setDroneSubEnumValue(1);

            payloadInfo.setPayloadEnumValue(67);
            payloadInfo.setPayloadSubEnumValue(0);
            payloadInfo.setPayloadPositionIndex(0);
        } else if (productType == ProductType.M30_SERIES) {
            droneInfo.setDroneEnumValue(67);
            droneInfo.setDroneSubEnumValue(1);

            payloadInfo.setPayloadEnumValue(53);
            payloadInfo.setPayloadSubEnumValue(0);
            payloadInfo.setPayloadPositionIndex(0);
        } else if (productType == ProductType.M300_RTK) {
            droneInfo.setDroneEnumValue(60);
            droneInfo.setDroneSubEnumValue(0);

            payloadInfo.setPayloadEnumValue(43);
            payloadInfo.setPayloadSubEnumValue(0);
            payloadInfo.setPayloadPositionIndex(0);
        } else if (productType == ProductType.M350_RTK) {
            droneInfo.setDroneEnumValue(89);
            droneInfo.setDroneSubEnumValue(1);

            payloadInfo.setPayloadEnumValue(43);
            payloadInfo.setPayloadSubEnumValue(0);
            payloadInfo.setPayloadPositionIndex(0);
        }

        missionConfig.setDroneInfo(droneInfo);
        missionConfig.setPayloadInfo(payloadInfo);

        Folder folder = new Folder();
        folder.setTemplateId(0);
        if(missionJson.getMission().get(0).getAltitudeType() == 2){
            folder.setExecuteHeightMode("WGS84");
        }else {
            folder.setExecuteHeightMode("relativeToStartPoint");
        }
        folder.setWaylineId(0);
        folder.setAutoFlightSpeed(mission.getFlySpeed());
        List<Placemark> placeMarks = getPlaceMarkList(mission);
        folder.setPlacemark(placeMarks);

        WaylinesWpml waylinesWpml = new WaylinesWpml();
        waylinesWpml.setMissionConfig(missionConfig);
        waylinesWpml.setFolder(folder);


        String xml = objectToXml(waylinesWpml);
        save2File(xml);
    }

    private List<Placemark> getPlaceMarkList(WaypointMission mission) {
        List<WaypointMission.Waypoint> waypointList = mission.getWaypointList();
        //获取照片存储格式
        payloadLensIndex = SpUtil.getSavePictureFormat();
        //这里单独判断一下，如果是一个点代表是指点飞行、环绕飞行，需要增加一个点
        if (waypointList.size() == 1) {
            LocationCoordinate2D startLocation = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
            if (startLocation == null) {
                ToastUtil.show("无法获取当前飞行器位置，已使用返航点计算补点位置！");
                startLocation = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyHomeLocation));
            }
            AppLatLng endLocation = waypointList.get(0).getLatLng();
            AppLatLng middleLocation = new AppLatLng();
            middleLocation.setLat((startLocation.getLatitude() + endLocation.getLat()) / 2);
            middleLocation.setLng((startLocation.getLongitude() + endLocation.getLng()) / 2);
            WaypointMission.Waypoint middleWaypoint = new WaypointMission.Waypoint();
            middleWaypoint.setLatLng(middleLocation);
            middleWaypoint.setPointName("安全点位");
            middleWaypoint.setAltitude(waypointList.get(0).getAltitude());
            middleWaypoint.setHeading(waypointList.get(0).getHeading());
            middleWaypoint.setGimbalPitch(waypointList.get(0).getGimbalPitch());
            if (waypointList.get(0).getSpeed() == 0) {
                middleWaypoint.setSpeed(missionJson.getMission().get(0).getAutoflightSpeed());
            } else {
                middleWaypoint.setSpeed(waypointList.get(0).getSpeed());
            }
            waypointList.add(0, middleWaypoint);
        }

        List<Placemark> placeMarks = new ArrayList<>();
        for (int i = 0; i < waypointList.size(); i++) {
            Placemark placemark = new Placemark();
            Point point = new Point();
            AppLatLng appLatLng = waypointList.get(i).getLatLng();
            String location = appLatLng.getLng() + "," + appLatLng.getLat();
            point.setCoordinates(location);
            placemark.setPoint(point);
            placemark.setIndex(i);
            placemark.setExecuteHeight(waypointList.get(i).getAltitude());
            if (waypointList.get(i).getSpeed() == 0) {
                placemark.setWaypointSpeed(missionJson.getMission().get(0).getAutoflightSpeed());
            } else {
                placemark.setWaypointSpeed(waypointList.get(i).getSpeed());
            }

            WaypointHeadingParam waypointHeadingParam = new WaypointHeadingParam();
            if (missionType == 2 || missionType == 3) {//2代表是扫图，3代表倾斜摄影
                waypointHeadingParam.setWaypointHeadingMode("smoothTransition");
            } else {
                waypointHeadingParam.setWaypointHeadingMode("followWayline");
            }
            waypointHeadingParam.setWaypointHeadingAngle((int) waypointList.get(i).getHeading());

            placemark.setWaypointHeadingParam(waypointHeadingParam);
            WaypointTurnParam waypointTurnParam = new WaypointTurnParam();
            if (TextUtils.equals(mission.getGlobalWaypointTurnMode(), "toPointAndPassWithContinuityCurvature")) {
                waypointTurnParam.setWaypointTurnMode("toPointAndPassWithContinuityCurvature");
            } else {
                waypointTurnParam.setWaypointTurnMode("toPointAndStopWithDiscontinuityCurvature");
            }
            waypointTurnParam.setWaypointTurnDampingDist(0f);
            placemark.setWaypointTurnParam(waypointTurnParam);
            placemark.setUseStraightLine(1);


            Flightpath flightpath = flightPaths.get(i);
            //做一个保险，现在全景任务没有动作数据所以根据任务类型来推断是否是全景任务是的话单独处理
            if (missionType == 4 && flightpath.getActions().isEmpty() && i == 1) {
                ActionGroup actionGroup = new ActionGroup();
                actionGroup.setActionGroupId(i);
                actionGroup.setActionGroupStartIndex(i);
                actionGroup.setActionGroupEndIndex(i);
                actionGroup.setActionGroupMode("sequence");

                ActionTrigger actionTrigger = new ActionTrigger();
                actionTrigger.setActionTriggerType("reachPoint");
                actionGroup.setActionTrigger(actionTrigger);

                List<Action> actions = new ArrayList<>();
                Action action = new Action();
                action.setActionId(0);
                action.setActionActuatorFunc("panoShot");
                ActionActuatorFuncParam actionActuatorFuncParam = new ActionActuatorFuncParam();
                actionActuatorFuncParam.setPayloadPositionIndex(0);
                actionActuatorFuncParam.setPanoShotSubMode("panoShot_360");
                actionActuatorFuncParam.setPayloadLensIndex("wide");
                action.setActionActuatorFuncParam(actionActuatorFuncParam);
                actions.add(action);
                actionGroup.setAction(actions);
                placemark.setActionGroup(actionGroup);
            }

            int actionsSize = flightpath.getActions().size();
            if (actionsSize > 0) {
                ActionGroup actionGroup = new ActionGroup();
                actionGroup.setActionGroupId(i);
                actionGroup.setActionGroupStartIndex(i);
                actionGroup.setActionGroupEndIndex(i);
                actionGroup.setActionGroupMode("sequence");

                ActionTrigger actionTrigger = new ActionTrigger();
                actionTrigger.setActionTriggerType("reachPoint");
                actionGroup.setActionTrigger(actionTrigger);
                List<Action> actions = new ArrayList<>();
                for (int j = 0; j < actionsSize; j++) {
                    FlightpathAction flightpathAction = flightpath.getActions().get(j);

                    MISSION_ACTION type = MISSION_ACTION.getByCode(flightpathAction.getName());
                    Action action = new Action();
                    action.setActionId(j);

                    ActionActuatorFuncParam actionActuatorFuncParam = new ActionActuatorFuncParam();

                    Log.e("waypointV2ActionList", "第" + (i + 1) + "路点动作 " + type.getName() + flightpathAction.getName() + "," + flightpathAction.getParam());
                    if (type.getCode() == MISSION_ACTION.PHOTO.getCode()) {
                        action.setActionActuatorFunc("takePhoto");
                        actionActuatorFuncParam.setPayloadPositionIndex(0);
                        Log.e("TAG", "missiontype: " + missionType);
                        if (missionType == 2 || missionType == 3) {//2代表是扫图，3代表倾斜摄影
                            actionActuatorFuncParam.setPayloadLensIndex("wide");
                        } else {
                            actionActuatorFuncParam.setPayloadLensIndex(payloadLensIndex);
                        }
                    } else if (type == MISSION_ACTION.RECORD) {
                        action.setActionActuatorFunc("startRecord");
                        actionActuatorFuncParam.setPayloadPositionIndex(0);
                        actionActuatorFuncParam.setPayloadLensIndex(payloadLensIndex);
                    } else if (type == MISSION_ACTION.RECORD_STOP) {
                        action.setActionActuatorFunc("stopRecord");
                        actionActuatorFuncParam.setPayloadPositionIndex(0);
                        actionActuatorFuncParam.setPayloadLensIndex(payloadLensIndex);
                    } else if (type == MISSION_ACTION.UAV_YAW) {
                        action.setActionActuatorFunc("rotateYaw");
                        actionActuatorFuncParam.setAircraftHeading(flightpathAction.getParam());
                        actionActuatorFuncParam.setAircraftPathMode("clockwise");
                    } else if (type == MISSION_ACTION.GIMBAL) {
                        action.setActionActuatorFunc("gimbalRotate");
                        actionActuatorFuncParam.setPayloadPositionIndex(0);
                        actionActuatorFuncParam.setGimbalRotateMode("absoluteAngle");
                        actionActuatorFuncParam.setGimbalPitchRotateEnable(1);
                        actionActuatorFuncParam.setGimbalRotateTimeEnable(0);
                        actionActuatorFuncParam.setGimbalPitchRotateAngle(flightpathAction.getParam());
                    } else if (type == MISSION_ACTION.CAMERA_ZOOM) {
                        action.setActionActuatorFunc("zoom");
                        actionActuatorFuncParam.setFocalLength(flightpathAction.getParam() * 24);
                    } else if (type == MISSION_ACTION.WAIT) {
                        action.setActionActuatorFunc("hover");
                        actionActuatorFuncParam.setHoverTime(flightpathAction.getParam() / 1000);
                    } else if (type == MISSION_ACTION.PANO_SHOT) {
                        action.setActionActuatorFunc("panoShot");
                        actionActuatorFuncParam.setPayloadPositionIndex(0);
                        actionActuatorFuncParam.setPanoShotSubMode("panoShot_360");
                    }
                    actionActuatorFuncParam.setFileSuffix(flightpath.getName());
                    action.setActionActuatorFuncParam(actionActuatorFuncParam);
                    actions.add(action);

                }
                if (actions.size() > 0) {
                    actionGroup.setAction(actions);
                    placemark.setActionGroup(actionGroup);
                }
                // }
            }

            placeMarks.add(placemark);
        }
        return placeMarks;
    }

    public static String objectToXml(Object obj) {
        XStream xStream = new XStream();
        //xstream使用注解转换
        xStream.processAnnotations(obj.getClass());
        return xStream.toXML(obj);
    }

    private boolean save2File(String data) {
        String wpmlFilePath = Environment.getExternalStorageDirectory().toString() + "/wpmz/" + "waylines.wpml";
        String kmlFilePath = Environment.getExternalStorageDirectory().toString() + "/wpmz/" + "template.kml";
        try {
            File file = new File(wpmlFilePath);
            if (!file.exists()) {
                File dir = new File(file.getParent());
                dir.mkdirs();
                file.createNewFile();
            }
            File kmlFile = new File(kmlFilePath);
            if (!kmlFile.exists()) {
                File dir = new File(kmlFile.getParent());
                dir.mkdirs();
                kmlFile.createNewFile();
            }

            FileOutputStream outStream = new FileOutputStream(file);
            String top = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<kml xmlns=\"http://www.opengis.net/kml/2.2\" xmlns:wpml=\"http://www.dji.com/wpmz/1.0.0\">\n";
            outStream.write(top.getBytes());
            outStream.write(data.getBytes());
            String bottom = "\n</kml>";
            outStream.write(bottom.getBytes());
            outStream.close();

            FileOutputStream kmlOutStream = new FileOutputStream(kmlFile);
            kmlOutStream.write(top.getBytes());
            kmlOutStream.write(data.getBytes());
            kmlOutStream.write(bottom.getBytes());
            kmlOutStream.close();

            File wpmz = Environment.getExternalStoragePublicDirectory("/wpmz");
            ;
            String zipPath = Environment.getExternalStoragePublicDirectory("/outZip.kmz").getPath();
            ;
            FileUtil.toZip(wpmz.getPath(), zipPath);
            //ToastUtil.show("保存成功，保存路径："+filePath);
            return true;
        } catch (Exception e) {
            ToastUtil.show("保存失败：" + e.getLocalizedMessage());
            e.printStackTrace();
            return false;
        }
    }

    private void prepareMissionData() {
        String dirName = DiskUtil.getExternalCacheDirPath(ContextUtil.getApplicationContext(), WAYPOINT_SAMPLE_FILE_DIR);
        File dir = new File(dirName);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String cachedirName = DiskUtil.getExternalCacheDirPath(ContextUtil.getApplicationContext(), WAYPOINT_SAMPLE_FILE_CACHE_DIR);
        File cachedir = new File(cachedirName);
        if (!cachedir.exists()) {
            cachedir.mkdirs();
        }
        String destPath = dirName + WAYPOINT_SAMPLE_FILE_NAME;
        if (!new File(destPath).exists()) {
            FileUtils.copyAssetsFile(
                    ContextUtil.getApplicationContext(),
                    WAYPOINT_SAMPLE_FILE_NAME,
                    destPath);
        }
    }

    /**
     * 停止最后一个任务并上传任务。
     * 该方法首先检查当前是否有任务（mission）在执行或中断状态，如果是，则尝试停止任务。
     * 如果任务停止成功，则上传任务；如果停止失败，则传递失败结果。
     * 如果任务处于就绪状态（READY），则直接上传任务。
     * 如果任务正在上传，则传递上传失败的结果。
     * 如果任务状态未知，则传递一个相应的失败结果。
     * 注意：该方法不接受任何参数，也没有返回值。
     */
    @Override
    public void stopLastAndUploadMission() {
        if (mission != null && waypointMissionExecuteState != null) {
            // 当前任务处于执行或中断状态时，尝试停止任务
            if (waypointMissionExecuteState == WaypointMissionExecuteState.EXECUTING || waypointMissionExecuteState == WaypointMissionExecuteState.INTERRUPTED) {
                WaypointMissionManager.getInstance().stopMission(FileUtils.getFileName(curMissionPath, WAYPOINT_FILE_TAG), new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        // 停止任务成功后，上传任务
                        uploadMission(mission);
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        // 停止任务失败时，传递失败结果
                        passResult(MISSION_STOP, VALUE_FAILED, error.description());
                    }
                });
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.READY) {
                // 任务处于就绪状态时，直接上传任务
                uploadMission(mission);
            } else if (waypointMissionExecuteState == WaypointMissionExecuteState.UPLOADING) {
                // 任务正在上传时，传递上传失败的结果
                passResult(MISSION_UPLOAD, VALUE_FAILED, "mission uploading...");
            } else {
                // 任务状态未知时，传递相应的失败结果
                passResult(UNKNOWN, VALUE_FAILED, "mission state " + waypointMissionExecuteState.name());
            }
        }
    }


    @Override
    public void startMission() {
        if (curMissionPath == null) {
            passResult(MISSION_START, VALUE_FAILED, "当前任务文件路径为空");
            return;
        }
        WaypointMissionManager.getInstance().startMission(FileUtils.getFileName(curMissionPath, WAYPOINT_FILE_TAG), new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                passResult(MISSION_START, VALUE_SUCCEED, "");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                if (TextUtils.isEmpty(error.description())) {
                    passResult(MISSION_START, VALUE_FAILED, error.errorCode());
                } else {
                    passResult(MISSION_START, VALUE_FAILED, error.description());
                }
            }
        });
    }

    @Override
    public void startBPMission() {
        String missionName = FileUtils.getFileName(curMissionPath, WAYPOINT_FILE_TAG);
        WaypointMissionManager.getInstance().queryBreakPointInfoFromAircraft(missionName, new CommonCallbacks.CompletionCallbackWithParam<BreakPointInfo>() {
            @Override
            public void onSuccess(BreakPointInfo breakPointInfo) {
                if (breakPointInfo.getWaypointID() != null) {
                    resumeFromBreakPoint(missionName, breakPointInfo);
                } else {
                    ToastUtil.show("未查询到断点信息，无法执行续飞任务！");
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                ToastUtil.show("queryBreakPointInfo error " + idjiError);
            }
        });
    }

    @Override
    public void pauseMission() {
        WaypointMissionManager.getInstance().pauseMission(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
//                passResult(MISSION_PAUSE, VALUE_SUCCEED, "");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                passResult(MISSION_PAUSE, VALUE_FAILED, error.description());
            }
        });
    }

    @Override
    public void resumeMission() {
        WaypointMissionManager.getInstance().resumeMission(new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                passResult(MISSION_RESUME, VALUE_SUCCEED, "");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                passResult(MISSION_RESUME, VALUE_FAILED, error.description());
            }
        });
    }

    @Override
    public void stopMission() {
        if (curMissionPath == null) {
            Toaster.show("任务未执行");
            return;
        }
        WaypointMissionManager.getInstance().stopMission(FileUtils.getFileName(curMissionPath, WAYPOINT_FILE_TAG), new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                passResult(MISSION_STOP, VALUE_SUCCEED, "");
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                passResult(MISSION_STOP, VALUE_FAILED, error.description());
            }
        });
    }

    //断点续飞
    private void resumeFromBreakPoint(String missionName, BreakPointInfo breakPointInfo) {
        WaypointMissionManager.getInstance().startMission(missionName, breakPointInfo, new CommonCallbacks.CompletionCallback() {
            @Override
            public void onSuccess() {
                passResult(MISSION_START, VALUE_SUCCEED, "");
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                if (TextUtils.isEmpty(idjiError.description())) {
                    passResult(MISSION_START, VALUE_FAILED, idjiError.errorCode());
                } else {
                    passResult(MISSION_START, VALUE_FAILED, idjiError.description());
                }
            }
        });
    }


    private void passResult(final int state, final float value, final String err) {
        ContextUtil.getHandler().post(() -> {
            if (resultListener != null) {
                resultListener.onResult(state, value, err);
            }
        });
    }

    private void uploadMission(WaypointMission mission) {
        Log.e("uploadMission", "pushKMZFileToAircraft: ");
        lastUploadTime = 0;
        File waypointFile = new File(curMissionPath);

        if (waypointFile.exists()) {
            WaypointMissionManager.getInstance().pushKMZFileToAircraft(curMissionPath, new CommonCallbacks.CompletionCallbackWithProgress<Double>() {
                @Override
                public void onProgressUpdate(Double progress) {
                    Log.e("uploadMission", "onProgressUpdate: " + progress);
                    if (progress != null) {
                        passResult(MISSION_UPLOAD, progress.floatValue() / 100, "");

                    }
                }

                @Override
                public void onSuccess() {
                    Log.e("uploadMission", "onSuccess: ");
                    if (System.currentTimeMillis() - lastUploadTime < 7000) {
                        return;
                    }
                    if (System.currentTimeMillis() - lastFinishTime < 2000) { //加这个判断是因为5.5SDK每次任务结束后会自己触发上传
                        return;
                    }
                    lastUploadTime = System.currentTimeMillis();
                    passResult(MISSION_UPLOAD, VALUE_FINISHED, "");
                }

                @Override
                public void onFailure(@NonNull IDJIError error) {
                    Log.e("uploadMission", "uploadMission:onFailure " + error.description());
                    if (lastUploadTime != 0 && System.currentTimeMillis() - lastUploadTime > 10000) {  //加这个判断是因为5.5SDK每次任务结束后会自己触发上传
                        return;
                    }
                    passResult(MISSION_UPLOAD, VALUE_FAILED, error.description());

                }
            });
        } else {
            ToastUtil.show("Mission file not found!");
        }

    }


}
