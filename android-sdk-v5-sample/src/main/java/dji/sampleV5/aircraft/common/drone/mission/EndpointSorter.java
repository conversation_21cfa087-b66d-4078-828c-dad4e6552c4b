package dji.sampleV5.aircraft.common.drone.mission;


import static dji.sampleV5.aircraft.mvvm.util.plan.PolygonRouteGenerator.MAX_NUMBER_OF_CAMERAS;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;

public class EndpointSorter {
    private List<AppLatLng> gridPoints = new ArrayList<>();
    private List<LineLatLng> grid;
    /**
     * whether the param {@link #sampleDistance} make sense
     */
    private boolean genInterWP;
    private Double sampleDistance;
    private List<AppLatLng> cameraLocations = new ArrayList<>();
    // 航线总长度
    private int routeLength = 0;

    public EndpointSorter(List<LineLatLng> grid, Double sampleDistance, boolean genInterWP) {
        this.grid = grid;
        this.sampleDistance = sampleDistance;
        this.genInterWP = genInterWP;
    }

    public void sortGrid() throws Exception {
        AppLatLng lastPoint = grid.get(0).start;
        while (grid.size() > 0) {
            LineLatLng closestLine = getClosestLine(lastPoint);
            if (closestLine != null){
                lastPoint = processOneGridLine(closestLine, lastPoint);
            }
        }
    }

    private LineLatLng getClosestLine(AppLatLng lastPoint){
        LineLatLng minLine = null;
        double minLength = -1;
        for (LineLatLng line : grid){
            AppLatLng closestPoint = line.getClosestEndpointTo(lastPoint);
            float length = LatLngUtil.calculateLineDistance(lastPoint.getLat(), lastPoint.getLng(), closestPoint.getLat(), closestPoint.getLng());
            if (minLength == -1){
                minLength = length;
                minLine = line;
            }

            if (minLength > length){
                minLine = line;
            }
        }
        return minLine;
    }

    private AppLatLng processOneGridLine(LineLatLng closestLine, AppLatLng lastPoint)
            throws Exception {
        AppLatLng closestPoint, farthestPoint;
        closestPoint = closestLine.getClosestEndpointTo(lastPoint);
        farthestPoint = closestLine.getFarthestEndpointTo(lastPoint);

        addRouteLength(closestPoint, farthestPoint);
        if (genInterWP) {
            updateCameraLocations(closestPoint, farthestPoint);
        }

        gridPoints.add(closestPoint);
        gridPoints.add(farthestPoint);

        grid.remove(closestLine);

        if (cameraLocations.size() > MAX_NUMBER_OF_CAMERAS) {
            throw new GridWithTooCameraPosition();
        }

        return farthestPoint;
    }



    private void addRouteLength(AppLatLng firstWP, AppLatLng secondWP) {
        int lastIndex = gridPoints.size();
        if (lastIndex > 0) {
            AppLatLng start = gridPoints.get(lastIndex - 1);
            routeLength += LatLngUtil.calculateLineDistance(start.getLat(), start.getLng(), firstWP.getLat(), firstWP.getLng());
        }
        routeLength += LatLngUtil.calculateLineDistance(firstWP.getLat(), firstWP.getLng(), secondWP.getLat(), secondWP.getLng());
    }

    private void updateCameraLocations(AppLatLng firstWP, AppLatLng secondWp) {
        if (genInterWP) {
            List<AppLatLng> cameraLocationsOnThisStrip = new LineSampler(firstWP, secondWp).sample(sampleDistance);
            cameraLocations.addAll(cameraLocationsOnThisStrip);
        }
    }

    public List<AppLatLng> getSortedGrid() {
        return gridPoints;
    }

    public List<AppLatLng> getCameraLocations() {
        return cameraLocations;
    }

    public int getRouteLength() {
        return this.routeLength;
    }

    public class GridWithTooCameraPosition extends Exception {
        public GridWithTooCameraPosition() {
            super("too much camera position");
        }
    }

}
