package dji.sampleV5.aircraft.common.drone.mission;


import static dji.sampleV5.aircraft.common.drone.mission.PolygonMission.CameraOrientation.Vertical;
import static dji.sampleV5.aircraft.page.plan.BaseMission.TurnMode.Clockwise;
import static dji.sampleV5.aircraft.page.plan.BaseMission.TurnMode.CounterClockwise;
import static dji.sampleV5.aircraft.page.plan.WaypointMission.HeadingMode.Auto;
import static dji.sampleV5.aircraft.page.plan.WaypointMission.HeadingMode.UsingWaypointHeading;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.common.drone.DJITranslation;
import dji.sampleV5.aircraft.data.mission.ObliqueMission;
import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.mvvm.net.bean.CameraInfoBean;
import dji.sampleV5.aircraft.mvvm.util.plan.ObliqueRouteGenerator;
import dji.sampleV5.aircraft.mvvm.util.plan.PolygonRouteGenerator;
import dji.sampleV5.aircraft.page.plan.BaseMission;
import dji.sampleV5.aircraft.page.plan.CircleMission;
import dji.sampleV5.aircraft.util.coordinate.GeoSysConversion;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.mission.GimbalPitchMode;
import dji.sdk.keyvalue.value.mission.Waypoint;
import dji.sdk.keyvalue.value.mission.WaypointAction;
import dji.sdk.keyvalue.value.mission.WaypointActionType;
import dji.sdk.keyvalue.value.mission.WaypointMission;
import dji.sdk.keyvalue.value.mission.WaypointMissionFinishedAction;
import dji.sdk.keyvalue.value.mission.WaypointMissionFlightPathMode;
import dji.sdk.keyvalue.value.mission.WaypointMissionGotoFirstWaypointMode;
import dji.sdk.keyvalue.value.mission.WaypointMissionHeadingMode;
import dji.sdk.keyvalue.value.mission.WaypointTurnMode;

/**
 * Created by caojin on 2017/1/5.
 */

public class MissionUtil {

    /**
     * 计算照片表示的实际长度
     * <p>
     * 公式（设单张照片所拍摄的地面长度为d，相机横向放置，即传感器长度，对应d）：
     * 传感器长度 / d = 焦距 / 高度，
     * 则d = 传感器长度 * 高度 / 焦距
     * 则单位像素表示的实际距离为：d / 传感器分辨率（长）
     *
     * @param setting  相机配置
     * @param altitude 高度，返回值单位与其单位相同
     */
    public static double calGroundResolutionWidth(CameraInfoBean setting, float altitude) {
        return setting.getSizewidth() * altitude / getPhysicsFocal(setting);
    }

    public static double calGroundResolutionHeight(CameraInfoBean setting, float altitude) {
        return setting.getSizeheight() * altitude / getPhysicsFocal(setting);
    }


    /**
     * 将焦距转换为物理焦距（35mm等效焦距转换）
     * 当使用35mm等效焦距时，需要根据传感器实际尺寸进行转换
     */
    private static float getPhysicsFocal(CameraInfoBean setting) {
        if (setting.is_35mm()) {
            // 35mm模式下，需要根据传感器实际尺寸进行转换
            return (float) (setting.getFocuslength() / (43.27 / (Math.sqrt(Math.pow(setting.getSizewidth(), 2) + Math.pow(setting.getSizeheight(), 2)))));
        } else {
            // 非35mm模式直接返回原始焦距值
            return setting.getFocuslength();
        }
    }

    /**
     * 将等效焦距转换为物理焦距的通用计算方法
     * 适用于已知传感器尺寸的35mm等效焦距转换
     *
     * @param focal 等效焦距值（单位：mm）
     * @param sensorWidth 传感器宽度（单位：mm）
     * @param sensorHeight 传感器高度（单位：mm）
     * @return 实际物理焦距值（单位：mm）
     */
    public static float getPhysicsFocal(float focal, double sensorWidth, double sensorHeight) {
        // 计算公式：等效焦距 / (全画幅对角线 ÷ 实际传感器对角线)
        // 其中：
        // 1. 分母部分计算传感器实际对角线长度（勾股定理）
        // 2. 43.27mm是35mm全画幅传感器的标准对角线长度
        return (float) (focal / (43.27 /
                Math.sqrt( // 计算传感器实际对角线长度
                        Math.pow(sensorWidth, 2) +
                                Math.pow(sensorHeight, 2)
                )));
    }


    /**
     * 计算单位像素表示的实际距离，单位为：mm/px
     */
    public static double calGroundResolutionWidthScale(CameraInfoBean setting, float altitude) {
        return calGroundResolutionWidth(setting, altitude) / setting.getResolutionwidth();
    }

    public static double calGroundResolutionHeightScale(CameraInfoBean setting, float altitude) {
        return calGroundResolutionHeight(setting, altitude) / setting.getResolutionheight();
    }

    /**
     * todo：
     * #1 测试弧线转弯
     * #1 能不能有弧度代替直角，否则该模式不行
     * #2 航点悬停拍照
     * #1 99个航点上限
     * #3 等时间/等距拍照
     * #1 有些地方需要拍照，有些地方不需要拍照
     */
    public static WaypointMission surveyToDJI(PolygonMission rawMission, int offset) {
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setAutoFlightSpeed((double) rawMission.getFlySpeed());
        waypointMission.setFinishedAction(toDJI(rawMission.getFinishedAction()));
        // TODO: 2017/1/8 曲线模式
        waypointMission.setFlightPathMode(WaypointMissionFlightPathMode.NORMAL);
        waypointMission.setGotoFirstWaypointMode(WaypointMissionGotoFirstWaypointMode.SAFELY);
        waypointMission.setHeadingMode(toDJI(rawMission.getCameraOrientation()));
        waypointMission.setMaxFlightSpeed(15.0);
        waypointMission.setExitMissionOnRCSignalLostEnabled(rawMission.isExitMissionOnRCSignalLost());
        waypointMission.setGimbalPitchRotationMode(GimbalPitchMode.AUTO);
        waypointMission.setRepeatTimes(1);

        List<Waypoint> resultList = new ArrayList<>();
        if (rawMission.getGenMode() == PolygonMission.GenMode.Area) {
            resultList = genAreaWaypoint(rawMission, offset);
        } else if (rawMission.getGenMode() == PolygonMission.GenMode.Scan) {
            resultList = genScanWaypoint(rawMission, offset);
        }

        waypointMission.setWaypoints(resultList);
        waypointMission.setWaypointCount(resultList.size());
        return waypointMission;
    }

    private static List<Waypoint> genScanWaypoint(PolygonMission rawMission, int offset) {
        Grid grid = genPolygonPoints(rawMission);
        List<AppLatLng> rawList = rawMission.getTakePhotoMode() == PolygonMission.TakePhotoMode.HoverOnWaypoint ? grid.getCameraLocations() : grid.getGridPoints();
        List<Waypoint> resultList = new ArrayList<>(rawList.size());

        int waypointCount = rawList.size();
        for (int i = offset; i < waypointCount; i++) {
            AppLatLng latLng = rawList.get(i);
            float altitude = rawMission.getAltitude();

            Waypoint waypoint = new Waypoint();
            LocationCoordinate2D location = new LocationCoordinate2D(latLng.getLat(), latLng.getLng());
            waypoint.setLocation(location);
            waypoint.setAltitude((double) altitude);
            waypoint.setGimbalPitch((double) rawMission.getGimbalPitch());
            switch (rawMission.getTakePhotoMode()) {
                case HoverOnWaypoint:
                    WaypointAction takePhotoAction = new WaypointAction(WaypointActionType.START_TAKE_PHOTO, 0);
                    waypoint.getWaypointActions().add(takePhotoAction);
                    if (rawMission.getHoverAfterShoot() != 0) {
                        waypoint.getWaypointActions().add(new WaypointAction(WaypointActionType.STAY, rawMission.getHoverAfterShoot()));
                    }
                    break;
                case TimeInterval:
                    if (i % 2 == 0) {
                        double shootPhotoTimeInterval = ((1 - rawMission.getOverlap()) * calGroundResolutionHeight(rawMission.getCameraSetting(), (int) (rawMission.getAltitude() * 1000))) / 1000 / rawMission.getFlySpeed();
                        waypoint.setShootPhotoDistanceInterval(shootPhotoTimeInterval);
                    }
                    break;
                case DistanceInterval:
                    if (i % 2 == 0) {
                        double shootPhotoTimeInterval = ((1 - rawMission.getOverlap()) * calGroundResolutionHeight(rawMission.getCameraSetting(), (int) (rawMission.getAltitude() * 1000))) / 1000;
                        waypoint.setShootPhotoDistanceInterval(shootPhotoTimeInterval);
                    }
                    break;
            }
            if (rawMission.getCameraOrientation() == Vertical) {
                double heading =  DJITranslation.polar2DJI(LineTools.getLinePolarAngleVertical(rawMission.getGenAngle()));
                waypoint.setHeading((int) heading);
            }
            waypoint.setTurnMode(WaypointTurnMode.COUNTER_CLOCKWISE);
            resultList.add(waypoint);
        }

        return resultList;
    }

    private static List<Waypoint> genAreaWaypoint(PolygonMission rawMission, int offset) {
        Grid grid = genPolygonPoints(rawMission);
        List<ShootAppLatLng> rawList = grid.getShootGridPoints();
        List<Waypoint> resultList = new ArrayList<>(rawList.size());

        int waypointCount = rawList.size();
        for (int i = offset; i < waypointCount; i++) {
            ShootAppLatLng latLng = rawList.get(i);
            float altitude = rawMission.getAltitude();

            Waypoint waypoint = new Waypoint();
            LocationCoordinate2D location = new LocationCoordinate2D(latLng.getAppLatLng().getLat(), latLng.getAppLatLng().getLng());
            waypoint.setLocation(location);
            waypoint.setAltitude((double) altitude);
            waypoint.setGimbalPitch((double) rawMission.getGimbalPitch());
            // 根据拍照模式配置不同参数
            switch (rawMission.getTakePhotoMode()) {
                case HoverOnWaypoint:  // 悬停拍照模式
                    // 添加拍照动作（立即执行，参数0）
                    waypoint.getWaypointActions().add(new WaypointAction(WaypointActionType.START_TAKE_PHOTO, 0));
                    // 配置悬停时长（单位：秒）
                    if (rawMission.getHoverAfterShoot() != 0) {
                        waypoint.getWaypointActions().add(new WaypointAction(WaypointActionType.STAY, rawMission.getHoverAfterShoot()));
                    }
                    break;

                case TimeInterval:     // 时间间隔拍照
                    if (latLng.isShoot()) {  // 只在需要拍照的位置设置
                        // 计算拍照时间间隔 = (1-重叠率)*地面分辨率高度 / 飞行速度
                        double interval = ((1 - rawMission.getOverlap())
                                * calGroundResolutionHeight(rawMission.getCameraSetting(), (int)(rawMission.getAltitude()*1000)) // 毫米转米
                        ) / 1000 / rawMission.getFlySpeed();
                        waypoint.setShootPhotoTimeInterval(interval);
                    }
                    break;

                case DistanceInterval: // 距离间隔拍照
                    if (latLng.isShoot()) {
                        // 计算拍照距离间隔 = (1-重叠率)*地面分辨率高度
                        double distance = ((1 - rawMission.getOverlap())
                                * calGroundResolutionHeight(rawMission.getCameraSetting(), (int)(rawMission.getAltitude()*1000))
                        ) / 1000;  // 毫米转米
                        waypoint.setShootPhotoDistanceInterval(distance);
                    }
                    break;
            }

            // 垂直拍摄模式时设置航向角
            if (rawMission.getCameraOrientation() == Vertical) {
                // 转换生成角度为DJI坐标系角度（0-360度）
                waypoint.setHeading((int) DJITranslation.polar2DJI(
                        LineTools.getLinePolarAngleVertical(rawMission.getGenAngle())));
            }
            // 统一设置转向模式为逆时针
            waypoint.setTurnMode(WaypointTurnMode.COUNTER_CLOCKWISE);
            resultList.add(waypoint);
        }

        return resultList;
    }


    public static WaypointMission obliqueToDJI(ObliqueMission rawMission, int level, int index) {
        WaypointMission waypointMission = new WaypointMission();
        if (level == 1) {
            waypointMission.setAutoFlightSpeed((double) rawMission.getFlySpeed());
        } else {
            waypointMission.setAutoFlightSpeed((double) rawMission.getObliqueSpeed());
        }

        waypointMission.setFinishedAction(toDJI(rawMission.getFinishedAction()));

        waypointMission.setFlightPathMode(WaypointMissionFlightPathMode.NORMAL);
        waypointMission.setGotoFirstWaypointMode(WaypointMissionGotoFirstWaypointMode.SAFELY.SAFELY);
        waypointMission.setHeadingMode(WaypointMissionHeadingMode.USING_INITIAL_DIRECTION);
        waypointMission.setMaxFlightSpeed(15.0);
        waypointMission.setExitMissionOnRCSignalLostEnabled(rawMission.isExitMissionOnRCSignalLost());
        waypointMission.setGimbalPitchRotationMode(GimbalPitchMode.AUTO);
        waypointMission.setRepeatTimes(1);

        Grid grid;
        try {
            grid = genObliquePoints(rawMission, level);
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }

        double routeAngle = rawMission.getGenAngle();
        switch (level) {
            case 1:
            case 5:
                routeAngle = rawMission.getGenAngle();
                break;
            case 2:
                routeAngle = rawMission.getGenAngle() + 90;
                break;
            case 3:
                routeAngle = rawMission.getGenAngle() + 180;
                break;
            case 4:
                routeAngle = rawMission.getGenAngle() + 270;
                break;
        }

        List<AppLatLng> rawList = grid.getGridPoints();
        List<Waypoint> resultList = new ArrayList<>(rawList.size());

        int waypointCount = rawList.size();
        for (int i = index; i < waypointCount; i++) {
            AppLatLng latLng = rawList.get(i);
            float altitude = rawMission.getAltitude();

            Waypoint waypoint = new Waypoint();
            LocationCoordinate2D location = new LocationCoordinate2D(latLng.getLat(), latLng.getLng());
            waypoint.setLocation(location);
            waypoint.setAltitude((double) altitude);
            if (i == index) {
                double targetAngle = DJITranslation.polar2DJI(routeAngle % 360);
                waypoint.setTurnMode(WaypointTurnMode.COUNTER_CLOCKWISE);
                waypoint.getWaypointActions().add(new WaypointAction(WaypointActionType.ROTATE_AIRCRAFT, (int) targetAngle));
            }

            if (level == 1) {
                waypoint.setGimbalPitch((double) -90);
            } else {
                waypoint.setGimbalPitch((double) rawMission.getGimbalPitch());
            }

            if (i % 2 == 0) {
                double distance = ((1 - rawMission.getOverlap()) * calGroundResolutionHeight(rawMission.getCameraSetting(), (int) (rawMission.getAltitude() * 1000))) / 1000;
                waypoint.setShootPhotoDistanceInterval(distance);
            }

            resultList.add(waypoint);
        }
        waypointMission.setWaypoints(resultList);
        waypointMission.setWaypointCount(resultList.size());

        return waypointMission;
    }

    public static WaypointMission diyToDJI(WaypointMission rawMission, int index) {
        WaypointMission waypointMission = new WaypointMission();
        waypointMission.setAutoFlightSpeed(rawMission.getAutoFlightSpeed());
        waypointMission.setFinishedAction(rawMission.getFinishedAction());
        waypointMission.setFlightPathMode(WaypointMissionFlightPathMode.NORMAL);
        waypointMission.setGotoFirstWaypointMode(WaypointMissionGotoFirstWaypointMode.SAFELY);
        waypointMission.setHeadingMode(rawMission.getHeadingMode());
        waypointMission.setMaxFlightSpeed(15.0);
        waypointMission.setExitMissionOnRCSignalLostEnabled(rawMission.getExitMissionOnRCSignalLostEnabled());
        waypointMission.setGimbalPitchRotationMode(GimbalPitchMode.AUTO);
        waypointMission.setRepeatTimes(1);

        List<Waypoint> rawList = rawMission.getWaypoints();
        if (rawList.size() > 99) {
            Log.e("MissionUtil", "waypoint count can not over 99");
        }

        List<Waypoint> resultList = new ArrayList<>(rawList.size());
        for (int i = index; i < rawList.size(); i++) {
            Waypoint item = rawList.get(i);
            Waypoint waypoint = new Waypoint();
            waypoint.setLocation(item.getLocation());
            waypoint.setAltitude(item.getAltitude());
            waypoint.setGimbalPitch(item.getGimbalPitch());
            waypoint.setHeading(item.getHeading());
            waypoint.setTurnMode(item.getTurnMode());
            List<WaypointAction> rawActionList = item.getWaypointActions();
            if (rawActionList != null && rawActionList.size() > 0) {
                for (WaypointAction action : rawActionList) {
                    waypoint.getWaypointActions().add(new WaypointAction(action.getActionType(), action.getActionParam()));
                }
            }
            resultList.add(waypoint);
        }
        waypointMission.setWaypoints(resultList);
        waypointMission.setWaypointCount(resultList.size());
        return waypointMission;
    }

    public static Grid genPolygonPoints(PolygonMission mission) {
        List<AppLatLng> points = mission.getPointList();
        Polygon polygon = new Polygon();
        polygon.addPoints(points);

        PolygonRouteGenerator generator = new PolygonRouteGenerator();
        generator.setGenMode(mission.getGenMode());
        generator.setPolygon(polygon);
        CameraInfoBean setting = mission.getCameraSetting();
        generator.setAngle(mission.getGenAngle());
        generator.setRouteMode(mission.getRouteMode());
        generator.setLineDis(MissionUtil.calGroundResolutionWidth(setting, mission.getAltitude()) * (1 - mission.getGenSideLap()));
        generator.setWpDis(MissionUtil.calGroundResolutionHeight(setting, mission.getAltitude()) * (1 - mission.getOverlap()));
        generator.setMargin(mission.getGenMargin());
        generator.setMissionType(mission.getMissionType());
        boolean isHover = mission.getTakePhotoMode() == PolygonMission.TakePhotoMode.HoverOnWaypoint;
        generator.setGenInterWP(isHover);
        Grid grid = null;
        try {
            grid = generator.generate();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return grid;
    }

    public static Grid genObliquePoints(ObliqueMission mission, int level) throws Exception {
        List<AppLatLng> points = mission.getPointList();
        Polygon polygon = new Polygon();
        polygon.addPoints(points);

        ObliqueRouteGenerator generator = new ObliqueRouteGenerator();
        generator.setPolygon(polygon);
        generator.setGenMode(mission.getGenMode());
        CameraInfoBean setting = mission.getCameraSetting();
        generator.setAngle(mission.getGenAngle());
        generator.setGimbalAngle(mission.getGimbalPitch());

        if (level == 1) {
            //第一层固定使用航向、旁向重叠率
            generator.setLineDis(MissionUtil.calGroundResolutionWidth(setting, mission.getAltitude()) * (1 - mission.getGenSideLap()));
            generator.setWpDis(MissionUtil.calGroundResolutionHeight(setting, mission.getAltitude()) * (1 - mission.getOverlap()));
        } else {
            //其他层固定使用倾斜航向、旁向重叠率
            generator.setLineDis(MissionUtil.calGroundResolutionWidth(setting, mission.getAltitude()) * (1 - mission.getObliqueSideLap()));
            generator.setWpDis(MissionUtil.calGroundResolutionHeight(setting, mission.getAltitude()) * (1 - mission.getObliqueOverlap()));
        }
        generator.setMargin(mission.getGenMargin());
        generator.setGenInterWP(mission.getTakePhotoMode() == ObliqueMission.TakePhotoMode.HoverOnWaypoint);
        generator.setMissionType(mission.getMissionType());

        Grid grid = null;
        try {
            grid = generator.generateOblique(level, mission.getAltitude());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return grid;
    }

    /**
     * 将自定义动作类型转换为DJI SDK支持的动作类型枚举
     * 用于航点动作指令的协议适配
     */
    private static WaypointActionType toDJI(dji.sampleV5.aircraft.page.plan.WaypointMission.Waypoint.ActionType type) {
        switch (type) {
            case GimbalPitch:       // 云台俯仰角控制
                return WaypointActionType.GIMBAL_PITCH;
            case RotateAircraft:    // 飞行器旋转
                return WaypointActionType.ROTATE_AIRCRAFT;
            case StartRecord:       // 开始录像
                return WaypointActionType.START_RECORD;
            case StartTakePhoto:    // 执行拍照
                return WaypointActionType.START_TAKE_PHOTO;
            case Stay:              // 悬停等待
                return WaypointActionType.STAY;
            case StopRecord:        // 停止录像
                return WaypointActionType.STOP_RECORD;
        }
        return WaypointActionType.STAY;  // 默认返回悬停动作
    }

    /**
     * 转向模式枚举转换
     * 适配自定义转向模式到DJI SDK协议
     */
    private static WaypointTurnMode toDJI(BaseMission.TurnMode mode) {
        switch (mode) {
            case Clockwise:         // 顺时针转向
                return WaypointTurnMode.CLOCKWISE;
            case CounterClockwise:  // 逆时针转向
                return WaypointTurnMode.COUNTER_CLOCKWISE;
        }
        return WaypointTurnMode.CLOCKWISE;  // 默认返回顺时针
    }

    /**
     * 航向模式枚举转换
     * 实现业务层航向模式到SDK协议的映射
     */
    private static WaypointMissionHeadingMode toDJI(dji.sampleV5.aircraft.page.plan.WaypointMission.HeadingMode mode) {
        switch (mode) {
            case Auto:                 // 自动航向模式
                return WaypointMissionHeadingMode.AUTO;
            case UsingWaypointHeading: // 使用航点指定航向
                return WaypointMissionHeadingMode.USING_WAYPOINT_HEADING;
        }
        return WaypointMissionHeadingMode.AUTO;  // 默认自动模式
    }

    private static WaypointMissionFinishedAction toDJI(PolygonMission.FinishedAction action) {
        switch (action) {
            case AutoLand:
                return WaypointMissionFinishedAction.AUTO_LAND;
            case GoHome:
                return WaypointMissionFinishedAction.GO_HOME;
            case NoAction:
                return WaypointMissionFinishedAction.NO_ACTION;
        }
        return WaypointMissionFinishedAction.NO_ACTION;
    }

    private static WaypointMissionHeadingMode toDJI(PolygonMission.CameraOrientation mode) {
        switch (mode) {
            case Parallel:
                return WaypointMissionHeadingMode.FOLLOW_GIMBAL_HEADING;
            case Vertical:
                return WaypointMissionHeadingMode.USING_WAYPOINT_HEADING;
        }
        return WaypointMissionHeadingMode.CONTROL_BY_REMOTE_CONTROLLER;
    }
}
