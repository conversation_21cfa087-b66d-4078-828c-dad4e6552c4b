package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class PointTools {

	public static AppLatLng findFarthestPoint(ArrayList<AppLatLng> crosses, AppLatLng middle) {
		double farthestDistance = Double.NEGATIVE_INFINITY;
		AppLatLng farthestPoint = null;
		for (AppLatLng cross : crosses) {
			double distance = GeoTools.getApproximatedDistance(cross, middle);
			if (distance > farthestDistance) {
				farthestPoint = cross;
				farthestDistance = distance;
			}
		}
		return farthestPoint;
	}

	/**
	 * Finds the closest point in a list to another point
	 * 
	 * @param point
	 *            point that will be used as reference
	 * @param list
	 *            List of points to be searched
	 * @return The closest point
	 */
	@SuppressWarnings("unused")
	private static AppLatLng findClosestPoint(AppLatLng point, List<AppLatLng> list) {
		AppLatLng answer = null;
		double currentbest = Double.MAX_VALUE;

		for (AppLatLng pnt : list) {
			double dist1 = GeoTools.getApproximatedDistance(point, pnt);

			if (dist1 < currentbest) {
				answer = pnt;
				currentbest = dist1;
			}
		}
		return answer;
	}

	/**
	 * Finds the pair of adjacent points that minimize the distance to a
	 * reference point
	 * 
	 * @param point
	 *            point that will be used as reference
	 * @param waypoints2
	 *            List of points to be searched
	 * @return Position of the second point in the pair that minimizes the
	 *         distance
	 */
	static int findClosestPair(AppLatLng point, List<AppLatLng> waypoints2) {
		int answer = 0;
		double currentbest = Double.MAX_VALUE;
		double dist;
		AppLatLng p1, p2;

		for (int i = 0; i < waypoints2.size(); i++) {
			if (i == waypoints2.size() - 1) {
				p1 = waypoints2.get(i);
				p2 = waypoints2.get(0);
			} else {
				p1 = waypoints2.get(i);
				p2 = waypoints2.get(i + 1);
			}

			dist = PointTools.pointToLineDistance(p1, p2, point);
			if (dist < currentbest) {
				answer = i + 1;
				currentbest = dist;
			}
		}
		return answer;
	}

	/**
	 * Provides the distance from a point P to the line segment that passes
	 * through A-B. If the point is not on the side of the line, returns the
	 * distance to the closest point
	 * 
	 * @param L1
	 *            First point of the line
	 * @param L2
	 *            Second point of the line
	 * @param P
	 *            Point to measure the distance
	 */
	public static double pointToLineDistance(AppLatLng L1, AppLatLng L2, AppLatLng P) {
		double A = P.getLat() - L1.getLat();
		double B = P.getLng() - L1.getLng();
		double C = L2.getLat() - L1.getLat();
		double D = L2.getLng() - L1.getLng();

		double dot = A * C + B * D;
		double len_sq = C * C + D * D;
		double param = dot / len_sq;

		double xx, yy;

		if (param < 0) // point behind the segment
		{
			xx = L1.getLat();
			yy = L1.getLng();
		} else if (param > 1) // point after the segment
		{
			xx = L2.getLat();
			yy = L2.getLng();
		} else { // point on the side of the segment
			xx = L1.getLat() + param * C;
			yy = L1.getLng() + param * D;
		}

		return Math.hypot(xx - P.getLat(), yy - P.getLng());
	}

}
