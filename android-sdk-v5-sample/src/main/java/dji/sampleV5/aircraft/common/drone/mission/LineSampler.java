package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class LineSampler {

	private List<AppLatLng> points;
	private List<AppLatLng> sampledPoints = new ArrayList<>();

	public LineSampler(AppLatLng p1, AppLatLng p2) {
		points = new ArrayList<>();
		points.add(p1);
		points.add(p2);
	}

	public List<AppLatLng> sample(double sampleDistance) {
		// add the first point
		//sampledPoints.add(points.get(0));

		AppLatLng from = points.get(0);
		AppLatLng to = points.get(1);
		sampledPoints.addAll(sampleLine(from, to, sampleDistance));

		// add the last point
		//sampledPoints.add(points.get(1));

		return sampledPoints;
	}

	private List<AppLatLng> sampleLine(AppLatLng from, AppLatLng to, double samplingDistance) {
		List<AppLatLng> result = new ArrayList<>();
		double heading = GeoTools.getHeadingFromCoordinates(from, to);
		double totalLength = GeoTools.getDistance(from, to);
		double distance = 0;

		while (distance < totalLength) {
			result.add(GeoTools.newCoordFromBearingAndDistance(from, heading, distance));
			distance += samplingDistance;
		}
		return result;
	}
}
