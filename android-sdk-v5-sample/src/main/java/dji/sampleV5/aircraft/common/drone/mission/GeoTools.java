package dji.sampleV5.aircraft.common.drone.mission;


import static java.lang.Math.PI;
import static java.lang.Math.abs;
import static java.lang.Math.atan2;
import static java.lang.Math.cos;
import static java.lang.Math.sin;
import static java.lang.Math.tan;
import static java.lang.Math.toRadians;

import com.vividsolutions.jts.geom.Coordinate;
import com.vividsolutions.jts.geom.GeometryFactory;
import com.vividsolutions.jts.geom.LineString;
import com.vividsolutions.jts.geom.LinearRing;

import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class GeoTools {
    private static final double RADIUS_OF_EARTH = 6378137.0;// In meters.

    public GeoTools() {
    }

    /**
     * Returns the distance between two points
     *
     * @return distance between the points in degrees
     */
    public static double getApproximatedDistance(AppLatLng p1, AppLatLng p2) {
        return (Math.hypot((p1.getLat() - p2.getLat()), (p1.getLng() - p2.getLng())));
    }

    /**
     * Extrapolate latitude/longitude given a heading and distance thanks to
     * http://www.movable-type.co.uk/scripts/DWLatLng.html
     *
     * @param origin   Point of origin
     * @param bearing  bearing to navigate
     * @param distance distance to be added
     * @return New point with the added distance
     */
    public static AppLatLng newCoordFromBearingAndDistance(AppLatLng origin, double bearing, double distance) {
        return newCoordFromBearingAndDistance(origin.getLat(), origin.getLng(), bearing, distance);
    }

    /**
     * Extrapolate latitude/longitude given a heading and distance thanks to
     * http://www.movable-type.co.uk/scripts/DWLatLng.html
     *
     * @param lat      latitude
     * @param lon      longitude
     * @param bearing  bearing to navigate
     * @param distance distance to be added
     * @return New point with the added distance
     */
    private static AppLatLng newCoordFromBearingAndDistance(double lat, double lon, double bearing, double distance) {

        double lat1 = Math.toRadians(lat);
        double lon1 = Math.toRadians(lon);
        double brng = Math.toRadians(bearing);
        double dr = distance / RADIUS_OF_EARTH;

        double lat2 = Math.asin(Math.sin(lat1) * Math.cos(dr) + Math.cos(lat1) * Math.sin(dr)
                * Math.cos(brng));
        double lon2 = lon1
                + Math.atan2(Math.sin(brng) * Math.sin(dr) * Math.cos(lat1),
                Math.cos(dr) - Math.sin(lat1) * Math.sin(lat2));

        return (new AppLatLng(Math.toDegrees(lat2), Math.toDegrees(lon2)));
    }

    /**
     * Copied from android-map-utils (licensed under Apache v2)
     * com.google.maps.android.SphericalUtil.java
     *
     * @return area in m�
     */
    public static Area getArea(Polygon poly) {
        List<AppLatLng> path = poly.getPoints();
        int size = path.size();
        if (size < 3) {
            return new Area(0);
        }
        double total = 0;
        AppLatLng prev = path.get(size - 1);
        double prevTanLat = tan((PI / 2 - toRadians(prev.getLat())) / 2);
        double prevLng = toRadians(prev.getLng());
        // For each edge, accumulate the signed area of the triangle formed by
        // the North Pole
        // and that edge ("polar triangle").
        for (AppLatLng point : path) {
            double tanLat = tan((PI / 2 - toRadians(point.getLat())) / 2);
            double lng = toRadians(point.getLng());
            total += polarTriangleArea(tanLat, lng, prevTanLat, prevLng);
            prevTanLat = tanLat;
            prevLng = lng;
        }
        return new Area(abs(total * (RADIUS_OF_EARTH * RADIUS_OF_EARTH)));
    }

    /**
     * Copied from android-map-utils (licensed under Apache v2)
     * com.google.maps.android.SphericalUtil.java
     * <p/>
     * Returns the signed area of a triangle which has North Pole as a vertex.
     * Formula derived from
     * "Area of a spherical triangle given two edges and the included angle" as
     * per "Spherical Trigonometry" by Todhunter, page 71, section 103, point 2.
     * See http://books.google.com/books?id=3uBHAAAAIAAJ&pg=PA71 The arguments
     * named "tan" are tan((pi/2 - latitude)/2).
     */
    private static double polarTriangleArea(double tan1, double lng1, double tan2, double lng2) {
        double deltaLng = lng1 - lng2;
        double t = tan1 * tan2;
        return 2 * atan2(t * sin(deltaLng), 1 + t * cos(deltaLng));
    }

    /**
     * Computes the heading between two coordinates
     *
     * @return heading in degrees
     */
    public static double getHeadingFromCoordinates(AppLatLng fromLoc, AppLatLng toLoc) {
        double fLat = Math.toRadians(fromLoc.getLat());
        double fLng = Math.toRadians(fromLoc.getLng());
        double tLat = Math.toRadians(toLoc.getLat());
        double tLng = Math.toRadians(toLoc.getLng());

        double degree = Math.toDegrees(Math.atan2(
                Math.sin(tLng - fLng) * Math.cos(tLat),
                Math.cos(fLat) * Math.sin(tLat) - Math.sin(fLat) * Math.cos(tLat)
                        * Math.cos(tLng - fLng)));

        return warpToPositiveAngle(degree);
    }

    /**
     * Computes the distance between two coordinates
     *
     * @return distance in meters
     */
    public static double getDistance(AppLatLng from, AppLatLng to) {
        return RADIUS_OF_EARTH * Math.toRadians(getArcInRadians(from, to));
    }

    public static double getDistance(List<AppLatLng> list){
        double distance = -1;
        for (int i = 0; i < list.size()-1; i++){
            distance += getDistance(list.get(i), list.get(i+1));
        }
        return distance;
    }

    public static double warpToPositiveAngle(double degree) {
        if (degree >= 0) {
            return degree;
        } else {
            return 360 + degree;
        }
    }

    /**
     * Calculates the arc between two points
     * http://en.wikipedia.org/wiki/Haversine_formula
     *
     * @return the arc in degrees
     */
    static double getArcInRadians(AppLatLng from, AppLatLng to) {

        double latitudeArc = Math.toRadians(from.getLat() - to.getLat());
        double longitudeArc = Math.toRadians(from.getLng() - to.getLng());

        double latitudeH = Math.sin(latitudeArc * 0.5);
        latitudeH *= latitudeH;
        double lontitudeH = Math.sin(longitudeArc * 0.5);
        lontitudeH *= lontitudeH;

        double tmp = Math.cos(Math.toRadians(from.getLat()))
                * Math.cos(Math.toRadians(to.getLat()));
        return Math.toDegrees(2.0 * Math.asin(Math.sqrt(latitudeH + tmp * lontitudeH)));
    }

    /**
     * 判断点是否在多边形内,使用JTS工具库判断
     * ps:点在边上也返回false
     *
     * @param pt   检测点
     * @param list 多边形的顶点
     * @return 点在多边形内返回true, 否则返回false
     */
    public static boolean isPointInPolygon(AppLatLng pt, List<AppLatLng> list) {
        if (list != null && list.size() > 0) {
            com.vividsolutions.jts.geom.Point point = new GeometryFactory().createPoint(new Coordinate(pt.getLng(), pt.getLat()));
            Coordinate[] coordinates = new Coordinate[list.size() + 1];
            for (int i = 0; i < list.size(); i++) {
                coordinates[i] = new Coordinate(list.get(i).getLng(), list.get(i).getLat());
            }

            coordinates[list.size()] = coordinates[0];//构建一个首尾相连的点集
            LinearRing linearRing = new GeometryFactory().createLinearRing(coordinates);
            com.vividsolutions.jts.geom.Polygon polygon = new GeometryFactory().createPolygon(linearRing, null);
            return polygon.contains(point);
        } else {
            return false;
        }
    }

    /**
     * 判断点是否在多边形内,使用JTS工具库判断
     * ps:点在边上也返回false
     *
     * @param start   检测线起点
     * @param end    检测线终点
     * @param list 多边形的顶点
     * @return 点在多边形内返回true, 否则返回false
     */
    public static boolean isLineInPolygon(AppLatLng start, AppLatLng end,  List<AppLatLng> list) {
        if (list != null && list.size() > 0) {
            Coordinate[] coordinateLine = new Coordinate[2];
            coordinateLine[0] = new Coordinate(start.getLng(), start.getLat());
            coordinateLine[1] = new Coordinate(end.getLng(), end.getLat());

            LineString linear = new GeometryFactory().createLineString(coordinateLine);

            Coordinate[] coordinates = new Coordinate[list.size() + 1];
            for (int i = 0; i < list.size(); i++) {
                coordinates[i] = new Coordinate(list.get(i).getLng(), list.get(i).getLat());
            }

            coordinates[list.size()] = coordinates[0];//构建一个首尾相连的点集
            LinearRing linearRing = new GeometryFactory().createLinearRing(coordinates);
            com.vividsolutions.jts.geom.Polygon polygon = new GeometryFactory().createPolygon(linearRing, null);
            return polygon.contains(linear);
        } else {
            return false;
        }
    }
}
