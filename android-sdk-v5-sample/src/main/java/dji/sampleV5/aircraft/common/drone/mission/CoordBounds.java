package dji.sampleV5.aircraft.common.drone.mission;


import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;
import dji.sampleV5.aircraft.util.coordinate.LatLngUtil;

/**
 * Calculate a rectangle that bounds all inserted points
 */
public class CoordBounds {
	private List<AppLatLng> mPoints;

	public CoordBounds(List<AppLatLng> points) {
		this.mPoints = points;
	}

	/**
	 * 计算中心到各个顶点的最大距离，单位：m
	 */
	public double getDiag(AppLatLng centerPoint) {
		double distance = LatLngUtil.calculateLineDistance(mPoints.get(0).getLat(), mPoints.get(0).getLng(), centerPoint.getLat(), centerPoint.getLng());
		for (int i = 1;i < mPoints.size();i++) {
			double temp = LatLngUtil.calculateLineDistance(mPoints.get(i).getLat(), mPoints.get(i).getLng(), centerPoint.getLat(), centerPoint.getLng());
			if (temp > distance) {
				distance = temp;
			}
		}
		return distance;
	}

	/**
	 * 计算几何中心
	 */
	public AppLatLng getMiddle() {
		//多边形面积
		double area = 0.0;
		// 重心的x,y
		double Gx = 0.0, Gy = 0.0;
		int size = mPoints.size();
		for (int i = 1; i <= size; i++) {
			double iLat = mPoints.get(i % size).getLat();
			double iLng = mPoints.get(i % size).getLng();
			double nextLat = mPoints.get(i - 1).getLat();
			double nextLng = mPoints.get(i - 1).getLng();
			double temp = (iLat * nextLng - iLng * nextLat) / 2.0;
			area += temp;
			Gx += temp * (iLat + nextLat) / 3.0;
			Gy += temp * (iLng + nextLng) / 3.0;
		}

		if(area == 0){
			return null;
		}else {
			Gx = Gx / area;
			Gy = Gy / area;
			return new AppLatLng(Gx, Gy);
		}
	}
}
