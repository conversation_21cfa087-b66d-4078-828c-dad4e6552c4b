package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class FindRoadHelper {


    /**
     * 给定顺序点和边界，找出边界外的路线并处理
     * <p>
     * PS：判断一条路线是否在限制区内，转化成判断路线中点是否在限制区内
     *
     * @param roadPoints  需要到达的点集合
     * @param boundPoints 限制区域边界点集合
     */
    public static List<ShootAppLatLng> findValidRound(List<AppLatLng> roadPoints, List<AppLatLng> boundPoints, List<LineLatLng> boundLines) {
        List<ShootAppLatLng> result = new ArrayList<>();
        for (int i = 0; i < roadPoints.size() - 1; i++) {
            AppLatLng point1 = roadPoints.get(i);
            AppLatLng point2 = roadPoints.get(i + 1);

            if (i % 2 == 0){
                result.add(new ShootAppLatLng(roadPoints.get(i), true));
            }else {
                result.add(new ShootAppLatLng(roadPoints.get(i), false));
            }

            List<ShootAppLatLng> shootList = new ArrayList<>();
            ArrayList<AppLatLng> crosses = LineTools.findCrossings(boundLines, new LineLatLng(point1, point2));
            if (crosses.size() > 2) {
                //航线与限制区交点>2时，说明必定有部分航线在限制区域外
                for (AppLatLng appLatLng : foundMinRoadOnBound(point1, point2, boundPoints)){
                    shootList.add(new ShootAppLatLng(appLatLng, false));
                }
                result.addAll(shootList);
            } else {
                //航线与限制区交点=2时，有两种情况。
                // 1、完全在限制区内
                // 2、完全在限制区外
                if (crosses.size() == 2 && isLineOutPolygon(crosses.get(0), crosses.get(1), boundPoints)) {
                    for (AppLatLng appLatLng : foundMinRoadOnBound(point1, point2, boundPoints)){
                        shootList.add(new ShootAppLatLng(appLatLng, false));
                    }
                    result.addAll(shootList);
                }
            }
        }

        result.add(new ShootAppLatLng(roadPoints.get(roadPoints.size() - 1), false));

        return result;
    }

    //处理一下，去除与边界线相交的两个端点
    private static boolean isLineOutPolygon(AppLatLng start, AppLatLng end, List<AppLatLng> boundPoints) {
        AppLatLng startTemp = new AppLatLng((start.getLat() * 0.1 + end.getLat() * 0.9), (start.getLng() * 0.1 + end.getLng() * 0.9));
        AppLatLng endTemp = new AppLatLng((start.getLat() * 0.9 + end.getLat() * 0.1), (start.getLng() * 0.9 + end.getLng() * 0.1));
        return !GeoTools.isLineInPolygon(startTemp, endTemp, boundPoints);
    }

    /**
     * 从限制区上找到最短路径
     *
     * @param startPoint  起点
     * @param targetPoint 目标点
     * @param bounds      限制区域点集
     * @return 沿着限制区域点走动的合理点集
     * <p>
     * 代码处理逻辑
     * 1、找出start、target在边界上点的下标，确定点在限制区上的位置
     * 2、分别计算从start顺时针、逆时针走到target距离
     * 3、比较两个距离大小，选距离小返回边界点路线集合
     * <p>
     * 该算法不是最优路径算法
     *
     * <p>
     * PS:限制区点集顺序是按照左上角逆时针排序
     */
    private static List<AppLatLng> foundMinRoadOnBound(AppLatLng startPoint, AppLatLng targetPoint, List<AppLatLng> bounds) {
        List<AppLatLng> list = new ArrayList<>();

        AppLatLng startTemp, endTemp;

        int startLeftIndex = -1, targetLeftIndex = -1;

        for (int i = 0; i < bounds.size(); i++) {
            startTemp = bounds.get(i);
            endTemp = (i == bounds.size() - 1) ? bounds.get(0) : bounds.get(i + 1);

            if (isPointOnLine(startPoint, new LineLatLng(startTemp, endTemp))) {
                startLeftIndex = i;
            }

            if (isPointOnLine(targetPoint, new LineLatLng(startTemp, endTemp))) {
                targetLeftIndex = i;
            }

            if (startLeftIndex != -1 &&
                    targetLeftIndex != -1) {
                break;
            }
        }

        if (startLeftIndex == -1 ||
                targetLeftIndex == -1) {
            return list;
        } else {
            double clockwiseDistance = calculateClockwiseLength(startLeftIndex, targetLeftIndex, bounds);
            double antiClockwiseDistance = calculateAntiClockwiseLength(startLeftIndex, targetLeftIndex, bounds);
            if (clockwiseDistance == -1 || antiClockwiseDistance == -1) {
                return list;
            } else {
                if (clockwiseDistance < antiClockwiseDistance) {
                    while (startLeftIndex != targetLeftIndex) {
                        list.add(bounds.get(startLeftIndex));
                        if (startLeftIndex == 0) {
                            startLeftIndex = bounds.size() - 1;
                        } else {
                            startLeftIndex--;
                        }
                    }
                } else {
                    if (startLeftIndex == bounds.size() - 1) {
                        startLeftIndex = 0;
                    } else {
                        startLeftIndex += 1;
                    }//逆时针从右边走，取点右边的坐标

                    while (startLeftIndex != targetLeftIndex) {
                        list.add(bounds.get(startLeftIndex));
                        if (startLeftIndex == bounds.size() - 1) {
                            startLeftIndex = 0;
                        } else {
                            startLeftIndex++;
                        }
                    }
                    list.add(bounds.get(targetLeftIndex));
                }
            }
        }


        return list;
    }

    /**
     * 判断是否在线段内
     *
     * @param point 目标点
     * @param line  线段
     * @return boolean
     */
    private static boolean isPointOnLine(AppLatLng point, LineLatLng line) {
        double minLat = Math.min(line.start.getLat(), line.end.getLat());
        double maxLat = Math.max(line.start.getLat(), line.end.getLat());
        double minLng = Math.min(line.start.getLng(), line.end.getLng());
        double maxLng = Math.max(line.start.getLng(), line.end.getLng());

        if (point.getLat() >= minLat &&
                point.getLat() <= maxLat &&
                point.getLng() >= minLng &&
                point.getLng() <= maxLng) {
            double value = (point.getLat() - line.start.getLat()) * (line.start.getLng() - line.end.getLng()) - (line.start.getLat() - line.end.getLat())
                    * (point.getLng() - line.start.getLng());
            value = Math.abs(value);
            return value < 0.000000000001;//精确度问题,测试发现在线上时精确度E-18
        } else {
            return false;
        }
    }

    private static double calculateClockwiseLength(int startLeft, int targetLeft, List<AppLatLng> bounds) {
        int maxIndex = bounds.size() - 1;
        List<AppLatLng> list = new ArrayList<>();
        if (startLeft != -1 && targetLeft != -1) {
            if (startLeft == targetLeft) {
                return 1;
            }

            while (startLeft != targetLeft) {
                if (startLeft == 0) {
                    startLeft = maxIndex;
                } else {
                    startLeft--;
                }

                list.add(bounds.get(startLeft));
            }

            if (list.size() == 1) {
                return 1;
            } else {
                return GeoTools.getDistance(list);
            }
        } else {
            return -1;
        }
    }

    private static double calculateAntiClockwiseLength(int startLeft, int targetLeft, List<AppLatLng> bounds) {
        int maxIndex = bounds.size() - 1;
        List<AppLatLng> list = new ArrayList<>();
        if (startLeft != -1 && targetLeft != -1) {
            if (startLeft == targetLeft) {
                return 1;
            }

            while (startLeft != targetLeft) {
                if (startLeft == maxIndex) {
                    startLeft = 0;
                } else {
                    startLeft++;
                }

                list.add(bounds.get(startLeft));
            }

            if (list.size() == 1) {
                return 1;
            } else {
                return GeoTools.getDistance(list);
            }
        } else {
            return -1;
        }
    }
}
