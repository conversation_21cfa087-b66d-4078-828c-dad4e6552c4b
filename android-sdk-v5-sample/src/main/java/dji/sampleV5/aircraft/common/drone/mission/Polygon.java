package dji.sampleV5.aircraft.common.drone.mission;


import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import dji.sampleV5.aircraft.lbs.bean.AppLatLng;

public class Polygon {

	private List<AppLatLng> points = new ArrayList<>();

	public void setPoints(List<AppLatLng> pointList){
		points.clear();
		addPoints(pointList);
	}

	public void addPoints(List<AppLatLng> pointList) {
		points.addAll(pointList);
	}

	public void addPoint(AppLatLng coord) {
		points.add(coord);
	}

	public List<AppLatLng> getPoints() {
		return points;
	}

	public List<LineLatLng> getLines() {
		List<LineLatLng> list = new ArrayList<>();
		for (int i = 0; i < points.size(); i++) {
			int endIndex = (i == 0) ? points.size() - 1 : i - 1;
			list.add(new LineLatLng(points.get(i), points.get(endIndex)));
		}
		return list;
	}

	public void movePoint(AppLatLng coord, int number) {
		points.get(number).set(coord);
	}

	public Area getArea() {
		return GeoTools.getArea(this);
	}

	/*
	 * @Override public List<LatLng> getPathPoints() { List<LatLng> path =
	 * getLatLngList(); if (getLatLngList().size() > 2) { path.add(path.get(0));
	 * } return path; }
	 */

	public void checkIfValid() throws Exception {
		if (points.size() < 3) {
			throw new InvalidPolygon(points.size());
		}
	}

	public class InvalidPolygon extends Exception {
		private static final long serialVersionUID = 1L;
		public int size;

		public InvalidPolygon(int size) {
			this.size = size;
		}
	}

	public void reversePoints() {
		Collections.reverse(points);
	}

}
