package dji.sampleV5.aircraft.util.phone;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;
import android.view.ViewConfiguration;


import dji.sampleV5.aircraft.ContextUtil;

public final class DensityUtil {

    private static float density = -1f;

    private DensityUtil() {
    }

    public static float getDensity() {
        if (density == -1) {
            density = ContextUtil.getApplicationContext().getResources().getDisplayMetrics().density;
        }
        return density;
    }

    public static int dp2px(float dpValue) {
        return (int) (dpValue * getDensity() + 0.5f);
    }

    /**
     * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
     */
    public static int dip2px(Context context, float dpValue) {
        Resources r = context.getResources();
        float px = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
                dpValue, r.getDisplayMetrics());
        return (int) px;
    }

    public static int px2dp(float pxValue) {
        return (int) (pxValue / getDensity() + 0.5f);
    }

    public static int getScreenWidth() {
        Resources res = ContextUtil.getApplicationContext().getResources();
        if (res.getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            return res.getDisplayMetrics().widthPixels;
        }else {
            if (ContextUtil.getCurrentActivity() != null) {
                DisplayMetrics dm = new DisplayMetrics();
                ContextUtil.getCurrentActivity().getWindowManager().getDefaultDisplay().getRealMetrics(dm);
                return dm.widthPixels;
            }else {
                return res.getDisplayMetrics().widthPixels + getNavigationBarHeight();
            }
        }
    }


    public static int getScreenHeight() {
        Resources res = ContextUtil.getApplicationContext().getResources();
        if (res.getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT) {
            return res.getDisplayMetrics().heightPixels;
        }else {
            if (ContextUtil.getCurrentActivity() != null) {
                DisplayMetrics dm = new DisplayMetrics();
                ContextUtil.getCurrentActivity().getWindowManager().getDefaultDisplay().getRealMetrics(dm);
                return dm.heightPixels;
            }else {
                return res.getDisplayMetrics().heightPixels + getNavigationBarHeight();
            }
        }
    }

    /**
     * 获取导航栏高度
     */
    public static int getNavigationBarHeight() {
        Context context = ContextUtil.getApplicationContext();
        if (checkDeviceHasNavigationBar()){
            int rid = context.getResources().getIdentifier("config_showNavigationBar", "bool", "android");
            if (rid !=0 ){
                int resourceId = context.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
                return context.getResources().getDimensionPixelSize(resourceId);
            }
        }
        return 0;
    }

    public static boolean checkDeviceHasNavigationBar() {
        //通过判断设备是否有返回键、菜单键(不是虚拟键,是手机屏幕外的按键)来确定是否有navigation bar
        boolean hasMenuKey = ViewConfiguration.get(ContextUtil.getApplicationContext())
                .hasPermanentMenuKey();
        boolean hasBackKey = KeyCharacterMap
                .deviceHasKey(KeyEvent.KEYCODE_BACK);

        return !hasMenuKey && !hasBackKey;
    }
}
