package dji.sampleV5.aircraft.util.task;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Describe
 */
public class TimeUtil {

    private static TimeUtil timeUtil;

    public static TimeUtil getInstance(){
        if(timeUtil==null){
            timeUtil = new TimeUtil();
        }
        return timeUtil;
    }

    public static Date transferString2Date(String s) {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
        } catch (ParseException e) {
            //LOGGER.error("时间转换错误, string = {}", s, e);
        }
        return date;
    }

    public static String transferDate2String(long timestamp) {
        Date date = new Date(timestamp);
        String time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        return time;
    }
}
