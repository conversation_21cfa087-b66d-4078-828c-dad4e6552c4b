package dji.sampleV5.aircraft.util;


import java.util.ArrayList;
import java.util.List;

import dji.sampleV5.aircraft.net.bean.LocateInfo;


public class LatLngUtil {

    public static List<LocateInfo> landPotCut(LocateInfo from, LocateInfo to, double maxLimit){
        List<LocateInfo> result = new ArrayList<LocateInfo>();
        float distanceFromTo = MyMapUtil.calculateLineDistance(from,to);
        if(distanceFromTo <= maxLimit){
            result.add(to);
        }else{
            LocateInfo middle = new LocateInfo((from.getLatitude() + to.getLatitude()) / 2,(from.getLongitude() + to.getLongitude()) / 2);
            float distanceDiv = MyMapUtil.calculateLineDistance(from,middle);
            System.out.println("--distanceDiv:"+distanceDiv+"maxLimit:"+maxLimit+"middle"+middle);
            if(distanceDiv <= maxLimit){
                result.add(middle);
                result.add(to);
            }else{
                List<LocateInfo> lowlist = landPotCut(from, middle, maxLimit);
                List<LocateInfo> highlist = landPotCut(middle, to, maxLimit);
                result.addAll(lowlist);
                result.addAll(highlist);
            }
        }
        return result;
    }
    public static List<LocateInfo> landPotCut2(LocateInfo from, LocateInfo to, double maxLimit){
        List<LocateInfo> result = new ArrayList<LocateInfo>();
        float distanceFromTo = MyMapUtil.calculateLineDistance(from,to);
        if(distanceFromTo <= maxLimit){
            result.add(to);
        }else{
            LocateInfo middle = new LocateInfo((from.getLatitude() + to.getLatitude()) / 2,(from.getLongitude() + to.getLongitude()) / 2);
            float distanceDiv = MyMapUtil.calculateLineDistance(from,middle);
            System.out.println("--distanceDiv:"+distanceDiv+"maxLimit:"+maxLimit+"middle"+middle);
            result.add(middle);
            result.add(to);
        }
        return result;
    }
}
