package dji.sampleV5.aircraft.util;

import android.app.Activity;
import android.util.Log;

import com.alibaba.fastjson.JSONException;
import com.google.gson.Gson;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.data.task.JsonResult;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Describe
 */
public class HttpUtil {
    private static OkHttpClient client;

    private static  HttpUtil httpUtil;

    public static HttpUtil getInstance(){
        if(httpUtil == null){
            httpUtil = new HttpUtil();
        }
        return httpUtil;
    }

    private HttpUtil(){
        client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10,TimeUnit.SECONDS)
                .readTimeout(10,TimeUnit.SECONDS)
                .build();
    }
    public interface ICallBack{
        void onResponse(JsonResult jsonResult);
    }


    /*-------------------------------------------------------------------------------------------------------
    提供给外部调用的方法*/

    /**
     * post方式提交Json字符串
     *
     * @param activity  Activity
     * @param url       子路径
     * @param postBody  要发送的字符串
     * @param iCallBack 请求成功的回调
     */
    /*public void stringPost(Activity activity, String url, String postBody, ICallBack iCallBack) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(mediaType, postBody);
        Request request = new Request.Builder()
                .addHeader("qicloudToken", SpUtil.getLoginCache().getQicloudToken())
                .url(NetConfig.YG_FORMAL  + url)
                .post(requestBody)
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.d("onFailure", e.getMessage());

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (response.code() != 200) {

                } else {
                    JsonResult jsonResult = new JsonResult();
                    try {
                        JSONObject jsonObject = new JSONObject(response.body().string());
                        jsonResult.setCode(String.valueOf(jsonObject.getInt("code")));
                        jsonResult.setData(jsonObject.getString("data"));
                        jsonResult.setReason(jsonObject.getString("reason"));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (org.json.JSONException e) {
                        e.printStackTrace();
                    }
                    requestSuccess(activity, jsonResult, iCallBack);
                }
            }
        });
    }*/

    /**
     * post方式提交表单（已自动添加personToken）
     *
     * @param activity  Activity
     * @param url       根路径后面的子路径
     * @param map       要发送的数据Map
     * @param iCallBack 请求成功的回调
     */
    /*public void formPost(Activity activity, String url, Map<String, String> map, final ICallBack iCallBack) {
        FormBody.Builder builder = new FormBody.Builder();
        //从SharedPreference中取出personToken添加到表单中
        builder.add("id", activity.getSharedPreferences("user", Activity.MODE_PRIVATE).getString("personToken", ""));
        for (Map.Entry<String, String> entry : map.entrySet()) {
            builder.add(entry.getKey(), entry.getValue());
        }
        FormBody formBody = builder.build();
        Request request = new Request.Builder()
                .addHeader("qicloudToken", SpUtil.getLoginCache().getQicloudToken())
                //根路径拼接子路径
                .url(NetConfig.YG_FORMAL  + url)
                .post(formBody)
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.d("onFailure", e.getMessage());

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (response.code() != 200) {

                } else {
                    JsonResult jsonResult = new JsonResult();
                    try {
                        JSONObject jsonObject = new JSONObject(response.body().string());
                        jsonResult.setCode(String.valueOf(jsonObject.getInt("code")));
                        jsonResult.setData(jsonObject.getString("data"));
                        jsonResult.setReason(jsonObject.getString("reason"));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (org.json.JSONException e) {
                        e.printStackTrace();
                    }
                    requestSuccess(activity, jsonResult, iCallBack);
                }
            }
        });
    }*/

    /**
     * Post方式上传图片
     *
     * @param activity  Activity
     * @param url       根路径后面的子路径
     * @param id        id
     * @param file      图片文件
     * @param iCallBack 请求成功的回调
     */
    /*public void formFilePost(Activity activity, String url, String id, File file, final ICallBack iCallBack) {
        MediaType mediaType = MediaType.parse("image/png");
        RequestBody fileBody = RequestBody.create(mediaType, file);
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", "file", fileBody)
                .addFormDataPart("id", id)
                .build();
        Request request = new Request.Builder()
                .addHeader("qicloudToken", SpUtil.getLoginCache().getQicloudToken())
                .url(NetConfig.YG_FORMAL  + url)
                .post(requestBody)
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.d("onFailure", e.getMessage());

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                if (response.code() != 200) {

                } else {
                    JsonResult result = new Gson().fromJson(response.body().string(), JsonResult.class);
                    requestSuccess(activity, result, iCallBack);
                }
            }
        });
    }*/

    /**
     * get请求（已自动添加personToken）
     *
     * @param activity  Activity
     * @param url       根路径后面的子路径
     * @param map       要发送的数据Map
     * @param iCallBack 请求成功的回调
     */
    public void get(Activity activity, String url, Map<String, String> map,String tokenName, final ICallBack iCallBack) {
        StringBuilder builder = new StringBuilder(url);
        //从SharedPreference中取出personToken拼接到url后面
        builder.append("?");
      //  builder.append(activity.getSharedPreferences("user", Activity.MODE_PRIVATE).getString("personToken", ""));
        for (Map.Entry<String, String> entry : map.entrySet()) {

            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
            builder.append("&");
        }
        builder.substring(0,builder.length()-1);
        Request request = new Request.Builder()
                //根路径拼接子路径
                .addHeader(tokenName, SpUtil.getLoginCache().getApiToken())
                .addHeader("user", SpUtil.getLoginCache().getUser())
                .url(builder.toString())
                .get()
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.e("onFailure", e.getMessage());

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                Log.e("TAG", "onResponse: "+response.toString());
                if (response.code() != 200) {

                } else {
                    JsonResult jsonResult = new JsonResult();
                    Log.e("TAG", "onResponse: "+response.toString());
                    try {
                        JSONObject jsonObject = new JSONObject(response.body().string());
                        jsonResult.setCode(String.valueOf(jsonObject.getInt("code")));
                        jsonResult.setData(jsonObject.getString("data"));
                        jsonResult.setReason(jsonObject.getString("reason"));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (org.json.JSONException e) {
                        e.printStackTrace();
                    }
                    requestSuccess(activity, jsonResult, iCallBack);
                }
            }
        });
    }

    public void getByApiToken(Activity activity, String url, Map<String, String> map,String apiToken, final ICallBack iCallBack) {
        StringBuilder builder = new StringBuilder(url);
        //从SharedPreference中取出personToken拼接到url后面
        builder.append("?");
        //  builder.append(activity.getSharedPreferences("user", Activity.MODE_PRIVATE).getString("personToken", ""));
        for (Map.Entry<String, String> entry : map.entrySet()) {

            builder.append(entry.getKey());
            builder.append("=");
            builder.append(entry.getValue());
            builder.append("&");
        }
        builder.substring(0,builder.length()-1);
        Request request = new Request.Builder()
                //根路径拼接子路径
                .addHeader("token", apiToken)
                .url(builder.toString())
                .get()
                .build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(@NotNull Call call, @NotNull IOException e) {
                Log.e("onFailure", e.getMessage());

            }

            @Override
            public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                Log.e("TAG", "onResponse: "+response.toString());
                if (response.code() != 200) {

                } else {
                    JsonResult jsonResult = new JsonResult();
                    try {
                        JSONObject jsonObject = new JSONObject(response.body().string());
                        jsonResult.setCode(String.valueOf(jsonObject.getInt("code")));
                        jsonResult.setData(jsonObject.getString("data"));
                        jsonResult.setReason(jsonObject.getString("reason"));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    } catch (org.json.JSONException e) {
                        e.printStackTrace();
                    }
                    requestSuccess(activity, jsonResult, iCallBack);
                }
            }
        });
    }

    /*-------------------------------------------------------------------------------------------------------*/

    /**
     * 请求成功后，将获取的数据传递给回调方法
     * 回调方法中的内容直接运行在主线程中
     *
     * @param activity  Activity
     * @param result    JsonResult 拿到的数据
     * @param iCallBack 回调接口
     */
    private static void requestSuccess(Activity activity, final JsonResult result, final ICallBack iCallBack) {
        activity.runOnUiThread(() -> {
            if (iCallBack != null) {
                iCallBack.onResponse(result);
            }
        });
    }

}
