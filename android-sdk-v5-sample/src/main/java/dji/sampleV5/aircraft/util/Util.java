package dji.sampleV5.aircraft.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import com.amap.api.maps.model.LatLng;

import java.util.List;
import java.util.Locale;

public class Util {

    public static boolean nullTo(Integer raw, boolean other) {
        return raw == null ? other : (raw == 1);
    }

    public static float nullTo(Float raw, float other) {
        return raw == null ? other : raw;
    }

    public static float nullTo(Integer raw, float other) {
        return raw == null ? other : raw;
    }

    public static String nullToEmpty(String raw) {
        return raw == null ? "" : raw;
    }

    public static String getDot(float raw, int limit) {
        return String.format("%." + limit + "f", raw);
    }

    public static String formatRecordTime(long second) {
        return String.format(Locale.CHINA, "%02d:%02d:%02d", second / 3600, second / 60, second % 60);
    }


    public static String formatSecond2Hour(long second) {
        if (second / 3600 > 99) {
            return ">99h";
        }
        return String.format(Locale.CHINA, "%02d:%02d:%02d", second / 3600, (second % 3600) / 60, (second % 3600) % 60);
    }


    public static boolean checkGpsCoordinate(double latitude, double longitude) {
        return (latitude > -90 && latitude < 90 && longitude > -180 && longitude < 180) && (latitude != 0f && longitude != 0f);
    }


    public static String getAppVersionName(Context context) {
        PackageManager packageManager = context.getPackageManager();
        PackageInfo packInfo = null;
        try {
            packInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return packInfo == null ? "" : packInfo.versionName;
    }

    public static void toGaode(Context mContext, String latitude, String longitude) {
        if (isInstallApk(mContext, "com.autonavi.minimap")) {// 是否安装了高德
            Intent intents = new Intent();
            if (!TextUtils.isEmpty(latitude.trim()) && !TextUtils.isEmpty(longitude.trim())) {
                //intents.setData(Uri.parse("androidamap://navi?sourceApplication=nyx_super&lat=" + latitude.trim() + "&lon=" + longitude.trim() + "&dev=0&style=2"));
                intents.setData(android.net.Uri.parse("androidamap://route?sourceApplication=appName&slat=&slon=&sname=我的位置&dlat="+ latitude.trim() +"&dlon="+ longitude.trim()+"&dname=目的地&dev=0&t=2"));
                mContext.startActivity(intents); // 启动调用
            }
        }else {
            ToastUtil.show("请安装高德地图");
        }
    }

    public static void toBaidu(Context mContext, String latitude, String longitude) {
        if (isInstallApk(mContext, "com.baidu.BaiduMap")) {// 是否安装了百度地图
            Intent intent = new Intent();
            // 驾车导航
            if (!TextUtils.isEmpty(latitude.trim()) && !TextUtils.isEmpty(longitude.trim())) {
                try {
                    LatLng newLatLng = GCJ2BD(new LatLng(Double.valueOf(latitude.trim()), Double.valueOf(longitude.trim())));
                    //intent.setData(Uri.parse("baidumap://map/navi?location=" + newLatLng.latitude + "," + newLatLng.longitude));
                    intent.setData(android.net.Uri.parse("baidumap://map/geocoder?location=" + newLatLng.latitude  + "," + newLatLng.longitude));
                    mContext.startActivity(intent); // 启动调用
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }else {
            ToastUtil.show("请安装百度地图");
        }
    }

    public static boolean isInstallApk(Context mContext, String name) {
        List<PackageInfo> packageInfoList = mContext.getPackageManager().getInstalledPackages(0);
        for (int i = 0; i < packageInfoList.size(); i++) {
            PackageInfo packageInfo = packageInfoList.get(i);
            if (packageInfo.packageName.equals(name)) {
                return true;
            } else {
                continue;
            }
        }
        return false;
    }

    /**
     * 百度地图需要传入BD09坐标系
     * GCJ-02 坐标转换成 BD-09 坐标
     */
    public static LatLng GCJ2BD(LatLng bd) {
        double x = bd.longitude, y = bd.latitude;
        double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * Math.PI);
        double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * Math.PI);
        double tempLon = z * Math.cos(theta) + 0.0065;
        double tempLat = z * Math.sin(theta) + 0.006;
        return new LatLng(tempLat, tempLon);
    }

    public static String getAppName(Context context) {
        String appName = "";
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(
                    context.getPackageName(), 0);
            int labelRes = packageInfo.applicationInfo.labelRes;
            appName =  context.getResources().getString(labelRes);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return appName;
    }
}
