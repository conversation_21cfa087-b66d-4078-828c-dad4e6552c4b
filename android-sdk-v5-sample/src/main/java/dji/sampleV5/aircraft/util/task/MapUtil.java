package dji.sampleV5.aircraft.util.task;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.graphics.Color;

import com.amap.api.maps.AMap;
import com.amap.api.maps.model.BitmapDescriptorFactory;
import com.amap.api.maps.model.LatLng;
import com.amap.api.maps.model.Marker;
import com.amap.api.maps.model.MarkerOptions;
import com.amap.api.maps.model.Polyline;
import com.amap.api.maps.model.PolylineOptions;
import dji.sampleV5.aircraft.R;
import dji.sampleV5.aircraft.data.task.MissionInfo;
import dji.sampleV5.aircraft.lbs.MapIndex;
import dji.sampleV5.aircraft.util.coordinate.GeoSysConversion;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe
 */
public class MapUtil {
    private Context context;
    private AMap aMap;
    private List<MissionInfo.FlightRecordBean> flightRecordsBeanList;
    private ArrayList<LatLng> latLngs = new ArrayList<>();
    private PolylineOptions polylineOptions;
    private Polyline polyline;
    //private Marker gimbal;
    private Marker aircraft;
    private int i = 0;
    private Map<LatLng, Long> map = new HashMap<LatLng, Long>();

    public MapUtil(Context context, AMap aMap) {
        this.context = context;
        this.aMap = aMap;
        PolylineOptions polylineOptions = new PolylineOptions().width(10).color(Color.argb(255, 0, 255, 0)).zIndex(MapIndex.DRONE_INDEX);
        polyline = aMap.addPolyline(polylineOptions);
        /*MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.icon(BitmapDescriptorFactory.fromBitmap(BitmapFactory
                .decodeResource(context.getResources(), R.drawable.ic_gimbal_yaw)));
        gimbal =aMap.addMarker(markerOptions);*/
        MarkerOptions markerOptions1 = new MarkerOptions();
        markerOptions1.icon(BitmapDescriptorFactory.fromBitmap(BitmapFactory
                .decodeResource(context.getResources(), R.drawable.aircraft)))
                .zIndex(MapIndex.DRONE_INDEX)
                .setFlat(true)
                .anchor(0.5f, 0.5f);

        aircraft = aMap.addMarker(markerOptions1);
    }

    public void setInfoList(MissionInfo missionInfo) {
        this.flightRecordsBeanList = missionInfo.getFlightRecords().list;

    }

    public interface ArriveCallback {
        void toPoint(MissionInfo.FlightRecordBean operateRecordsBean);
    }

    private ArriveCallback arriveCallback;

    public void setArriveCallback(ArriveCallback arriveCallback) {
        this.arriveCallback = arriveCallback;
    }

    public void addPoint(float point) {
        /* if(point>=flightRecordsBeanList.get(i).getTimestamp() && i < flightRecordsBeanList.size() - 1) {*/
        //i++;
        i = (int) point / 500;
        if (i < flightRecordsBeanList.size() - 1) {
            double[] position = GeoSysConversion.wgs84toGCJ02(flightRecordsBeanList.get(i).getLatitude(), flightRecordsBeanList.get(i).getLongitude());
            LatLng latLng = new LatLng(position[0], position[1]);
            latLngs.add(latLng);
            map.put(latLng, flightRecordsBeanList.get(i).getTimestamp());
            polyline.setPoints(latLngs);
            //gimbal.setPosition(latLng);
            aircraft.setPosition(latLng);
            rotateMarker(flightRecordsBeanList.get(i), i);
            if (this.arriveCallback != null) {
                arriveCallback.toPoint(flightRecordsBeanList.get(i));
            }
        }
    }

    private void rotateMarker(MissionInfo.FlightRecordBean operateRecordsBean, int i) {
        //if(i==0)return;
        //float start = flightRecordsBeanList.get(i-1).getGimbalYaw();
       /* Animation animation = new RotateAnimation(start,operateRecordsBean.getGimbalYaw(),0,0,0);
        long duration = 50L;
        animation.setDuration(duration);
        animation.setInterpolator(new LinearInterpolator());*/
       /* gimbal.setAnimation(animation);
        gimbal.startAnimation();*/
        //float start1= flightRecordsBeanList.get(i-1).getUAVYaw();
      /*  Animation animation1 = new RotateAnimation(start1,operateRecordsBean.getUAVYaw(),0,0,0);
        animation1.setDuration(50L);
        animation1.setInterpolator(new LinearInterpolator());*/
        aircraft.setRotateAngle((360 - operateRecordsBean.getUAVYaw()) % 360);
        /*aircraft.setAnimation(animation1);
        aircraft.startAnimation();*/
    }

    public void seekTo(long point) {
        i = (int) point / 500;
        latLngs.clear();
        if (i < flightRecordsBeanList.size() - 1) {
            for (int j = 0; j < i; j++) {
                double[] position = GeoSysConversion.wgs84toGCJ02(flightRecordsBeanList.get(j).getLatitude(), flightRecordsBeanList.get(j).getLongitude());
                LatLng latLng = new LatLng(position[0], position[1]);
                latLngs.add(latLng);
            }
            polyline.setPoints(latLngs);
            aircraft.setPosition(latLngs.get(latLngs.size() - 1));
            rotateMarker(flightRecordsBeanList.get(i), i);
            /*while (flightRecordsBeanList.get(i).getTimestamp() <= point) {

                map.put(latLngs.get(latLngs.size() - 1), flightRecordsBeanList.get(i).getTimestamp());
                polyline.setPoints(latLngs);
                aircraft.setPosition(latLngs.get(latLngs.size() - 1));
                //gimbal.setPosition(latLngs.get(latLngs.size()-1));
                i++;
            }*/
        } /*else {
            while (map.get(latLngs.get(latLngs.size() - 1)) > point) {
                latLngs.remove(latLngs.size() - 1);
                polyline.setPoints(latLngs);
                aircraft.setPosition(latLngs.get(latLngs.size() - 1));
                //gimbal.setPosition(latLngs.get(latLngs.size()-1));
                i--;
            }
        }*/

    }


}
