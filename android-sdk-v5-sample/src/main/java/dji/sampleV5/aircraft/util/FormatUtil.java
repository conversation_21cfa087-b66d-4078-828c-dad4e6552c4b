package dji.sampleV5.aircraft.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.util.Locale;
import java.util.regex.Pattern;

public class FormatUtil {
    private static final int MIN_CLICK_DELAY_TIME = 1000;
    private static long lastClickTime;

    /**
     * 获得指定小数位数的Float内容
     *
     * @param value 原始float内容
     * @param scale 小数点后位数
     * @return
     */
    public static Float getFloatByFormat(float value, int scale) {
        int roundingMode = BigDecimal.ROUND_HALF_UP;//表示四舍五入，可以选择其他舍值方式，例如去尾，等等.
        BigDecimal bd = new BigDecimal((double) value);
        bd = bd.setScale(scale, roundingMode);
        return bd.floatValue();
    }

    /**
     * 获得指定小数位数的Double内容
     *
     * @param value 原始double内容
     * @param scale 小数点后位数
     * @return
     */
    public static Double getDoubleByFormat(double value, int scale) {
        BigDecimal bg = new BigDecimal(value);
        return bg.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * Int转String
     *
     * @param in
     * @param format "0000"
     * @return
     */
    public static String convertIntToString(int in, String format) {
        return new DecimalFormat(format).format(in);
    }

    /**
     * 判断输入是否为手机号码
     */
    public static boolean is_reg_mobleNumber(String mobileNumber) {
        if (StringUtil.isEmpty(mobileNumber)) return false;
        return Pattern.matches("1\\d{10}", mobileNumber);
    }

    /*  */

    /**
     * 根据markertitle 返回是第几个marker
     **//*
    public static int abc(Marker marker, List<ProjectHiveMarkerBean> list){
        int  postion = 0;
        if (list == null || list.size()==0){return postion;}
        for (int i = 0; i<list.size();i++){
            if (marker.getTitle().equals(list.get(i).getMarker().getTitle())){
                postion =i;
                return postion;
            }
        }
        return postion;
    }
    public static int hivemarker(List<HiveInfoBean> list){
        int hiveicon = 0;
        UavmInfo2 uavmInfo2 = UavmInfo2.getInstance();
        if (list.size()>5){
            uavmInfo2.mapzoom = 14;
            hiveicon = R.mipmap.closedxiao;
        }else if (list.size()>3){
            uavmInfo2.mapzoom = 15;
            hiveicon = R.mipmap.closedzhong;
        }else {
            uavmInfo2.mapzoom = 16;
            hiveicon = R.mipmap.closed;
        }
        return hiveicon;
    }*/


    // 两次点击按钮之间的点击间隔不能少于1000毫秒
    public static boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        if ((curClickTime - lastClickTime) >= MIN_CLICK_DELAY_TIME) {
            flag = true;
        }
        lastClickTime = curClickTime;
        return flag;
    }

    /**
     * convert px to its equivalent dp
     * <p>
     * 将px转换为与之相等的dp
     */
    public static int px2dp(Context context, float pxValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (pxValue / scale + 0.5f);
    }


    /**
     * 14      * convert dp to its equivalent px
     * 15      *
     * 16      * 将dp转换为与之相等的px
     * 17
     */
    public static int dp2px(Context context, float dipValue) {
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * scale + 0.5f);
    }

    //debug  ； F1:BC:F0:D4:80:87:AD:1E:E8:E4:7D:00:F8:F2:9F:54:C6:11:1E:A0
    //正式  CB:C2:EA:1C:5B:F0:C3:1C:95:F5:06:11:10:E4:4A:5F:13:66:8A:ED
    public static String sHA1(Context context) {
        try {
            PackageInfo info = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            byte[] cert = info.signatures[0].toByteArray();
            MessageDigest md = MessageDigest.getInstance("SHA1");
            byte[] publicKey = md.digest(cert);
            StringBuffer hexString = new StringBuffer();
            for (int i = 0; i < publicKey.length; i++) {
                String appendString = Integer.toHexString(0xFF & publicKey[i])
                        .toUpperCase(Locale.US);
                if (appendString.length() == 1)
                    hexString.append("0");
                hexString.append(appendString);
                hexString.append(":");
            }
            String result = hexString.toString();
            return result.substring(0, result.length() - 1);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 提取int
     **/
    public static int main(String str) {
        str = str.trim();
        StringBuilder str2 = new StringBuilder();
        if (!"".equals(str)) {
            for (int i = 0; i < str.length(); i++) {
                if (str.charAt(i) >= 48 && str.charAt(i) <= 57) {
                    str2.append(str.charAt(i));
                }
            }
            return Integer.parseInt(str2.toString());
        }
        return 1;
    }

    public static boolean isShouldHideInput(View v, MotionEvent event) {
        if (v != null && (v instanceof EditText)) {
            int[] leftTop = {0, 0};
            //获取输入框当前的location位置
            v.getLocationInWindow(leftTop);
            int left = leftTop[0];
            int top = leftTop[1];
            int bottom = top + v.getHeight();
            int right = left + v.getWidth();
            if (event.getX() > left && event.getX() < right
                    && event.getY() > top && event.getY() < bottom) {
                // 点击的是输入框区域，保留点击EditText的事件
                return false;
            } else {
                //使EditText触发一次失去焦点事件
                v.setFocusable(false);
                v.setFocusableInTouchMode(true);
                return true;
            }
        }
        return false;
    }

}
