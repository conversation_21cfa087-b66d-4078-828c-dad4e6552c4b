package dji.sampleV5.aircraft.util;

import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.StringRes;


import dji.sampleV5.aircraft.ContextUtil;
import dji.sampleV5.aircraft.util.toast.ToastCompat;


public class ToastUtil {
    private static final long SHORT_DURATION_TIMEOUT = 4000;
    private static String sLstMsg;
    private static long sLstTime;

    public static void show(String msg) {
        if(TextUtils.isEmpty(msg)){
            return;
        }
        if (msg.equals(sLstMsg)) {
            long crtTime = System.currentTimeMillis();
            if (crtTime - sLstTime > SHORT_DURATION_TIMEOUT) {
                sLstTime = crtTime;
            } else {
                return;
            }
        } else {
            sLstMsg = msg;
        }
        ContextUtil.getHandler().post(() -> ToastCompat.makeText(ContextUtil.getApplicationContext(), msg, Toast.LENGTH_SHORT).show());
    }

    public static void show(@StringRes int resId) {
        show(ContextUtil.getString(resId));
    }
}
