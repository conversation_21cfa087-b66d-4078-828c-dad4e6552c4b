package dji.sampleV5.aircraft.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.Base64;


import com.elvishew.xlog.XLog;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import dji.sampleV5.aircraft.ContextUtil;

public class BitmapUtils {
    private static final Pattern Base64Pattern = Pattern.compile("^data:image/(\\w+);base64,");

    public static Bitmap getNumberBitmap(int iconSize, String number) {
        return getNumberBitmap(iconSize, number, Color.WHITE, Color.BLUE);
    }

    public static Bitmap getStartBitmap(int iconSize) {
        return getNumberBitmap(iconSize, "S", Color.GREEN, Color.WHITE);
    }

    public static Bitmap getEndBitmap(int iconSize) {
        return getNumberBitmap(iconSize, "E", Color.RED, Color.WHITE);
    }

    public static Bitmap getChooseNumberBitmap(int iconSize, String number) {
        return getNumberBitmap(iconSize, number, Color.BLUE, Color.WHITE);
    }

    private static Bitmap getNumberBitmap(int iconSize, String number, int bgColor, int textColor) {
        Bitmap bitmap = Bitmap.createBitmap(iconSize, iconSize, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        RectF rect = new RectF(0, 0, canvas.getWidth(), canvas.getHeight());
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DEV_KERN_TEXT_FLAG);
        // draw background
        paint.setColor(bgColor);
        canvas.drawOval(rect, paint);
        // draw text
        paint.setColor(textColor);
        paint.setStrokeWidth(3);
        paint.setTextSize(iconSize * 0.6f);
        paint.setTextAlign(Paint.Align.CENTER);
        Paint.FontMetricsInt fontMetrics = paint.getFontMetricsInt();
        float baseline = (rect.bottom + rect.top - fontMetrics.bottom - fontMetrics.top) / 2;
        canvas.drawText(number, rect.centerX(), baseline, paint);
        return bitmap;
    }

    //图片裁切
    public static Bitmap cropBitmap(Bitmap bitmap) {
        // 检查输入参数合理性
        if (bitmap == null) {
            throw new IllegalArgumentException("Invalid input parameters");
        }
        // 使用Application的Context避免潜在的内存泄露
        Context context = ContextUtil.getApplicationContext();
        // 获取屏幕宽高
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
        int screenHeight = context.getResources().getDisplayMetrics().heightPixels;
        // 计算裁剪区域的中心点坐标
        int centerScreenX = screenWidth / 2;
        int centerScreenY = screenHeight / 2;
        // 计算裁剪区域的左上角坐标
        int desiredX = centerScreenX - ((bitmap.getWidth() - 1000) / 2);
        int desiredY = centerScreenY - (bitmap.getHeight() / 2);

        // 检查裁剪区域是否在原始 Bitmap 范围内
        if (desiredX < 0 || desiredY < 0 ||
                desiredX + (bitmap.getWidth() - 1000) > bitmap.getWidth() ||
                desiredY + bitmap.getHeight() > bitmap.getHeight()) {
            // 添加日志记录
            XLog.e("CropBitmap", "裁剪区域超出原始 Bitmap 范围");
            throw new IllegalArgumentException("裁剪区域超出原始 Bitmap 范围");
        }
        // 返回裁剪后的Bitmap
        return Bitmap.createBitmap(
                bitmap,
                desiredX, desiredY,
                (bitmap.getWidth() - 1000), bitmap.getHeight()
        );
    }


    /**
     * 将 Bitmap 转换为 Base64 编码的字符串。
     *
     * @param bitmap   需要转换的 Bitmap 对象。
     * @param format   图片压缩格式，如 Bitmap.CompressFormat.PNG 或 Bitmap.CompressFormat.JPEG。
     * @param quality  压缩质量，范围为 0 到 100，值越高，质量越好，但生成的 Base64 字符串越长。
     * @return         Base64 编码的字符串。
     */
    public static String bitmapToBase64AsDataUri(Bitmap bitmap, Bitmap.CompressFormat format, int quality) {

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            bitmap.compress(format, quality, outputStream);
            byte[] byteArray = outputStream.toByteArray();
            String base64EncodedString = Base64.encodeToString(byteArray, 0, byteArray.length, Base64.NO_WRAP);
            String mimeType;
            switch (format) {
                case JPEG:
                    mimeType = "image/jpeg";
                    break;
                case PNG:
                default:
                    mimeType = "image/png"; // 默认使用 PNG MIME 类型
            }
            return "data:" + mimeType + ";base64," + base64EncodedString;
        } catch (IOException e) {
            // 处理压缩过程中可能出现的异常（可选）
            e.printStackTrace();
            return null; // 或者抛出异常，根据您的需求选择合适的错误处理方式
        }
    }

}