package dji.sampleV5.aircraft.util.coordinate;


public class LatLngUtil {

    /**
     * 获取两点之间的距离
     */
    public static float calculateLineDistance(double startLat, double startLng, double endLat, double endLng) {
        try {
            double var2 = 0.01745329251994329D;
            double var4 = startLng;
            double var6 = startLat;
            double var8 = endLng;
            double var10 = endLat;
            var4 *= var2;
            var6 *= var2;
            var8 *= var2;
            var10 *= var2;
            double var12 = Math.sin(var4);
            double var14 = Math.sin(var6);
            double var16 = Math.cos(var4);
            double var18 = Math.cos(var6);
            double var20 = Math.sin(var8);
            double var22 = Math.sin(var10);
            double var24 = Math.cos(var8);
            double var26 = Math.cos(var10);
            double[] var28 = new double[3];
            double[] var29 = new double[3];
            var28[0] = var18 * var16;
            var28[1] = var18 * var12;
            var28[2] = var14;
            var29[0] = var26 * var24;
            var29[1] = var26 * var20;
            var29[2] = var22;
            double var30 = Math.sqrt((var28[0] - var29[0]) * (var28[0] - var29[0]) + (var28[1] - var29[1]) * (var28[1] - var29[1]) + (var28[2] - var29[2]) * (var28[2] - var29[2]));
            return (float) (Math.asin(var30 / 2.0D) * 1.27420015798544E7D);
        } catch (Throwable var32) {
            var32.printStackTrace();
            return 0.0F;
        }
    }


    public static double vertical(double rawAngle) {
        return -1;
    }

}
