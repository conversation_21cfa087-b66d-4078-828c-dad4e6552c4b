package dji.sampleV5.aircraft.util

import android.content.Context
import android.os.Message
import dji.sampleV5.aircraft.mvvm.util.XLogUtil.d
import dji.sdk.keyvalue.key.CameraKey
import dji.sdk.keyvalue.key.FlightControllerKey
import dji.sdk.keyvalue.key.KeyTools
import dji.sdk.keyvalue.value.camera.CameraExposureCompensation
import dji.sdk.keyvalue.value.camera.CameraExposureMode
import dji.sdk.keyvalue.value.camera.CameraFocusMode
import dji.sdk.keyvalue.value.camera.CameraISO
import dji.sdk.keyvalue.value.camera.CameraShutterSpeed
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType
import dji.sdk.keyvalue.value.common.CameraLensType
import dji.sdk.keyvalue.value.common.ComponentIndexType
import dji.sdk.keyvalue.value.flightcontroller.FailsafeAction
import dji.v5.common.callback.CommonCallbacks
import dji.v5.common.error.IDJIError
import dji.v5.manager.KeyManager
import dji.v5.manager.aircraft.perception.PerceptionManager
import dji.v5.manager.aircraft.perception.data.PerceptionDirection

object KeyOperateUtil {

    fun setExposureMode(context: Context, mode: CameraExposureMode, type: CameraLensType?, cameraIndex: ComponentIndexType, onSuccess: (CameraExposureMode) -> Unit = {}, onFailure: (String) ->Unit = {}) {
        val exposureModeKey = KeyTools.createCameraKey(
            CameraKey.KeyExposureMode,
            cameraIndex,
            type
        )
        KeyManager.getInstance()
            .setValue(exposureModeKey, mode, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    onSuccess.invoke(mode)
                    if (mode == CameraExposureMode.PROGRAM) {
                        d(context.javaClass.name, "设置曝光模式为PROGRAM成功")
                    } else if (mode == CameraExposureMode.MANUAL) {
                        d(context.javaClass.name, "设置曝光模式为MANUAL成功")
                    }
                }

                override fun onFailure(idjiError: IDJIError) {
                    var content =
                        "设置曝光模式为PROGRAM失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    if (mode == CameraExposureMode.PROGRAM) {
                        content =
                            "设置曝光模式为PROGRAM失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    } else if (mode == CameraExposureMode.MANUAL) {
                        content =
                            "设置曝光模式为MANUAL失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    }
                    d(context.javaClass.name, content)
                }
            })
    }

    /**
     * 处理镜头源切换
     *
     * @param source
     */
    fun setVideoStreamSourceType(context: Context, source: CameraVideoStreamSourceType, cameraIndex: ComponentIndexType, ) {
        val videoStreamSourceTypeKey = KeyTools.createKey(
            CameraKey.KeyCameraVideoStreamSource,
            cameraIndex
        )
        KeyManager.getInstance().setValue(
            videoStreamSourceTypeKey,
            source,
            object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    d(context.javaClass.name, "设置视频源类型成功")
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content =
                        "设置视频源类型失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    d(context.javaClass.name, content)
                }
            })
    }

    fun setCameraFocusRingValue(context: Context, value: Int, cameraIndex: ComponentIndexType, ) {
        val cameraFocusRingKey = KeyTools.createCameraKey(
            CameraKey.KeyCameraFocusRingValue,
            cameraIndex,
            CameraLensType.CAMERA_LENS_ZOOM
        )
        KeyManager.getInstance()
            .setValue(cameraFocusRingKey, value, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    val message = Message.obtain()
                    message.what = 1
                    message.obj = value

                    d(context.javaClass.name, "设置手动对焦值成功")
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content =
                        "设置手动对焦值失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    d(context.javaClass.name, content)
                }
            })
    }


    fun setCameraFocusMode(context: Context, mode: CameraFocusMode, cameraIndex: ComponentIndexType, ) {
        val cameraFocusModeKey = KeyTools.createCameraKey(
            CameraKey.KeyCameraFocusMode,
            cameraIndex,
            CameraLensType.CAMERA_LENS_ZOOM
        )
        KeyManager.getInstance()
            .setValue(cameraFocusModeKey, mode, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    val content = if (mode == CameraFocusMode.AFC) {
                        "设置对焦模式AFC成功"
                    } else if (mode == CameraFocusMode.AF) {
                        "设置对焦模式AF成功"
                    } else {
                        "设置对焦模式MANUAL成功"
                    }
                    d(context.javaClass.name, content)
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content = if (mode == CameraFocusMode.AFC) {
                        "设置对焦模式AFC失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    } else if (mode == CameraFocusMode.AF) {
                        "设置对焦模式AF失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    } else {
                        "设置对焦模式MANUAL失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    }
                    d(context.javaClass.name, content)
                    val finalContent = content
                }
            })
    }


    fun setCameraExposureCompensationValue(context: Context, value: CameraExposureCompensation, cameraIndex: ComponentIndexType, lensType: CameraLensType?, onSuccess: () -> Unit = {}, onFailure: (String) ->Unit = {}) {
        val evKey = KeyTools.createCameraKey(
            CameraKey.KeyExposureCompensation,
            cameraIndex,
            lensType
        )
        KeyManager.getInstance()
            .setValue(evKey, value, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    d(context.javaClass.name, "设置曝光补偿值成功")
                    onSuccess.invoke()
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content =
                        "设置曝光补偿值失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    d(context.javaClass.name, content)
                    onFailure.invoke(content)
                }
            })
    }

    fun setShutterSpeedValue(context: Context, value: CameraShutterSpeed, cameraIndex: ComponentIndexType, lensType: CameraLensType?, onSuccess: () -> Unit = {}, onFailure: (String) ->Unit = {}) {
        val shutterSpeedKey = KeyTools.createCameraKey(
            CameraKey.KeyShutterSpeed,
            cameraIndex,
            lensType
        )
        KeyManager.getInstance()
            .setValue(shutterSpeedKey, value, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    d(context.javaClass.name, "设置快门值成功")
                    onSuccess.invoke()
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content =
                        "设置快门值失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    d(context.javaClass.name, content)
                    onFailure.invoke(content)
                }
            })
    }

    fun setCameraISOValue(context: Context, value: CameraISO, cameraIndex: ComponentIndexType, lensType: CameraLensType?, onSuccess: () -> Unit = {}, onFailure: (String) ->Unit = {}) {
        val isoKey =
            KeyTools.createCameraKey(CameraKey.KeyISO, cameraIndex, lensType)
        KeyManager.getInstance()
            .setValue(isoKey, value, object : CommonCallbacks.CompletionCallback {
                override fun onSuccess() {
                    onSuccess.invoke()
                    d(context.javaClass.name, "设置ISO值 ${value} 成功")
                }

                override fun onFailure(idjiError: IDJIError) {
                    val content = "设置ISO值失败\t" + idjiError.errorCode() + "\t" + idjiError.description()
                    d(context.javaClass.name, content)
                    onFailure(content)
                }
            })
    }
}
