package dji.sampleV5.aircraft.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.net.util.Base64;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class CompressUtil {

	/**
	 * 字符串的压缩
	 *
	 * @param str 待压缩的字符串
	 * @return 返回压缩后的字符串
	 * @throws IOException
	 */
	public static String compress(String str) throws IOException {
		if (null == str || str.length() <= 0) {
			return null;
		}
		// 创建一个新的 byte 数组输出流
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		// 使用默认缓冲区大小创建新的输出流
		GZIPOutputStream gzip = new GZIPOutputStream(out);
		// 将 b.length 个字节写入此输出流
		gzip.write(str.getBytes("UTF-8"));
		gzip.close();
		// 使用指定的 charsetName，通过解码字节将缓冲区内容转换为字符串
		return out.toString("ISO-8859-1");
	}

	public static String compressEncode(Object res) throws IOException {
		String json = JSON.toJSONString(res, SerializerFeature.DisableCircularReferenceDetect);
		String com = compress(json);
		if (null == com) {
			return null;
		}
		String base = Base64.encodeBase64String(com.getBytes("ISO-8859-1"));
		return base;
	}

	// 压缩字符串
	public static String compressEncode(String res) throws IOException {
		String com = compress(res);
		if (null == com) {
			return null;
		}
		String base = Base64.encodeBase64String(com.getBytes("ISO-8859-1"));
		return base;
	}

	// 解压字符串
	public static String decompressEncode(String zipString) {
		byte[] bArr = Base64.decodeBase64(zipString);
		try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bArr);
				GZIPInputStream gZIPInputStream = new GZIPInputStream(byteArrayInputStream);
				BufferedReader bufferedReader = new BufferedReader(
						new InputStreamReader(gZIPInputStream, StandardCharsets.UTF_8))) {
			StringBuilder sb2 = new StringBuilder();
			String readLine;
			while ((readLine = bufferedReader.readLine()) != null) {
				sb2.append(readLine);
			}
			return sb2.toString();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void main(String[] args) throws IOException {

		String compressEncode = "H4sIAAAAAAAA/9Sd324cSRXGXwX1tR1V1TmnTpXvsiy7WmkTWThOENsI9XradovxTOQZZwkrX0YoWqG9YS8QD4DQCoFAewOIpyEOeQvU7ckoa58zyF1BmnOXmfS/+qbq9K++/qpdfVlX3eLJyeLR/LB5Vld7fqeujpqz9rz54Xx23J3U1V6/yez4vDlvJ5+2s8WH7XFzMV0+bqYX7WqHX83nZ8J/hcudujrrFotuPvtkUld7dVVXO3W16JbtYfPs3RNcNM9+fHp/uuyWF5PhqMHdczvD9wfLm99fro7xsDlrh6O++fs/rv76t1cvXv77n1+//u13r7/93XCe9an9sNXxUUL0x/6YgNG1NGzTziYH3bJdXd7BJ49+9Pjhkw/g4U8f4LvH+KBZHp0Om3z8kWcHyBGDwxQJfRo2PO5m3eL0/tGym89Wsqz2ffT8afv9b9YXfvXVb16/+MPVN3958+uvr7776j9//mb36uW3r37/r903f/zTqxcv13Kp13fRPFv93+H9xw/37//kgfvw56tLbydd81E3HXRaXUTor3TanZwu95vz5mxxrf7R+UW3aA+etu1k0HiQ/u1my77dn31ZV83Qtn6Xz362U1fN7V/rpDv7vJnud9da7fL1gU7bZtLN+h/aD5+72aT9ZV3tDf9eHC7aj6fzz5vpwfK86c/4aTfrj3ncTBftTl1Nm/VpwN1jhswx9FpO57OT9QV4uMchUwyUe8W+35KLRXvj4P3+XzTPn8672fLRxfnswXyy6rD/h3b6Ue1MgBhYaidQCIzpfbZz/amu9nDn7Yd1F3nbwvUv987+dbVHwh43JLi8oSTiPSjtME9uN6xEzNnbQflF83y/P667Hn0rjWG4lGXzi3b/dL6c6+L6TeL+b6nce5HKj5cqcEiqUsySUn6cUrgNSoXxSvmckypVclGSKhiWCgqkCpxAlSqCJBUYlgrHS+UoOrVUJSBJKjQsFY2WinMCvmuvIsNSxQKpfAhRlSoHSapoWCoeL1WCDKhJlQf2vCUVG5YqjZeKoz7+MmdJqWRYqTxeqZiJ9E4lFvVsWCk/ntU5hhDDHau6Hwnr2yHWeFpnIu/pjnXdW+Z1Px7YGTkFvV7Jld1bJnY/HtkZXfTq7EYp7t4ys/vx0M4AiGrNksu7twztvoDaw4h+NZLat8OLKcD2EF1WKpbzKchuzEhu3w6xCsAdIGSlZzmvOFeWwd0XkDs6HKxyuWMluWJZZndfAO/IKSqWjPPsZciyjO+hAN8Js2a1q2IFy/geCvA9Bs8KvjvPWbwZBsv4HgrwPWbQyzuK9B4s03sooHeOkRUg1UehZXoPBfSeIHn1ZhiT6DgEy/geSkx374LiZDkfg0gOwbLrHkps9wSaPeM8ZfkRoWXfPYzn9+QIs5js2NSzLAN8GA/wyYeYlQc6zlMUZzvBMsCH8QC/6Um98wRi/CNYBngoyMoE9qDXLCcyKVgOy0BRWsajjA6YHA/PEW+LNRLgt0OsgsBMcM7JUNqLpaRALBM8lEVmvHw37MXK4nQHLBM8FIVmyMl3Q0wuoVyzLBM8lMVmZE+510qcGYJlfoei2IxTPIcN/coyv0NRcGZDx8pORFKwzO9QFJ2JSa1YGcSZIVjmdyhKz0CWPYderCjODMEyv2NRfkYxszB5F0TIQsv+O5bEZzCjClnKKETL/juWxWfSncWyjO9YFJ9B1kqW9yK9o2V6x6L0DHh1XpizOIlGy/SOZfEZZX1AX99J5He0nJ/BAn4H8IpLipQTy8PQcn4GC/gdXXRKyaIcs+iSomV+x5IADcd4Z7Es8zsW8Dth1go8ZU5ygbfM71TG714p8JRZzvyRZYCnkgBNRqfMdtRhSJYBngoAniORejeMUXSzyDLAUwHAJ+CozHYoR5ngyTLBUwHBZ+9Isf4ok2wqk2WCpyL/PShL7Df0LMsOPI0n+OQINVOZMpG8xteyA08FCRofOMiP7nuxgshZZJngqSRBk5MSkUTKKC/aoZEEvxVzQypK0KSkQClcy3hbq5EAvxVaxYIAjQ+a8wdJeddMtMzvsSA/4whRuRdCUl43Ey3zeyzg95wCKvdCSMqrQaJlfo8F/J69Uxbt9GLJb+eJlvk9FiXgWVmHsqFnWeb3WMDvHEnvWFkk0mgZ32OBAR8zKkvuEVIO8ntnLON7LDDgY/CgECmkTOLEMFrG91hgwOvrDBGyk2PK0bIBH4tWsEZWMUsbhpYNeC4w4PU3X+hisWWA5wIDHiBoMWXITl7hxJYD8FySoHHglamh50TibIdHAvx2iFWSoGEOyt3Qc5QfGrJlgOcCgCfkdGexLAM8FwB8DE5zaDxTloehZYLnIoKHqDw09ExRtLPYMsFzAcFzJK9MdzwTiFDKIwl+Owp8EcFzlgkecsxO7lmWCZ6LCJ5BHoYbxLJM8KmI4CHIqRDIrKxxSpYJPhUQfKAc5ZoFKSZ5JXkaTfB9a0+OFu/8UYyT4YoPxT9ecKK1Bi/7E3Zn7WLZnD0dri24ALs+7Ab6gfd7BHvo6+qy+m8AAAD//2CjFn/jZAAA";
		String decompressEncode = decompressEncode(compressEncode);

		System.out.println("解压字符串长度：" + decompressEncode.length() + "  内容：" + decompressEncode);
	}
}
