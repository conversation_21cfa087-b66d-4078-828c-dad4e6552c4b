package dji.sampleV5.aircraft.mvvm.loadsir.target;

import android.app.Activity;
import android.view.View;
import android.view.ViewGroup;

import dji.sampleV5.aircraft.mvvm.loadsir.callback.Callback;
import dji.sampleV5.aircraft.mvvm.loadsir.callback.SuccessCallback;
import dji.sampleV5.aircraft.mvvm.loadsir.core.LoadLayout;


/**
 * Copyright (C), 2015-2024
 * FileName: ActivityTarget
 * Author: 80945
 * Date: 2024/10/14 14:47
 * Description:
 */
public class ActivityTarget implements ITarget {

    @Override
    public boolean equals(Object target) {
        return target instanceof Activity;
    }

    @Override
    public LoadLayout replaceView(Object target, Callback.OnReloadListener onReloadListener) {
        Activity activity = (Activity) target;
        ViewGroup contentParent = activity.findViewById(android.R.id.content);
        int childIndex = 0;
        View oldContent = contentParent.getChildAt(childIndex);
        contentParent.removeView(oldContent);

        ViewGroup.LayoutParams oldLayoutParams = oldContent.getLayoutParams();
        LoadLayout loadLayout = new LoadLayout(activity, onReloadListener);
        loadLayout.setupSuccessLayout(new SuccessCallback(oldContent, activity,
                onReloadListener));
        contentParent.addView(loadLayout, childIndex, oldLayoutParams);
        return loadLayout;
    }
}
