package dji.sampleV5.aircraft.mvvm.mission.waypoint.adapter

import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.base.appContext
import dji.sampleV5.aircraft.mvvm.mission.waypoint.WayPointMenuFragment
import dji.sampleV5.aircraft.mvvm.mission.waypoint.data.MissionMenuBean
import dji.v5.utils.common.AndUtil
import dji.v5.ux.core.ui.setting.taplayout.QTabView
import dji.v5.ux.core.ui.setting.taplayout.TabAdapter

class WayPointMenuAdapter(
    private var mCurrentFragmentManager: FragmentManager?,
    private val mAdapterMenus: MutableList<MissionMenuBean>,
    private val mAdapterFragments: MutableList<Fragment>,
): FragmentPagerAdapter(mCurrentFragmentManager!!), TabAdapter {
    init {
        requireNotNull(mCurrentFragmentManager) { "FragmentManager cannot be null" }
    }
    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val name = (getItem(position) as WayPointMenuFragment).fragmentFlag
        var fragment = mCurrentFragmentManager?.findFragmentByTag(name)
        if (fragment != null) {
            mCurrentFragmentManager?.beginTransaction()?.attach(fragment)?.commit()
        } else {
            fragment = getItem(position)
            mCurrentFragmentManager!!.beginTransaction().add(container.id, fragment, name).commit()
        }
        Handler(Looper.getMainLooper()).post { mCurrentFragmentManager?.executePendingTransactions() }
        return fragment
    }

    fun setMenuEnable(enable:Boolean) {
        mAdapterMenus.last().isEnable = enable
        notifyDataSetChanged()
    }

    private val fragmentMap = HashMap<String, Int>()
    fun getSelectIndex(str: String): Int {
        if (mAdapterFragments.size > 0 && str.isNotEmpty()) {
            if (fragmentMap.isEmpty()) {
                for (i in mAdapterFragments.indices) {
                    if (mAdapterFragments[i] is WayPointMenuFragment) {
                        val fragment = mAdapterFragments[i] as WayPointMenuFragment
                        fragmentMap[fragment.fragmentFlag] = i
                    }
                }
            }
            return fragmentMap[str] ?: -1
        }
        return -1
    }
    fun getSelectFlag(index: Int): String {
        return if (index in mAdapterFragments.indices) {
            (mAdapterFragments[index] as? WayPointMenuFragment)?.fragmentFlag ?: ""
        } else {
            ""
        }
    }

    override fun getCount(): Int {
        return mAdapterFragments.size
    }

    override fun getItemPosition(`object`: Any): Int {
        return  POSITION_UNCHANGED
    }

    override fun getItem(position: Int): Fragment {
        return mAdapterFragments[position]
    }

    override fun getBadge(position: Int): Int {
        // 如果有未读消息数，可以在这里返回
        return 0
    }

    override fun getIcon(position: Int): QTabView.TabIcon {
        val lastMenu = mAdapterMenus.last()
        val lastMenuIndex = mAdapterMenus.lastIndex
        val isLastTab = position == lastMenuIndex
        val menu = mAdapterMenus.getOrElse(position.coerceAtMost(lastMenuIndex)) { lastMenu }
        val normalIconRes = if (isLastTab && !lastMenu.isEnable) {
            R.drawable.mission_waypoint_enable_selector
        } else {
            menu.normalIcon
        }
        return QTabView.TabIcon.Builder()
            .setIcon(menu.selectIcon, normalIconRes)
            .setIconGravity(Gravity.CENTER)
            .setIconSize(
                AndUtil.dip2px(appContext, 50f),
                AndUtil.dip2px(appContext, 50f)
            )
            .setBackground(dji.v5.ux.R.drawable.uxsdk_selector_blue_oval_mask)
            .build()
    }

    override fun getTitle(position: Int): QTabView.TabTitle? {
        return null
    }

    override fun getBackground(position: Int): Int {
        // 返回Tab的背景色
        return R.color.transparent
    }

    fun destroy() {
        mCurrentFragmentManager = null
        fragmentMap.clear()
        mAdapterFragments.clear()
        mAdapterMenus.clear()
    }
}