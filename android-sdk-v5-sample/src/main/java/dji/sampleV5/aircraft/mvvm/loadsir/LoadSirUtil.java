package dji.sampleV5.aircraft.mvvm.loadsir;

import android.os.Looper;

import java.util.List;

import dji.sampleV5.aircraft.mvvm.loadsir.target.ITarget;

/**
 * Copyright (C), 2015-2024
 * FileName: LoadSirUtil
 * Author: 80945
 * Date: 2024/10/14 14:28
 * Description:
 */
public class LoadSirUtil {

    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }

    public static ITarget getTargetContext(Object target, List<ITarget> targetContextList) {
        for (ITarget targetContext : targetContextList) {
            if (targetContext.equals(target)) {
                return targetContext;
            }

        }
        throw new IllegalArgumentException("No TargetContext fit it");
    }
}
