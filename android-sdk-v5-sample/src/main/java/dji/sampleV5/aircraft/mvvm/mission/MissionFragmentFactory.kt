package dji.sampleV5.aircraft.mvvm.mission

import android.annotation.SuppressLint
import android.os.Build
import androidx.annotation.RequiresApi
import dji.sampleV5.aircraft.mvvm.ui.fragment.control.ControlFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.gimbal.GimbalFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.load.LoadFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.multi.MultiControlFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.task.InstancyTaskFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.task.NormalTaskFragment
import dji.v5.ux.core.ui.setting.ui.MenuFragment

/**
 * Description : 生成不同设置Fragment的工厂
 *
 * @author: Byte.Cai
 * date : 2022/11/17
 *
 *
 * Copyright (c) 2022, DJI All Rights Reserved.
 */
@SuppressLint("NewApi")
object MissionFragmentFactory {
    const val FRAGMENT_TAG_NORMAL_TASK = "NormalTaskFragment"
    const val FRAGMENT_TAG_INSTANCY_TASK = "InstancyTaskFragment"
    const val FRAGMENT_TAG_CONTROL = "ControlFragment"
    const val FRAGMENT_TAG_GIMBAL = "GimbalFragment"
    const val FRAGMENT_TAG_MULTI = "MultiControlFragment"
    const val FRAGMENT_TAG_LOAD = "LoadFragment"

    // 将直接存储Fragment实例改为存储Fragment类引用
    private val fragments = mapOf(
        FRAGMENT_TAG_NORMAL_TASK to NormalTaskFragment::class.java,
        FRAGMENT_TAG_INSTANCY_TASK to InstancyTaskFragment::class.java,
        FRAGMENT_TAG_CONTROL to ControlFragment::class.java,
        FRAGMENT_TAG_GIMBAL to GimbalFragment::class.java,
        FRAGMENT_TAG_MULTI to MultiControlFragment::class.java,
        FRAGMENT_TAG_LOAD to LoadFragment::class.java
    )

    @JvmStatic
    fun getMenuFragment(tag: String): MenuFragment {
        return fragments[tag]?.newInstance()
            ?: throw IllegalArgumentException("$tag Not support now.")
    }
}
