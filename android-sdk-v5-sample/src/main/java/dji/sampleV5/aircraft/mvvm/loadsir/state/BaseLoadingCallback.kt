package dji.sampleV5.aircraft.mvvm.loadsir.state

import android.content.Context
import android.view.View
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.loadsir.callback.Callback

/**
 * 作者　: he<PERSON><PERSON>jian
 * 时间　: 2020/12/14
 * 描述　:
 */
class BaseLoadingCallback: Callback() {

    override fun onCreateView(): Int {
        return R.layout.layout_loading
    }

    /**
     * 是否是 点击不可重试
     */
    override fun onReloadEvent(context: Context?, view: View?): <PERSON><PERSON><PERSON> {
        return true
    }
}