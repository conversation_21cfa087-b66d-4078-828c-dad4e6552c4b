package dji.sampleV5.aircraft.mvvm.mission.waypoint

import dji.sampleV5.aircraft.mvvm.ui.fragment.waypoint.wayhome.WayHomeFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.waypoint.wayline.WayLineFragment
import dji.sampleV5.aircraft.mvvm.ui.fragment.waypoint.waypoint.WayPointFragment
import dji.v5.ux.core.ui.setting.ui.MenuFragment

object WayPointFragmentFactory {
    const val FRAGMENT_TAG_HOME = "MissionHomeFragment"
    const val FRAGMENT_TAG_LINE = "MissionWaylineFragment"
    const val FRAGMENT_TAG_POINT = "MissionWaypointFragment"

    private val fragmentsMap = mapOf(
        FRAGMENT_TAG_HOME to WayHomeFragment(),
        FRAGMENT_TAG_LINE to WayLineFragment(),
        FRAGMENT_TAG_POINT to WayPointFragment()
    )

    @JvmStatic
    fun getMenuFragment(tag: String): MenuFragment {
        return fragmentsMap[tag] ?: throw IllegalArgumentException("$tag Not support now.")
    }
}