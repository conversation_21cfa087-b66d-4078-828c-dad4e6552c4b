package dji.sampleV5.aircraft.mvvm.mission.waypoint

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.mission.waypoint.WayPointFragmentFactory.getMenuFragment
import dji.v5.ux.core.ui.setting.ui.MenuFragment

class WayPointMenuFragment : Fragment(), FragmentManager.OnBackStackChangedListener {
    private var mProgressBar: ImageView? = null
    private var mFragmentTag: String = ""
    var fragmentFlag: String = ""
    private lateinit var fragmentManager: FragmentManager
    private lateinit var mFragmentRoot: View
    private lateinit var mMenuFragment: Fragment
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fragmentManager = childFragmentManager
        fragmentManager.addOnBackStackChangedListener(this)
        mFragmentTag = requireArguments().getString(ARG_PARAM, "")
        mMenuFragment = getMenuFragment(mFragmentTag)
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        if (!::mFragmentRoot.isInitialized) {
            mFragmentRoot =
                inflater.inflate(R.layout.mission_setting_menu_fragment_layout, container, false)
        }
        return mFragmentRoot
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mProgressBar = view.findViewById(R.id.setting_menu_progress_bar)
        if (!TextUtils.isEmpty(mFragmentTag)) {
            if (arguments == null) {
                return
            }
            mProgressBar?.visibility = View.VISIBLE
            val needLazyInflate =
                arguments?.getBoolean(NEED_LAZY_INFLATE, true)
            if (needLazyInflate == true) {
                view.postDelayed(mLazyInflateTask, 100)
            } else {
                inflateFunctionFragment()
            }
        }
    }

    private val mLazyInflateTask = Runnable { inflateFunctionFragment() }
    private fun inflateFunctionFragment() {
        if (mFragmentTag.isEmpty() || mProgressBar == null) return
        MenuFragment.addFragment(fragmentManager, mMenuFragment as MenuFragment?)
        mProgressBar?.visibility = View.GONE
        mFragmentTag = ""
    }

    override fun onDestroyView() {
        mProgressBar?.visibility = View.GONE
        mProgressBar = null
        mFragmentRoot.removeCallbacks(mLazyInflateTask)
        super.onDestroyView()
    }

    companion object {
        const val ARG_PARAM = "fragment_tag"
        private const val NEED_LAZY_INFLATE = "need_lazy_inflate"
        fun newInstance(tag: String, needLazyInitView: Boolean = true): WayPointMenuFragment {
            return WayPointMenuFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM, tag)
                    putBoolean(NEED_LAZY_INFLATE, needLazyInitView)
                }
                fragmentFlag = tag
            }
        }
    }
    override fun onBackStackChanged() {}
}