package dji.sampleV5.aircraft.mvvm.loadsir.callback;

import android.content.Context;
import android.view.View;

/**
 * Copyright (C), 2015-2024
 * FileName: SuccessCallback
 * Author: 80945
 * Date: 2024/10/14 14:27
 * Description:
 */
public class SuccessCallback extends Callback {
    public SuccessCallback(View view, Context context, OnReloadListener onReloadListener) {
        super(view, context, onReloadListener);
    }

    @Override
    protected int onCreateView() {
        return 0;
    }

    /**
     * @deprecated Use {@link #showWithCallback(boolean successVisible)} instead.
     */
    public void hide() {
        obtainRootView().setVisibility(View.INVISIBLE);
    }

    public void show() {
        obtainRootView().setVisibility(View.VISIBLE);
    }

    public void showWithCallback(boolean successVisible) {
        obtainRootView().setVisibility(successVisible ? View.VISIBLE : View.INVISIBLE);
    }

}
