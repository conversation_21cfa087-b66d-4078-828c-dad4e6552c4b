package dji.sampleV5.aircraft.mvvm.loadsir.target;

import android.view.View;
import android.view.ViewGroup;

import dji.sampleV5.aircraft.mvvm.loadsir.callback.Callback;
import dji.sampleV5.aircraft.mvvm.loadsir.callback.SuccessCallback;
import dji.sampleV5.aircraft.mvvm.loadsir.core.LoadLayout;

/**
 * Copyright (C), 2015-2024
 * FileName: ViewTarget
 * Author: 80945
 * Date: 2024/10/14 14:48
 * Description:
 */
public class ViewTarget implements ITarget {

    @Override
    public boolean equals(Object target) {
        return target instanceof View;
    }

    @Override
    public LoadLayout replaceView(Object target, Callback.OnReloadListener onReloadListener) {
        View oldContent = (View) target;
        ViewGroup contentParent = (ViewGroup) (oldContent.getParent());
        int childIndex = 0;
        int childCount = contentParent == null ? 0 : contentParent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            if (contentParent.getChildAt(i) == oldContent) {
                childIndex = i;
                break;
            }
        }
        if (contentParent != null) {
            contentParent.removeView(oldContent);
        }
        ViewGroup.LayoutParams oldLayoutParams = oldContent.getLayoutParams();
        LoadLayout loadLayout = new LoadLayout(oldContent.getContext(), onReloadListener);
        loadLayout.setupSuccessLayout(new SuccessCallback(oldContent, oldContent.getContext(),onReloadListener));
        if (contentParent != null) {
            contentParent.addView(loadLayout, childIndex, oldLayoutParams);
        }
        return loadLayout;
    }
}