package dji.sampleV5.aircraft.mvvm.mission

import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.core.view.GravityCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import dji.sampleV5.aircraft.R
import dji.sampleV5.aircraft.mvvm.mission.MissionFragmentFactory.getMenuFragment
import dji.sampleV5.aircraft.mvvm.widget.CustomDrawerLayout
import dji.v5.ux.core.ui.setting.ui.MenuFragment

/**
 * @projectName android-sdk-v5-as
 * @FileName MissionMentFragment
 * @data 2024/2/1 11:06
 * <AUTHOR>
 * @description 任务相关界面创建的入口
 **/
@RequiresApi(Build.VERSION_CODES.N)
class MissionMenuFragment: Fragment(), FragmentManager.OnBackStackChangedListener {
    private var mTitleView: TextView? = null
    private var mCloseBtn: ImageView? = null
    private var mProgressBar: ImageView? = null
    private var mBackBtn: ImageView? = null


    private var mFragmentTag: String? = null
    private lateinit var fragmentManager: FragmentManager
    private var mFragmentRoot: View? = null
    private lateinit var menuFragment: MenuFragment
    var fragmentFlag: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        fragmentManager = childFragmentManager
        fragmentManager.addOnBackStackChangedListener(this)
        arguments?.getString(ARG_PARAM)?.let { tag ->
            menuFragment = getMenuFragment(tag)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        mFragmentRoot = inflater.inflate(R.layout.optration_center_menu_fragment_layout, container, false)
        // 将menuFragment添加到MissionMenuFragment的视图层次结构中
        fragmentManager.beginTransaction()
            .replace(R.id.fragment_content, menuFragment)
            .commit()
        return mFragmentRoot!!
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mTitleView = view.findViewById(R.id.setting_menu_header_title)
        mCloseBtn = view.findViewById(R.id.setting_menu_header_close)
        mBackBtn = view.findViewById(R.id.setting_menu_header_back)
        mProgressBar = view.findViewById(R.id.setting_menu_progress_bar)
        if (!TextUtils.isEmpty(mFragmentTag)) {
            if (arguments == null) {
                return
            }
            mProgressBar?.visibility = View.VISIBLE
            val needLazyInflate = arguments?.getBoolean(NEED_LAZY_INFLATE, true)
            if (needLazyInflate == true) {
                mFragmentRoot?.post(mLazyInflateTask)
            } else {
                inflateFunctionFragment()
            }
        } else {
            updateTitle()
        }
        mCloseBtn?.setOnClickListener {
            // 获取Activity中的DrawerLayout实例
            val drawerLayout = activity?.findViewById<CustomDrawerLayout>(R.id.drawer_layout)
            // 检查drawerLayout是否为null
            drawerLayout?.closeDrawer(GravityCompat.END)
        }

        mBackBtn?.setOnClickListener { v: View? ->
            if (fragmentManager.backStackEntryCount > 1) {
                val menuFragment: MenuFragment = getLastMenuFragment()!!
                if (menuFragment != null && !menuFragment.onBackPressed()) {
                    popBackFragmentStack()
                }
            } else {
                popBackFragmentStack()
            }
        }
    }

    private var mLazyInflateTask = Runnable { inflateFunctionFragment() }

    private fun inflateFunctionFragment() {
        if (TextUtils.isEmpty(mFragmentTag) || mProgressBar == null) return
        val menuFragment = getMenuFragment(mFragmentTag!!)
        MenuFragment.addFragment(fragmentManager, menuFragment)
        mProgressBar?.visibility = View.GONE
        mFragmentTag = null
    }

    private fun popBackFragmentStack() {
        if (fragmentManager.backStackEntryCount > 1) {
            fragmentManager.popBackStackImmediate()
        }
    }

    private fun getLastMenuFragment(): MenuFragment? {
        if (fragmentManager.fragments.size > 0) {
            val fragment = fragmentManager.fragments[fragmentManager.fragments.size - 1]
            return if (fragment is MenuFragment) {
                fragment
            } else {
                null
            }
        } else {
            return null
        }
    }

    override fun onDestroyView() {
        mProgressBar?.visibility = View.GONE
        mProgressBar = null
        mCloseBtn = null
        mBackBtn = null
        if (mFragmentRoot != null) {
            mFragmentRoot!!.removeCallbacks(mLazyInflateTask)
        }
        mFragmentRoot = null
        mTitleView = null
        super.onDestroyView()
    }
    override fun onBackStackChanged() {
        updateTitle()
    }

    private fun updateTitle() {
        val entryCount = fragmentManager.backStackEntryCount
        if (mBackBtn != null) {
            mBackBtn?.visibility = if (entryCount > 0) View.VISIBLE else View.INVISIBLE
        }
        if (entryCount > 0){
            val entry = fragmentManager.getBackStackEntryAt(entryCount - 1)
            if (mTitleView != null) {
                mTitleView?.text = entry.name
            }
        }
    }

    companion object {
        const val ARG_PARAM = "fragment_tag"
        private const val NEED_LAZY_INFLATE = "need_lazy_inflate"
        fun newInstance(tag: String, needLazyInitView: Boolean = true): MissionMenuFragment {
            return MissionMenuFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM, tag)
                    putBoolean(NEED_LAZY_INFLATE, needLazyInitView)
                }
                fragmentFlag = tag
            }
        }
    }
}