package dji.sampleV5.aircraft.mvvm.loadsir.target;


import dji.sampleV5.aircraft.mvvm.loadsir.callback.Callback;
import dji.sampleV5.aircraft.mvvm.loadsir.core.LoadLayout;

/**
 * Copyright (C), 2015-2024
 * FileName: ITarget
 * Author: 80945
 * Date: 2024/10/14 14:28
 * Description:
 */
public interface ITarget {
    /**
     *
     * @param target
     * @return
     * v1.3.8
     */
    boolean equals(Object target);
    /**
     * 1.removeView 2.确定LP 3.addView
     * @param target
     * @param onReloadListener
     * @return
     * v1.3.8
     */
    LoadLayout replaceView(Object target, Callback.OnReloadListener onReloadListener);
}
