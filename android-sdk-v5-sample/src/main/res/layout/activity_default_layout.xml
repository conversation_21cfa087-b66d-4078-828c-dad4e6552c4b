<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright (c) 2018-2020 DJI
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy
  ~ of this software and associated documentation files (the "Software"), to deal
  ~ in the Software without restriction, including without limitation the rights
  ~ to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  ~ copies of the Software, and to permit persons to whom the Software is
  ~ furnished to do so, subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  ~ FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  ~ AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  ~ LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  ~ OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  ~ SOFTWARE.
  ~
  -->
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/drawer_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/fly_page_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:id="@+id/fpv_holder"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/iv_fpv_bg"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/shape_background_grey" />

                <RelativeLayout
                    android:id="@+id/fpv_wrapper"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignParentBottom="true"
                    android:layout_gravity="bottom|start">

                    <dji.v5.ux.core.widget.fpv.FPVWidget
                        android:id="@+id/widget_primary_fpv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:uxsdk_sourceCameraNameVisibility="false"
                        app:uxsdk_sourceCameraSideVisibility="false">

                        <dji.v5.ux.cameracore.widget.fpvinteraction.FPVInteractionWidget
                            android:id="@+id/widget_fpv_interaction"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent" />


                        <dji.sampleV5.aircraft.mvvm.widget.view.ThermalOverlayView
                            android:id="@+id/thermal_overlay"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="invisible"
                            tools:visibility="visible" />

                    </dji.v5.ux.core.widget.fpv.FPVWidget>

                    <dji.sampleV5.aircraft.mvvm.widget.view.OverLayerTopView
                        android:id="@+id/over_layer_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:visibility="invisible"
                        tools:visibility="visible" />
                </RelativeLayout>

                <!-- 地图会插入到此容器内部 -->
                <RelativeLayout
                    android:id="@+id/map_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_gravity="bottom|start" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/panel_normal_live"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <dji.v5.ux.core.widget.fpv.FPVWidget
                    android:id="@+id/widget_secondary_fpv"
                    android:layout_width="@dimen/uxsdk_mini_map_width"
                    android:layout_height="@dimen/uxsdk_mini_map_height"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    app:uxsdk_interactionEnabled="false"
                    app:uxsdk_sourceCameraNameVisibility="false"
                    app:uxsdk_sourceCameraSideVisibility="false" />

                <RelativeLayout
                    android:id="@+id/root_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <dji.sampleV5.aircraft.view.RemindPopupWindow
                        android:id="@+id/popup_window_from_left"
                        android:layout_width="@dimen/space_200"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/space_45"
                        android:layout_marginTop="@dimen/space_80"
                        android:orientation="vertical" />

                    <dji.v5.ux.flight.takeoff.TakeOffWidget
                        android:id="@+id/widget_take_off"
                        android:layout_width="@dimen/space_50"
                        android:layout_height="@dimen/space_50"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/uxsdk_50_dp"
                        android:padding="3dp"
                        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />


                    <dji.v5.ux.flight.returnhome.ReturnHomeWidget
                        android:id="@+id/widget_return_to_home"
                        android:layout_width="@dimen/space_50"
                        android:layout_height="@dimen/space_50"
                        android:layout_below="@+id/widget_take_off"
                        android:layout_marginStart="@dimen/uxsdk_50_dp"
                        android:layout_marginTop="5dp"
                        android:padding="3dp"
                        tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck" />

                    <!--飞行任务面板-->
                    <include
                        android:id="@+id/view_mission_panel"
                        layout="@layout/layout_mission_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="@dimen/space_25"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:id="@+id/bottom_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_marginStart="@dimen/space_200"
                        android:orientation="horizontal">

                        <dji.v5.ux.core.widget.altitude.AGLAltitudeWidget
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                        <dji.v5.ux.core.widget.distancehome.DistanceHomeWidget
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                        <dji.v5.ux.core.widget.horizontalvelocity.HorizontalVelocityWidget
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                        <dji.v5.ux.core.widget.verticalvelocity.VerticalVelocityWidget
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />


                    </LinearLayout>

                    <!--推流面板-->
                    <dji.sampleV5.aircraft.view.LiveControlView
                        android:id="@+id/view_rtmp_panel"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_30"
                        android:layout_above="@+id/bottom_status"
                        android:layout_marginStart="@dimen/uxsdk_195_dp"
                        android:background="@drawable/ic_black_radius_rectangle"
                        android:padding="@dimen/gap_tiny" />


                </RelativeLayout>

            </RelativeLayout>

            <!--点击地图、视频后的切换效果View-->
            <dji.sampleV5.aircraft.view.FullScreenRippleView
                android:id="@+id/iv_animation"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_alignParentBottom="true" />

            <!--用于 framgment 的显示-->
            <FrameLayout
                android:id="@+id/container_navigation"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <RelativeLayout
                android:id="@+id/normal"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_co2_value"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/uxsdk_90_dp"
                    android:layout_marginEnd="@dimen/uxsdk_100_dp"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/uxsdk_10_dp"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    tools:text="999" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_humidity_value"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_marginTop="@dimen/uxsdk_90_dp"
                    android:layout_marginEnd="@dimen/uxsdk_10_dp"
                    android:layout_toStartOf="@+id/tv_co2_value"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/uxsdk_10_dp"
                    android:textColor="@color/green"
                    android:textSize="12sp"
                    tools:text="999" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_pressure_value"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/tv_co2_value"
                    android:layout_alignStart="@+id/tv_co2_value"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:layout_marginEnd="@dimen/uxsdk_10_dp"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/uxsdk_10_dp"
                    android:textColor="@color/green"
                    android:textSize="12sp"
                    tools:text="999" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_temp_value"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/tv_humidity_value"
                    android:layout_alignStart="@+id/tv_humidity_value"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:layout_marginEnd="@dimen/uxsdk_10_dp"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/uxsdk_10_dp"
                    android:textColor="@color/green"
                    android:textSize="12sp"
                    tools:text="999" />

                <dji.v5.ux.cameracore.widget.cameracontrols.CameraControlsWidget
                    android:id="@+id/widget_camera_controls"
                    android:layout_width="@dimen/space_50"
                    android:layout_height="@dimen/space_180"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true" />

                <dji.v5.ux.core.panel.topbar.TopBarPanelWidget
                    android:id="@+id/panel_top_bar"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_alignParentTop="true"
                    android:background="@color/uxsdk_black"
                    app:uxsdk_itemsMarginRight="@dimen/uxsdk_spacing_normal"
                    app:uxsdk_itemsSpacing="14dp" />

                <dji.v5.ux.core.widget.remainingflighttime.RemainingFlightTimeWidget
                    android:id="@+id/widget_remaining_flight_time"
                    android:layout_width="match_parent"
                    android:layout_height="15dp"
                    android:layout_below="@+id/panel_top_bar" />


                <dji.v5.ux.training.simulatorcontrol.SimulatorControlWidget
                    android:id="@+id/widget_simulator_control"
                    android:layout_width="@dimen/uxsdk_330_dp"
                    android:layout_height="0dp"
                    android:layout_below="@+id/panel_top_bar"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/camera_mode"
                    android:layout_width="@dimen/uxsdk_65_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/space_60"
                    android:background="@drawable/ext_bg_gray_stroke_and_white_solid_rectangle"
                    android:gravity="center"
                    android:text="镜头/红外"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/mission_progress"
                    android:layout_width="@dimen/uxsdk_65_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/camera_mode"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/space_10"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:text="0/0"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/mission_name"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/camera_mode"
                    android:layout_marginStart="@dimen/uxsdk_80_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:background="@drawable/mission_progress_bg"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/uxsdk_10_dp"
                    android:text="航点名：NA"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/iv_navi_to_aircraft"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_marginStart="@dimen/uxsdk_10_dp"
                    android:layout_marginTop="@dimen/uxsdk_60_dp"
                    android:layout_toEndOf="@+id/camera_mode"
                    android:background="@drawable/selector_bg_white_6"
                    android:src="@drawable/selector_navi_to_aircraft"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_realtime_upload"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:layout_below="@+id/mission_progress"
                    android:layout_marginStart="@dimen/uxsdk_45_dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:src="@drawable/selector_realtime_upload"
                    android:background="@drawable/uxsdk_bg_white_radius"/>


                <!--任务检查单开关-->
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_check_list"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/mission_progress"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:background="@drawable/selector_bg_white_6"
                    android:padding="@dimen/uxsdk_5_dp"
                    android:src="@drawable/selector_check_list"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_spotlight"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/btn_check_list"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:background="@drawable/uxsdk_bg_white_radius"
                    android:padding="@dimen/uxsdk_6_dp"
                    android:src="@drawable/selector_auto_sensing_control"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_auto_sensing"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/btn_spotlight"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:background="@drawable/uxsdk_bg_white_radius"
                    android:padding="@dimen/uxsdk_5_dp"
                    android:src="@drawable/selector_spot_light_control"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btn_search_light"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/btn_auto_sensing"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:background="@drawable/uxsdk_bg_white_radius"
                    android:padding="@dimen/uxsdk_5_dp"
                    android:rotation="180"
                    android:src="@drawable/selector_search_light_switch"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <!--探照灯组件-->
                <dji.sampleV5.aircraft.mvvm.widget.searchLight.SearchlightControlView
                    android:id="@+id/searchlight_control_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@+id/btn_speaker"
                    android:layout_marginStart="@dimen/uxsdk_20_dp"
                    android:layout_toEndOf="@+id/btn_search_light"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <dji.sampleV5.aircraft.mvvm.widget.view.AnimatedButtonView
                    android:id="@+id/btn_speaker"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_below="@+id/btn_search_light"
                    android:layout_marginStart="@dimen/uxsdk_5_dp"
                    android:layout_marginTop="@dimen/uxsdk_10_dp"
                    android:padding="@dimen/uxsdk_5_dp"
                    android:visibility="gone"
                    app:animationIcons="@array/volume_animation_icons"
                    app:buttonBackground="@drawable/uxsdk_bg_white_radius"
                    app:defaultIcon="@drawable/selector_speaker_switch"
                    tools:visibility="visible" />

                <dji.sampleV5.aircraft.mvvm.widget.speaker.SpeakerWidget
                    android:id="@+id/speaker_widget"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@+id/btn_speaker"
                    android:layout_marginStart="@dimen/uxsdk_10_dp"
                    android:layout_toEndOf="@+id/searchlight_control_view"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <!--虚拟摇杆的开关-->
                <RelativeLayout
                    android:id="@+id/rl_vs_tips"
                    android:layout_width="160dp"
                    android:layout_height="@dimen/space_80"
                    android:layout_marginStart="@dimen/space_40"
                    android:layout_marginTop="@dimen/space_45"
                    android:layout_toRightOf="@+id/camera_mode"
                    android:background="@drawable/ic_black_radius_rectangle"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/vs_tips"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_margin="@dimen/space_6"
                        android:text="当前远程控制模式已开启，遥控器将无法控制无人机"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_12" />

                    <RelativeLayout
                        android:id="@+id/close_vs"
                        android:layout_width="@dimen/uxsdk_115_dp"
                        android:layout_height="25dp"
                        android:layout_below="@+id/vs_tips"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/selector_close_vs">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="关闭远程控制"
                            android:textColor="@color/white"
                            android:textSize="12sp" />
                    </RelativeLayout>

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/space_60"
                    android:background="@drawable/ext_bg_green_rectangle"
                    android:padding="10dp"
                    android:text="变焦"
                    android:textColor="@color/green" />

                <dji.sampleV5.aircraft.mvvm.widget.view.zoom.ManualZoomWidgetView
                    android:id="@+id/manual_zoom_widget"
                    android:layout_width="@dimen/dp_40"
                    android:layout_height="@dimen/dp_200"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dp_190"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <dji.sampleV5.aircraft.view.CameraH20TZoomPanel
                    android:id="@+id/zoom_panel"
                    android:layout_width="@dimen/space_60"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/space_110"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <dji.sampleV5.aircraft.view.CameraThermalZoomPanel
                    android:id="@+id/thermal_zoom_panel"
                    android:layout_width="@dimen/space_60"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/space_110"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <dji.v5.ux.core.panel.systemstatus.SystemStatusListPanelWidget
                    android:id="@+id/widget_panel_system_status_list"
                    android:layout_width="430dp"
                    android:layout_height="320dp"
                    android:layout_below="@+id/panel_top_bar"
                    android:background="@color/uxsdk_black_70_percent"
                    android:divider="@color/uxsdk_light_gray_900"
                    android:dividerHeight="0.5dp"
                    android:visibility="invisible"
                    tools:visibility="visible"
                    app:uxsdk_titleBarBackgroundColor="@color/uxsdk_black"
                    app:uxsdk_titleTextSize="@dimen/space_15" />

                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/btn_map_type"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_alignTop="@+id/widget_thermal_palette"
                    android:layout_marginEnd="@dimen/dp_10"
                    android:layout_toStartOf="@+id/widget_thermal_palette"
                    android:background="@drawable/selector_mission_map_control"
                    android:padding="@dimen/dp_5"
                    android:src="@drawable/selector_switch_map_type" />

                <dji.sampleV5.aircraft.mvvm.widget.camera.CameraThermalPaletteWidget
                    android:id="@+id/widget_thermal_palette"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/space_35"
                    android:layout_alignTop="@+id/widget_focus_exposure_switch"
                    android:layout_alignBottom="@+id/widget_focus_exposure_switch"
                    android:layout_marginEnd="@dimen/uxsdk_5_dp"
                    android:layout_toStartOf="@+id/constraint_camera" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraint_camera"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignTop="@+id/widget_focus_exposure_switch"
                    android:layout_marginEnd="5dp"
                    android:layout_toStartOf="@+id/widget_focus_mode"
                    android:minWidth="@dimen/uxsdk_150_dp">

                    <!--相机曝光、EV、快门、ISO控件-->
                    <dji.v5.ux.visualcamera.CameraVisiblePanelWidget
                        android:id="@+id/panel_visual_camera"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/uxsdk_camera_config_height"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <dji.sampleV5.aircraft.mvvm.widget.pickview.TextPickerView
                        android:id="@+id/pv_camera_iso"
                        android:layout_width="@dimen/uxsdk_50_dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/uxsdk_10_dp"
                        android:background="@drawable/bg_public_black_radius_6"
                        android:scaleY="0.9"
                        android:visibility="gone"
                        app:alpha="0.6"
                        app:dividerVisible="false"
                        app:isLoop="false"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/panel_visual_camera"
                        app:scaleX="0.9"
                        app:selectedIsBold="true"
                        app:selectedTextColor="@color/uxsdk_yellow_in_light"
                        app:unSelectedTextColor="@color/white"
                        app:visibleCount="5"
                        tools:visibility="visible" />

                    <dji.sampleV5.aircraft.mvvm.widget.pickview.TextPickerView
                        android:id="@+id/pv_camera_shutter"
                        android:layout_width="@dimen/uxsdk_50_dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/uxsdk_10_dp"
                        android:background="@drawable/bg_public_black_radius_6"
                        android:scaleY="0.9"
                        android:visibility="gone"
                        app:alpha="0.6"
                        app:dividerVisible="false"
                        app:isLoop="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/panel_visual_camera"
                        app:scaleX="0.9"
                        app:selectedIsBold="true"
                        app:selectedTextColor="@color/uxsdk_yellow_in_light"
                        app:unSelectedTextColor="@color/white"
                        app:visibleCount="5"
                        tools:visibility="visible"/>

                    <dji.sampleV5.aircraft.mvvm.widget.pickview.TextPickerView
                        android:id="@+id/pv_camera_ev"
                        android:layout_width="@dimen/uxsdk_50_dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/uxsdk_10_dp"
                        android:background="@drawable/bg_public_black_radius_6"
                        android:scaleY="0.9"
                        android:visibility="gone"
                        app:alpha="0.6"
                        app:dividerVisible="false"
                        app:isLoop="false"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_weight="1"
                        app:layout_constraintTop_toBottomOf="@+id/panel_visual_camera"
                        app:scaleX="0.9"
                        app:selectedIsBold="true"
                        app:selectedTextColor="@color/uxsdk_yellow_in_light"
                        app:unSelectedTextColor="@color/white"
                        app:visibleCount="5"
                        tools:visibility="visible"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <dji.v5.ux.cameracore.widget.focusmode.FocusModeWidget
                    android:id="@+id/widget_focus_mode"
                    android:layout_width="@dimen/uxsdk_camera_bar_height"
                    android:layout_height="@dimen/uxsdk_camera_bar_height"
                    android:layout_marginTop="@dimen/space_50"
                    android:layout_marginEnd="@dimen/space_15"
                    android:layout_toStartOf="@+id/widget_focus_exposure_switch"
                    android:padding="@dimen/space_2"
                    tools:ignore="TouchTargetSizeCheck" />

                <dji.v5.ux.cameracore.widget.focusexposureswitch.FocusExposureSwitchWidget
                    android:id="@+id/widget_focus_exposure_switch"
                    android:layout_width="@dimen/uxsdk_camera_bar_height"
                    android:layout_height="@dimen/uxsdk_camera_bar_height"
                    android:layout_alignTop="@+id/iv_manual"
                    android:layout_alignBottom="@+id/iv_manual"
                    android:layout_marginEnd="5dp"
                    android:layout_toStartOf="@+id/widget_auto_exposure_lock"
                    android:padding="@dimen/uxsdk_camera_bar_padding"
                    tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck" />

                <dji.v5.ux.cameracore.widget.autoexposurelock.AutoExposureLockWidget
                    android:id="@+id/widget_auto_exposure_lock"
                    android:layout_width="@dimen/uxsdk_camera_bar_height"
                    android:layout_height="@dimen/uxsdk_camera_bar_height"
                    android:layout_alignTop="@+id/iv_manual"
                    android:layout_marginEnd="8dp"
                    android:layout_toStartOf="@+id/iv_manual"
                    android:padding="@dimen/uxsdk_camera_bar_padding"
                    tools:ignore="TouchTargetSizeCheck" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_manual"
                    android:layout_width="@dimen/uxsdk_35_dp"
                    android:layout_height="@dimen/uxsdk_35_dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="@dimen/space_50"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/uxsdk_background_black_rectangle"
                    android:padding="5dp"
                    android:src="@drawable/selector_camera_exposure_auto" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/downloadLL"
                android:layout_width="@dimen/uxsdk_300_dp"
                android:layout_height="@dimen/uxsdk_200_dp"
                android:layout_alignParentStart="true"
                android:layout_marginStart="@dimen/uxsdk_100_dp"
                android:layout_marginTop="@dimen/uxsdk_150_dp"
                android:background="@drawable/bg_public_black_radius_6"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/btn_warning"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/space_40"
                    android:text="@string/downloading_warning"
                    android:textColor="@color/red"
                    android:textSize="@dimen/text_size_16" />


                <TextView
                    android:id="@+id/download_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/btn_warning"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/space_20"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_14" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/download_text"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/space_20"
                    android:orientation="horizontal">

                    <ProgressBar
                        android:id="@+id/downloadProgressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="200dp"
                        android:layout_height="3dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_30"
                        android:max="100"
                        android:progressDrawable="@drawable/progress_shape"
                        android:textSize="@dimen/text_size_12" />

                    <TextView
                        android:id="@+id/down_percent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/space_10"
                        android:text="0"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_12" />
                </LinearLayout>
            </RelativeLayout>

        </RelativeLayout>

        <dji.v5.ux.core.widget.setting.SettingPanelWidget
            android:id="@+id/manual_right_nav_setting"
            android:layout_width="@dimen/uxsdk_510_dp"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:background="@color/uxsdk_fpv_popover_content_background_color"
            android:descendantFocusability="afterDescendants">

        </dji.v5.ux.core.widget.setting.SettingPanelWidget>

        <!--喊话器功能组件-->
        <dji.sampleV5.aircraft.mvvm.widget.speaker.SpeakerMainComponent
            android:id="@+id/speaker_main_component"
            android:layout_width="@dimen/uxsdk_300_dp"
            android:layout_height="match_parent"
            android:layout_gravity="start"
            android:layout_marginTop="@dimen/uxsdk_30_dp" />

    </androidx.drawerlayout.widget.DrawerLayout>
</layout>